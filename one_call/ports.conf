# One call Port Configuration
# This file defines the ports used by the application components
# You can modify these values to avoid conflicts with other applications

# Backend API Server Port (used by FastAPI/uvicorn)
API_PORT=1801

# WebSocket Server Port (used by the WebSocket server)
WEBSOCKET_PORT=1802

# Frontend Development Server Port (used by React development server)
FRONTEND_PORT=1800

# Backend Proxy Port (referenced in package.json proxy setting)
BACKEND_PROXY_PORT=1803