# AWS Transcribe Integration Setup Guide

## Overview

This document provides setup instructions for the AWS Transcribe real-time streaming voice-to-text conversion service, implemented as a production-ready alternative to AssemblyAI.

## Features

- **Real-time Transcription**: Streaming voice-to-text conversion using AWS Transcribe
- **Configurable Settings**: Comprehensive configuration options for AWS Transcribe
- **Fallback Support**: Automatic fallback to AssemblyAI if AWS Transcribe fails
- **Error Handling**: Robust error handling and retry mechanisms
- **Production Ready**: Follows the same patterns as existing transcription services

## Prerequisites

1. **AWS Account**: You need an AWS account with Transcribe service access
2. **AWS Credentials**: Access Key ID and Secret Access Key with Transcribe permissions
3. **Python Dependencies**: boto3 and amazon-transcribe packages

## Installation

### 1. Install Dependencies

The required dependencies are already added to `requirements.txt`:

```bash
pip install boto3~=1.34.0
pip install amazon-transcribe~=0.6.2
```

### 2. AWS Credentials Setup

You have several options to configure AWS credentials:

#### Option A: Environment Variables (Recommended for Production)

Set the following environment variables in your `.env` file:

```bash
# AWS Transcribe Configuration
AWS_TRANSCRIBE_REGION=us-east-1
AWS_TRANSCRIBE_ACCESS_KEY_ID=your_access_key_here
AWS_TRANSCRIBE_SECRET_ACCESS_KEY=your_secret_key_here
AWS_TRANSCRIBE_LANGUAGE_CODE=en-US
```

#### Option B: AWS Credentials File

Create `~/.aws/credentials`:

```ini
[default]
aws_access_key_id = your_access_key_here
aws_secret_access_key = your_secret_key_here
```

And `~/.aws/config`:

```ini
[default]
region = us-east-1
```

### 3. Service Configuration

Update your environment configuration to use AWS Transcribe:

```bash
# Set AWS Transcribe as primary service
TRANSCRIPTION_SERVICE=aws_transcribe

# Enable fallback to AssemblyAI
TRANSCRIPTION_FALLBACK_ENABLED=true
```

## Configuration Options

All AWS Transcribe settings can be configured via environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `AWS_TRANSCRIBE_REGION` | `us-east-1` | AWS region for Transcribe service |
| `AWS_TRANSCRIBE_ACCESS_KEY_ID` | `None` | AWS Access Key ID |
| `AWS_TRANSCRIBE_SECRET_ACCESS_KEY` | `None` | AWS Secret Access Key |
| `AWS_TRANSCRIBE_LANGUAGE_CODE` | `en-US` | Language code for transcription |
| `AWS_TRANSCRIBE_SAMPLE_RATE` | `16000` | Audio sample rate in Hz |
| `AWS_TRANSCRIBE_MEDIA_ENCODING` | `pcm` | Audio encoding format |
| `AWS_TRANSCRIBE_VOCABULARY_NAME` | `None` | Custom vocabulary name (optional) |
| `AWS_TRANSCRIBE_VOCABULARY_FILTER_NAME` | `None` | Vocabulary filter name (optional) |
| `AWS_TRANSCRIBE_ENABLE_PARTIAL_RESULTS_STABILIZATION` | `true` | Enable partial results stabilization |
| `AWS_TRANSCRIBE_PARTIAL_RESULTS_STABILITY` | `medium` | Partial results stability level |

## Usage

### Basic Usage

The AWS Transcribe service integrates seamlessly with the existing transcription factory:

```python
from tutor.modules.audio.transcription_factory import TranscriptionFactory

# Create AWS Transcribe service
transcription_service = TranscriptionFactory.create_transcription_service(
    user=user,
    logger_instance=logger,
    service_type="aws_transcribe"
)

# Connect and start transcription
transcription_service.connect()
transcription_service.start()
```

### With Fallback

```python
# Create service with automatic fallback
transcription_service = TranscriptionFactory.create_transcription_service(
    user=user,
    logger_instance=logger,
    service_type="aws_transcribe",
    enable_fallback=True  # Falls back to AssemblyAI if AWS fails
)
```

### Service Validation

```python
# Check if AWS Transcribe is properly configured
is_valid, error_msg = TranscriptionFactory.validate_service_config("aws_transcribe")
if not is_valid:
    print(f"Configuration error: {error_msg}")

# Get available services
available = TranscriptionFactory.get_available_services()
print(f"Available services: {available}")
```

## Testing

Run the test script to verify your setup:

```bash
cd one_call
python test_aws_transcribe.py
```

Expected output for properly configured setup:
```
✓ AWS Transcribe service created successfully
✓ AWS Transcribe connection successful
✓ Audio chunk added successfully
✓ AWS Transcribe service closed successfully
```

## Troubleshooting

### Common Issues

1. **Credentials Not Configured**
   ```
   Error: AWS Transcribe credentials not configured
   ```
   Solution: Set `AWS_TRANSCRIBE_ACCESS_KEY_ID` and `AWS_TRANSCRIBE_SECRET_ACCESS_KEY`

2. **Invalid Region**
   ```
   Error: Could not connect to the endpoint URL
   ```
   Solution: Verify `AWS_TRANSCRIBE_REGION` is correct

3. **Permission Denied**
   ```
   Error: User is not authorized to perform: transcribe:StartStreamTranscription
   ```
   Solution: Ensure your AWS credentials have Transcribe permissions

4. **Import Errors**
   ```
   Error: AWS Transcribe libraries not installed
   ```
   Solution: Install boto3 and amazon-transcribe packages

### Required AWS Permissions

Your AWS user/role needs these permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "transcribe:StartStreamTranscription",
                "transcribe:ListTranscriptionJobs"
            ],
            "Resource": "*"
        }
    ]
}
```

## Architecture

The AWS Transcribe integration follows the same architecture as existing services:

```
TranscriptionFactory
├── AssemblyAI (existing)
├── Google Cloud Speech (existing)
└── AWS Transcribe (new)
    ├── RealtimeTranscription class
    ├── Error handling
    ├── Session management
    └── Audio streaming
```

## Production Considerations

1. **Monitoring**: Monitor AWS Transcribe usage and costs
2. **Rate Limits**: Be aware of AWS Transcribe rate limits
3. **Fallback**: Always enable fallback for production reliability
4. **Logging**: Monitor logs for transcription errors
5. **Security**: Use IAM roles instead of access keys when possible

## Support

For issues with the AWS Transcribe integration:

1. Check the logs for detailed error messages
2. Verify AWS credentials and permissions
3. Test with the provided test script
4. Ensure all dependencies are installed correctly
