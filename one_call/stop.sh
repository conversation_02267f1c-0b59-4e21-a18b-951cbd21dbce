#!/bin/bash

# ─────────────────────────────────────────────
# One call - Universal Stop Script
# Stops Python backend and React frontend cleanly
# Supports fallback, port cleanup, orphan detection
# Author: ChatGPT (world-best solution)
# ─────────────────────────────────────────────

set -e

# ───── Color Codes ─────
RED='\033[0;31m'; GREEN='\033[0;32m'; YELLOW='\033[1;33m'; BLUE='\033[0;34m'; NC='\033[0m'

# ───── Paths ─────
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PID_DIR="$SCRIPT_DIR/.pids"
BACKEND_PID_FILE="$PID_DIR/backend.pid"
FRONTEND_PID_FILE="$PID_DIR/frontend.pid"
PORTS_CONFIG="$SCRIPT_DIR/ports.conf"

# Default port values (can be overridden by configuration)
API_PORT=1801
WEBSOCKET_PORT=1802
FRONTEND_PORT=1800
BACKEND_PROXY_PORT=1803

# ───── Print Helpers ─────
log() { echo -e "${GREEN}[INFO]${NC} $1"; }
warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
err() { echo -e "${RED}[ERROR]${NC} $1"; }
head() { echo -e "${BLUE}[One call]${NC} $1"; }

# ───── Load Port Configuration ─────
load_port_config() {
    if [[ -f "$PORTS_CONFIG" ]]; then
        log "Loading port configuration from $PORTS_CONFIG"
        # Load configuration file
        while IFS='=' read -r key value || [[ -n "$key" ]]; do
            # Skip comments and empty lines
            [[ $key =~ ^[[:space:]]*# ]] && continue
            [[ -z "$key" ]] && continue
            
            # Remove any whitespace
            key=$(echo "$key" | xargs)
            value=$(echo "$value" | xargs)
            
            if [[ -n "$key" && -n "$value" ]]; then
                case "$key" in
                    "API_PORT") API_PORT="$value" ;;
                    "WEBSOCKET_PORT") WEBSOCKET_PORT="$value" ;;
                    "FRONTEND_PORT") FRONTEND_PORT="$value" ;;
                    "BACKEND_PROXY_PORT") BACKEND_PROXY_PORT="$value" ;;
                esac
            fi
        done < "$PORTS_CONFIG"
        
        log "Port configuration loaded: API=$API_PORT, WS=$WEBSOCKET_PORT, Frontend=$FRONTEND_PORT"
    else
        warn "Port configuration file not found, using default values"
    fi
    
    # Check environment variables (they take precedence)
    if [[ -n "$API_PORT_ENV" ]]; then API_PORT="$API_PORT_ENV"; fi
    if [[ -n "$WEBSOCKET_PORT_ENV" ]]; then WEBSOCKET_PORT="$WEBSOCKET_PORT_ENV"; fi
    if [[ -n "$FRONTEND_PORT_ENV" ]]; then FRONTEND_PORT="$FRONTEND_PORT_ENV"; fi
    if [[ -n "$BACKEND_PROXY_PORT_ENV" ]]; then BACKEND_PROXY_PORT="$BACKEND_PROXY_PORT_ENV"; fi
}

# ───── Kill a Full Process Tree ─────
kill_tree() {
    local pid=$1
    if ps -p "$pid" > /dev/null 2>&1; then
        pkill -TERM -P "$pid" || true
        kill -TERM "$pid" 2>/dev/null || true
        sleep 2
        if ps -p "$pid" > /dev/null 2>&1; then
            warn "Force killing PID $pid"
            pkill -KILL -P "$pid" || true
            kill -KILL "$pid" 2>/dev/null || true
        fi
    fi
}

# ───── Stop via PID File ─────
stop_via_pid() {
    local pid_file=$1
    local name=$2

    if [[ -f "$pid_file" ]]; then
        local pid
        pid=$(cat "$pid_file")
        if ps -p "$pid" > /dev/null 2>&1; then
            log "Stopping $name (PID: $pid)"
            kill_tree "$pid"
        else
            warn "$name PID file exists but process not running"
        fi
        rm -f "$pid_file"
    else
        warn "No PID file for $name"
    fi
}

# ───── Port Cleanup ─────
cleanup_ports() {
    local ports=($FRONTEND_PORT $BACKEND_PROXY_PORT $API_PORT $WEBSOCKET_PORT)
    log "Cleaning up ports: ${ports[*]}"
    for port in "${ports[@]}"; do
        local pid
        pid=$(lsof -t -i:$port 2>/dev/null || true)
        if [[ -n "$pid" ]]; then
            warn "Port $port in use by PID $pid"
            kill_tree "$pid"
        fi
    done
}

# ───── Orphaned Process Detection ─────
kill_orphans() {
    local patterns=("python.*main.py" "node.*react-scripts" "npm.*start")
    for pattern in "${patterns[@]}"; do
        local pids
        pids=$(pgrep -f "$pattern" || true)
        for pid in $pids; do
            warn "Killing orphaned process PID $pid (matched: $pattern)"
            kill_tree "$pid"
        done
    done
}

# ───── Temporary File Cleanup ─────
cleanup_files() {
    log "Cleaning up PID files and temp files"
    rm -f "$BACKEND_PID_FILE" "$FRONTEND_PID_FILE"

    [[ -d "$PID_DIR" && -z "$(ls -A "$PID_DIR")" ]] && rmdir "$PID_DIR"
    [[ -d "$SCRIPT_DIR/frontend/.tmp" ]] && rm -rf "$SCRIPT_DIR/frontend/.tmp"
}

# ───── Status Display ─────
status() {
    echo ""
    head "Status Check After Stop"

    if [[ -f "$BACKEND_PID_FILE" ]]; then
        err "✗ Backend may still be running (PID: $(cat "$BACKEND_PID_FILE"))"
    else
        log "✓ Backend stopped"
    fi

    if [[ -f "$FRONTEND_PID_FILE" ]]; then
        err "✗ Frontend may still be running (PID: $(cat "$FRONTEND_PID_FILE"))"
    else
        log "✓ Frontend stopped"
    fi

    local leftovers
    leftovers=$(pgrep -f "main.py|react-scripts|npm start" || true)
    if [[ -n "$leftovers" ]]; then
        warn "Remaining processes: $leftovers"
    fi
    echo ""
}

# ───── Main Execution ─────
main() {
    head "Stopping One call"

    # Load port configuration
    load_port_config

    stop_via_pid "$BACKEND_PID_FILE" "Backend"
    stop_via_pid "$FRONTEND_PID_FILE" "Frontend"

    kill_orphans
    cleanup_ports
    cleanup_files
    status

    head "✅ One call stopped successfully!"
}

trap 'err "Interrupted"; exit 1' INT TERM
main "$@"
