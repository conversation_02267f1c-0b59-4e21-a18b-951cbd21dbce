"""
Tests for WebSocket protocol events and handling
"""

import pytest
import json
import asyncio
import base64
from unittest.mock import MagicMock, AsyncMock

from tutor.modules.websocket import (
    EventType, ConnectedEvent, StartEvent, MediaEvent, StopEvent,
    MarkEvent, ClearEvent, DTMFEvent, EventFactory, StreamManager,
    StreamMetadata, MediaFormat
)


class TestProtocolEvents:
    """Test protocol event creation and serialization"""
    
    def test_connected_event(self):
        """Test ConnectedEvent creation and serialization"""
        event = ConnectedEvent()
        
        assert event.event == EventType.CONNECTED.value
        
        data = event.to_dict()
        assert data == {"event": "connected"}
        
        json_str = event.to_json()
        parsed = json.loads(json_str)
        assert parsed == {"event": "connected"}
    
    def test_start_event(self, stream_manager):
        """Test StartEvent creation and serialization"""
        # Create stream metadata
        stream_sid = stream_manager.create_stream(
            from_number="+**********",
            to_number="+**********",
            custom_parameters={"test": "value"}
        )
        
        metadata = stream_manager.get_stream_metadata(stream_sid)
        assert metadata is not None
        
        sequence_num = stream_manager.get_next_sequence_number(stream_sid)
        event = StartEvent(sequence_num, metadata)
        
        assert event.event == EventType.START.value
        assert event.sequenceNumber == str(sequence_num)
        assert event.streamSid == stream_sid
        
        data = event.to_dict()
        assert data["event"] == "start"
        assert data["sequenceNumber"] == str(sequence_num)
        assert data["streamSid"] == stream_sid
        assert "start" in data
        
        start_data = data["start"]
        assert start_data["streamSid"] == stream_sid
        assert start_data["from"] == "+**********"
        assert start_data["to"] == "+**********"
        assert start_data["mediaFormat"]["encoding"] == "audio/x-mulaw"
        assert start_data["mediaFormat"]["sampleRate"] == 8000
        assert start_data["customParameters"]["test"] == "value"
    
    def test_media_event(self, sample_audio_data):
        """Test MediaEvent creation and serialization"""
        stream_sid = "MZ**********"
        sequence_num = 2
        chunk = 1
        timestamp = 1640995200000
        
        event = MediaEvent(sequence_num, stream_sid, chunk, timestamp, sample_audio_data)
        
        assert event.event == EventType.MEDIA.value
        assert event.sequenceNumber == str(sequence_num)
        assert event.streamSid == stream_sid
        
        data = event.to_dict()
        media = data["media"]
        assert media["chunk"] == str(chunk)
        assert media["timestamp"] == str(timestamp)
        
        # Verify base64 encoding
        decoded_payload = base64.b64decode(media["payload"])
        assert decoded_payload == sample_audio_data
    
    def test_stop_event(self):
        """Test StopEvent creation and serialization"""
        stream_sid = "MZ**********"
        account_sid = "AC**********"
        call_sid = "CA**********"
        sequence_num = 3
        reason = "completed"
        
        event = StopEvent(sequence_num, stream_sid, account_sid, call_sid, reason)
        
        assert event.event == EventType.STOP.value
        assert event.sequenceNumber == str(sequence_num)
        assert event.streamSid == stream_sid
        
        data = event.to_dict()
        stop_data = data["stop"]
        assert stop_data["accountSid"] == account_sid
        assert stop_data["callSid"] == call_sid
        assert stop_data["reason"] == reason
    
    def test_mark_event(self):
        """Test MarkEvent creation and serialization"""
        stream_sid = "MZ**********"
        sequence_num = 4
        mark_name = "test_mark"
        
        event = MarkEvent(sequence_num, stream_sid, mark_name)
        
        assert event.event == EventType.MARK.value
        assert event.sequenceNumber == str(sequence_num)
        assert event.streamSid == stream_sid
        
        data = event.to_dict()
        assert data["mark"]["name"] == mark_name
    
    def test_clear_event(self):
        """Test ClearEvent creation and serialization"""
        stream_sid = "MZ**********"
        
        event = ClearEvent(stream_sid)
        
        assert event.event == EventType.CLEAR.value
        assert event.streamSid == stream_sid
        
        data = event.to_dict()
        assert data == {
            "event": "clear",
            "streamSid": stream_sid
        }
    
    def test_dtmf_event(self):
        """Test DTMFEvent creation and serialization"""
        stream_sid = "MZ**********"
        sequence_num = 5
        digit = "1"
        
        event = DTMFEvent(sequence_num, stream_sid, digit)
        
        assert event.event == EventType.DTMF.value
        assert event.sequenceNumber == str(sequence_num)
        assert event.streamSid == stream_sid
        
        data = event.to_dict()
        assert data["dtmf"]["digit"] == digit


class TestEventFactory:
    """Test EventFactory for creating events from JSON"""
    
    def test_create_connected_event(self):
        """Test creating ConnectedEvent from JSON"""
        json_data = {"event": "connected"}
        
        event = EventFactory.create_event_from_json(json_data)
        
        assert isinstance(event, ConnectedEvent)
        assert event.event == "connected"
    
    def test_create_mark_event(self):
        """Test creating MarkEvent from JSON"""
        json_data = {
            "event": "mark",
            "sequenceNumber": "4",
            "streamSid": "MZ**********",
            "mark": {
                "name": "test_mark"
            }
        }
        
        event = EventFactory.create_event_from_json(json_data)
        
        assert isinstance(event, MarkEvent)
        assert event.sequenceNumber == "4"
        assert event.streamSid == "MZ**********"
        assert event.mark["name"] == "test_mark"
    
    def test_create_clear_event(self):
        """Test creating ClearEvent from JSON"""
        json_data = {
            "event": "clear",
            "streamSid": "MZ**********"
        }
        
        event = EventFactory.create_event_from_json(json_data)
        
        assert isinstance(event, ClearEvent)
        assert event.streamSid == "MZ**********"
    
    def test_create_event_from_string(self):
        """Test creating event from JSON string"""
        json_str = '{"event": "connected"}'
        
        event = EventFactory.create_event_from_json(json_str)
        
        assert isinstance(event, ConnectedEvent)
    
    def test_create_event_invalid_json(self):
        """Test handling invalid JSON"""
        invalid_json = "invalid json"
        
        event = EventFactory.create_event_from_json(invalid_json)
        
        assert event is None
    
    def test_create_event_unknown_type(self):
        """Test handling unknown event type"""
        json_data = {"event": "unknown_event"}
        
        event = EventFactory.create_event_from_json(json_data)
        
        assert event is None


class TestStreamManager:
    """Test StreamManager functionality"""
    
    def test_create_stream(self, stream_manager):
        """Test stream creation"""
        stream_sid = stream_manager.create_stream(
            account_sid="AC123",
            call_sid="CA123",
            from_number="+**********",
            to_number="+**********",
            custom_parameters={"test": "value"}
        )
        
        assert stream_sid.startswith("MZ")
        assert len(stream_sid) == 34  # MZ + 32 hex chars
        assert stream_sid in stream_manager.streams
        assert stream_sid in stream_manager.sequence_counters
        
        stream_data = stream_manager.streams[stream_sid]
        assert stream_data["accountSid"] == "AC123"
        assert stream_data["callSid"] == "CA123"
        assert stream_data["from"] == "+**********"
        assert stream_data["to"] == "+**********"
        assert stream_data["customParameters"]["test"] == "value"
    
    def test_create_stream_default_values(self, stream_manager):
        """Test stream creation with default values"""
        stream_sid = stream_manager.create_stream()
        
        stream_data = stream_manager.streams[stream_sid]
        assert stream_data["accountSid"].startswith("AC")
        assert stream_data["callSid"].startswith("CA")
        assert stream_data["from"] == "XXXXXXXXXX"
        assert stream_data["to"] == "XXXXXXXXXX"
        assert stream_data["customParameters"] == {}
    
    def test_sequence_numbers(self, stream_manager):
        """Test sequence number management"""
        stream_sid = stream_manager.create_stream()
        
        # First sequence number should be 1
        seq1 = stream_manager.get_next_sequence_number(stream_sid)
        assert seq1 == 1
        
        # Second should be 2
        seq2 = stream_manager.get_next_sequence_number(stream_sid)
        assert seq2 == 2
        
        # Third should be 3
        seq3 = stream_manager.get_next_sequence_number(stream_sid)
        assert seq3 == 3
    
    def test_get_stream_metadata(self, stream_manager):
        """Test retrieving stream metadata"""
        stream_sid = stream_manager.create_stream(
            from_number="+**********",
            to_number="+**********",
            custom_parameters={"param1": "value1"}
        )
        
        metadata = stream_manager.get_stream_metadata(stream_sid)
        
        assert isinstance(metadata, StreamMetadata)
        assert metadata.streamSid == stream_sid
        assert metadata.from_number == "+**********"
        assert metadata.to_number == "+**********"
        assert metadata.customParameters["param1"] == "value1"
        assert isinstance(metadata.mediaFormat, MediaFormat)
        assert metadata.mediaFormat.encoding == "audio/x-mulaw"
        assert metadata.mediaFormat.sampleRate == 8000
    
    def test_get_stream_metadata_nonexistent(self, stream_manager):
        """Test retrieving metadata for nonexistent stream"""
        metadata = stream_manager.get_stream_metadata("invalid_stream_id")
        assert metadata is None
    
    def test_end_stream(self, stream_manager):
        """Test ending a stream"""
        stream_sid = stream_manager.create_stream()
        reason = "test_reason"
        
        stream_manager.end_stream(stream_sid, reason)
        
        stream_data = stream_manager.streams[stream_sid]
        assert stream_data["status"] == "ended"
        assert stream_data["end_reason"] == reason
        assert "ended_at" in stream_data
    
    def test_cleanup_stream(self, stream_manager):
        """Test cleaning up stream data"""
        stream_sid = stream_manager.create_stream()
        
        # Verify stream exists
        assert stream_sid in stream_manager.streams
        assert stream_sid in stream_manager.sequence_counters
        
        stream_manager.cleanup_stream(stream_sid)
        
        # Verify stream is removed
        assert stream_sid not in stream_manager.streams
        assert stream_sid not in stream_manager.sequence_counters


class TestMediaFormat:
    """Test MediaFormat specification"""
    
    def test_default_media_format(self):
        """Test default MediaFormat values"""
        format = MediaFormat()
        
        assert format.encoding == "audio/x-mulaw"
        assert format.sampleRate == 8000
        assert format.bitRate == 64
        assert format.bitDepth == 8
    
    def test_custom_media_format(self):
        """Test custom MediaFormat values"""
        format = MediaFormat(
            encoding="audio/x-pcm",
            sampleRate=16000,
            bitRate=128,
            bitDepth=16
        )
        
        assert format.encoding == "audio/x-pcm"
        assert format.sampleRate == 16000
        assert format.bitRate == 128
        assert format.bitDepth == 16


class TestStreamMetadata:
    """Test StreamMetadata functionality"""
    
    def test_stream_metadata_creation(self):
        """Test creating StreamMetadata"""
        media_format = MediaFormat()
        custom_params = {"param1": "value1", "param2": "value2"}
        
        metadata = StreamMetadata(
            streamSid="MZ**********",
            accountSid="AC**********",
            callSid="CA**********",
            from_number="+**********",
            to_number="+**********",
            mediaFormat=media_format,
            customParameters=custom_params
        )
        
        assert metadata.streamSid == "MZ**********"
        assert metadata.accountSid == "AC**********"
        assert metadata.callSid == "CA**********"
        assert metadata.from_number == "+**********"
        assert metadata.to_number == "+**********"
        assert metadata.mediaFormat == media_format
        assert metadata.customParameters == custom_params
    
    def test_stream_metadata_to_dict(self):
        """Test converting StreamMetadata to dictionary"""
        media_format = MediaFormat()
        custom_params = {"test": "value"}
        
        metadata = StreamMetadata(
            streamSid="MZ**********",
            accountSid="AC**********",
            callSid="CA**********",
            from_number="+**********",
            to_number="+**********",
            mediaFormat=media_format,
            customParameters=custom_params
        )
        
        data = metadata.to_dict()
        
        # Check field name mapping
        assert data["from"] == "+**********"  # from_number -> from
        assert data["to"] == "+**********"    # to_number -> to
        assert "from_number" not in data      # Original field should not be present
        assert "to_number" not in data        # Original field should not be present
        
        # Check other fields
        assert data["streamSid"] == "MZ**********"
        assert data["accountSid"] == "AC**********"
        assert data["callSid"] == "CA**********"
        assert data["customParameters"] == custom_params
        
        # Check media format
        media_data = data["mediaFormat"]
        assert media_data["encoding"] == "audio/x-mulaw"
        assert media_data["sampleRate"] == 8000
        assert media_data["bitRate"] == 64
        assert media_data["bitDepth"] == 8