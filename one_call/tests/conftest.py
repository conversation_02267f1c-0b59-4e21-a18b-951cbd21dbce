"""
Pytest configuration and fixtures for One call tests
"""

import pytest
import asyncio
import json
import tempfile
import shutil
from unittest.mock import MagicMock, AsyncMock
import websockets
import redis
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Import application components
from tutor.modules.websocket import Stream<PERSON>anager, AudioCodec
from tutor.executors.user import User
from tutor.modules.logger import setup_logger
from integrations import BaseIntegration, IntegrationConfig
from production.security.security_middleware import SecurityMiddleware


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def mock_logger():
    """Mock logger for testing"""
    logger = MagicMock()
    logger.info = MagicMock()
    logger.error = MagicMock()
    logger.warning = MagicMock()
    logger.debug = MagicMock()
    return logger


@pytest.fixture
def stream_manager():
    """Create a StreamManager instance for testing"""
    return StreamManager()


@pytest.fixture
def audio_codec():
    """Create an AudioCodec instance for testing"""
    return AudioCodec()


@pytest.fixture
def temp_directory():
    """Create a temporary directory for test files"""
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)


@pytest.fixture
def mock_websocket():
    """Mock WebSocket connection"""
    mock_ws = AsyncMock()
    mock_ws.send = AsyncMock()
    mock_ws.close = AsyncMock()
    mock_ws.open = True
    return mock_ws


@pytest.fixture
def mock_audio_processor():
    """Mock AudioProcessor for testing"""
    processor = MagicMock()
    processor.audio_queue = []
    processor.start_ai_call = MagicMock()
    processor.save_audio_file = MagicMock()
    processor.add_protocol_audio = MagicMock()
    processor.clear_buffer = MagicMock()
    return processor


@pytest.fixture
def mock_bot_manager():
    """Mock BotManager for testing"""
    manager = MagicMock()
    manager.start_new_session = MagicMock(return_value="Hello, how can I help you?")
    manager.handle_user_input = AsyncMock(return_value="I understand your question.")
    manager.start_new_call = MagicMock(return_value="Welcome to One call!")
    return manager


@pytest.fixture
def mock_user(mock_websocket, mock_audio_processor, mock_bot_manager, mock_logger):
    """Create a mock User instance for testing"""
    user_data = {
        'mobile': '+**********',
        'userId': 'test_user_123',
        'name': 'Test User',
        'sessionType': 'call'
    }
    
    # Create mock event loop
    loop = asyncio.new_event_loop()
    
    user = User(
        session="test_session_123",
        name="Test User",
        websocket=mock_websocket,
        data=user_data,
        event_loop=loop,
        active_users=[]
    )
    
    # Replace components with mocks
    user.audio_processor = mock_audio_processor
    user.bot_manager = mock_bot_manager
    user.logger = mock_logger
    
    return user


@pytest.fixture
def sample_audio_data():
    """Generate sample audio data for testing"""
    # Create sample mulaw audio data (160 samples = 20ms at 8000Hz)
    import struct
    mulaw_data = b''
    for i in range(160):
        # Simple sine wave converted to mulaw
        sample = int(127 * (i % 32) / 32)
        mulaw_data += struct.pack('B', sample)
    return mulaw_data


@pytest.fixture
def integration_config():
    """Create a sample integration configuration"""
    return IntegrationConfig(
        platform_name="test_platform",
        api_key="test_api_key",
        api_secret="test_api_secret",
        endpoint_url="https://api.test.com",
        webhook_url="https://webhook.test.com",
        custom_parameters={"test_param": "test_value"}
    )


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing"""
    redis_mock = MagicMock()
    redis_mock.incr = MagicMock(return_value=1)
    redis_mock.expire = MagicMock(return_value=True)
    redis_mock.pipeline = MagicMock()
    
    # Mock pipeline
    pipe_mock = MagicMock()
    pipe_mock.incr = MagicMock(return_value=pipe_mock)
    pipe_mock.expire = MagicMock(return_value=pipe_mock)
    pipe_mock.execute = MagicMock(return_value=[1, True])
    redis_mock.pipeline.return_value = pipe_mock
    
    return redis_mock


@pytest.fixture
def security_middleware(mock_redis):
    """Create SecurityMiddleware instance for testing"""
    middleware = SecurityMiddleware(redis_client=mock_redis)
    return middleware


@pytest.fixture
def mock_flask_app():
    """Mock Flask application for testing"""
    from flask import Flask
    
    app = Flask(__name__)
    app.config.update({
        'SECRET_KEY': 'test_secret_key',
        'TESTING': True,
        'JWT_EXPIRATION_HOURS': 24,
        'RATE_LIMIT_PER_MINUTE': 60,
        'RATE_LIMIT_PER_HOUR': 1000,
        'MAX_FAILED_ATTEMPTS': 5,
        'LOCKOUT_DURATION_MINUTES': 30,
        'REQUIRE_API_KEY': True,
        'VALID_API_KEYS': ['test_api_key_1', 'test_api_key_2'],
        'ALLOWED_ORIGINS': ['localhost', '127.0.0.1']
    })
    
    return app


@pytest.fixture
def sample_protocol_events():
    """Generate sample protocol events for testing"""
    return {
        'connected': {
            'event': 'connected'
        },
        'start': {
            'event': 'start',
            'sequenceNumber': '1',
            'start': {
                'streamSid': 'MZ**********abcdef',
                'accountSid': 'AC**********abcdef',
                'callSid': 'CA**********abcdef',
                'from': '+**********',
                'to': '+**********',
                'mediaFormat': {
                    'encoding': 'audio/x-mulaw',
                    'sampleRate': 8000,
                    'bitRate': 64,
                    'bitDepth': 8
                },
                'customParameters': {
                    'test_param': 'test_value'
                }
            },
            'streamSid': 'MZ**********abcdef'
        },
        'media': {
            'event': 'media',
            'sequenceNumber': '2',
            'media': {
                'chunk': '1',
                'timestamp': '*************',
                'payload': 'bm8rSmpvYUpqcHo='  # base64 encoded sample
            },
            'streamSid': 'MZ**********abcdef'
        },
        'stop': {
            'event': 'stop',
            'sequenceNumber': '3',
            'stop': {
                'accountSid': 'AC**********abcdef',
                'callSid': 'CA**********abcdef',
                'reason': 'completed'
            },
            'streamSid': 'MZ**********abcdef'
        },
        'mark': {
            'event': 'mark',
            'sequenceNumber': '4',
            'streamSid': 'MZ**********abcdef',
            'mark': {
                'name': 'test_mark'
            }
        },
        'clear': {
            'event': 'clear',
            'streamSid': 'MZ**********abcdef'
        },
        'dtmf': {
            'event': 'dtmf',
            'streamSid': 'MZ**********abcdef',
            'sequenceNumber': '5',
            'dtmf': {
                'digit': '1'
            }
        }
    }


@pytest.fixture
async def websocket_server():
    """Start a test WebSocket server"""
    async def echo_handler(websocket, path):
        async for message in websocket:
            # Echo back the message
            await websocket.send(message)
    
    server = await websockets.serve(echo_handler, "localhost", 0)
    port = server.sockets[0].getsockname()[1]
    
    yield f"ws://localhost:{port}"
    
    server.close()
    await server.wait_closed()


@pytest.fixture
def mock_aiohttp_session():
    """Mock aiohttp session for HTTP requests"""
    from unittest.mock import AsyncMock, MagicMock
    
    session = AsyncMock()
    response = AsyncMock()
    response.status = 200
    response.json = AsyncMock(return_value={"status": "success"})
    response.text = AsyncMock(return_value="success")
    
    session.get = AsyncMock(return_value=response)
    session.post = AsyncMock(return_value=response)
    session.put = AsyncMock(return_value=response)
    session.delete = AsyncMock(return_value=response)
    
    # Context manager support
    session.__aenter__ = AsyncMock(return_value=session)
    session.__aexit__ = AsyncMock(return_value=None)
    response.__aenter__ = AsyncMock(return_value=response)
    response.__aexit__ = AsyncMock(return_value=None)
    
    return session


@pytest.fixture
def performance_test_data():
    """Generate data for performance testing"""
    return {
        'concurrent_connections': 100,
        'messages_per_connection': 50,
        'audio_chunk_size': 160,
        'test_duration_seconds': 30,
        'expected_latency_ms': 100
    }


# Test helpers
def create_test_audio_chunk(size=160):
    """Create a test audio chunk of specified size"""
    import random
    return bytes([random.randint(0, 255) for _ in range(size)])


def assert_audio_format(audio_data, expected_format='mulaw'):
    """Assert that audio data is in the expected format"""
    assert isinstance(audio_data, bytes), "Audio data must be bytes"
    
    if expected_format == 'mulaw':
        # Basic validation for mulaw format
        assert len(audio_data) > 0, "Audio data cannot be empty"
        # Each sample should be a single byte
        for byte in audio_data:
            assert 0 <= byte <= 255, f"Invalid mulaw sample: {byte}"


def assert_protocol_event(event_data, event_type):
    """Assert that event data conforms to protocol specification"""
    assert isinstance(event_data, dict), "Event data must be a dictionary"
    assert event_data.get('event') == event_type, f"Expected event type {event_type}"
    
    if event_type in ['start', 'media', 'stop', 'mark', 'dtmf']:
        assert 'sequenceNumber' in event_data, "Sequence number required"
        assert 'streamSid' in event_data, "Stream SID required"
    
    if event_type == 'media':
        media = event_data.get('media', {})
        assert 'chunk' in media, "Media chunk number required"
        assert 'timestamp' in media, "Media timestamp required"
        assert 'payload' in media, "Media payload required"


# Async test helpers
async def wait_for_condition(condition_func, timeout=5.0, interval=0.1):
    """Wait for a condition to become true with timeout"""
    import time
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        if condition_func():
            return True
        await asyncio.sleep(interval)
    
    return False


async def simulate_websocket_client(url, messages, delay=0.1):
    """Simulate a WebSocket client sending messages"""
    results = []
    
    async with websockets.connect(url) as websocket:
        for message in messages:
            await websocket.send(json.dumps(message))
            await asyncio.sleep(delay)
            
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                results.append(json.loads(response))
            except asyncio.TimeoutError:
                results.append(None)
    
    return results