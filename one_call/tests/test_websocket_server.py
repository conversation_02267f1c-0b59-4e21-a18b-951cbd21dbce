"""
Tests for WebSocket server protocol handling and functionality
"""

import pytest
import asyncio
import json
import base64
from unittest.mock import Magic<PERSON><PERSON>, AsyncMock, patch

from tutor.websocket_server import handle_websocket_connection
from tutor.modules.websocket import EventFactory, StreamManager
from tutor.executors.user import User


class TestWebSocketServer:
    """Test WebSocket server functionality"""
    
    @pytest.mark.asyncio
    async def test_websocket_connection_establishment(self, mock_websocket, mock_user):
        """Test WebSocket connection establishment"""
        # Mock the connection handler
        with patch('tutor.websocket_server.handle_websocket_connection') as mock_handler:
            mock_handler.return_value = AsyncMock()
            
            # Simulate connection
            await mock_handler(mock_websocket, "/ws")
            
            # Verify handler was called
            mock_handler.assert_called_once_with(mock_websocket, "/ws")
    
    @pytest.mark.asyncio
    async def test_protocol_event_handling(self, mock_websocket, mock_user, sample_protocol_events):
        """Test handling of protocol events"""
        connected_event = sample_protocol_events['connected']
        
        # Mock send method to capture responses
        responses = []
        async def mock_send(data):
            responses.append(json.loads(data))
        
        mock_websocket.send = mock_send
        
        # Test connected event handling
        with patch('tutor.websocket_server.handle_connected_event') as mock_handler:
            mock_handler.return_value = {"event": "connected"}
            
            # Simulate event processing
            event = EventFactory.create_event_from_json(connected_event)
            result = mock_handler(event, mock_user)
            
            # Verify response
            assert result["event"] == "connected"
    
    @pytest.mark.asyncio
    async def test_start_event_processing(self, mock_websocket, mock_user, sample_protocol_events):
        """Test start event processing"""
        start_event_data = sample_protocol_events['start']
        
        with patch('tutor.websocket_server.handle_start_event') as mock_handler:
            mock_handler.return_value = {"status": "stream_started"}
            
            # Create event from JSON
            start_event = EventFactory.create_event_from_json(start_event_data)
            
            # Process event
            result = mock_handler(start_event, mock_user)
            
            # Verify stream was started
            assert result["status"] == "stream_started"
            mock_handler.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_media_event_processing(self, mock_websocket, mock_user, sample_protocol_events, mock_audio_processor):
        """Test media event processing"""
        media_event_data = sample_protocol_events['media']
        
        with patch('tutor.websocket_server.handle_media_event') as mock_handler:
            mock_handler.return_value = {"status": "media_processed"}
            
            # Create media event
            media_event = EventFactory.create_event_from_json(media_event_data)
            
            # Process event
            result = mock_handler(media_event, mock_user)
            
            # Verify media was processed
            assert result["status"] == "media_processed"
            mock_handler.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_stop_event_processing(self, mock_websocket, mock_user, sample_protocol_events):
        """Test stop event processing"""
        stop_event_data = sample_protocol_events['stop']
        
        with patch('tutor.websocket_server.handle_stop_event') as mock_handler:
            mock_handler.return_value = {"status": "stream_stopped"}
            
            # Create stop event
            stop_event = EventFactory.create_event_from_json(stop_event_data)
            
            # Process event
            result = mock_handler(stop_event, mock_user)
            
            # Verify stream was stopped
            assert result["status"] == "stream_stopped"
            mock_handler.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_mark_event_processing(self, mock_websocket, mock_user, sample_protocol_events):
        """Test mark event processing"""
        mark_event_data = sample_protocol_events['mark']
        
        with patch('tutor.websocket_server.handle_mark_event') as mock_handler:
            mock_handler.return_value = {"status": "mark_processed"}
            
            # Create mark event
            mark_event = EventFactory.create_event_from_json(mark_event_data)
            
            # Process event
            result = mock_handler(mark_event, mock_user)
            
            # Verify mark was processed
            assert result["status"] == "mark_processed"
            mock_handler.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_clear_event_processing(self, mock_websocket, mock_user, sample_protocol_events):
        """Test clear event processing"""
        clear_event_data = sample_protocol_events['clear']
        
        with patch('tutor.websocket_server.handle_clear_event') as mock_handler:
            mock_handler.return_value = {"status": "buffer_cleared"}
            
            # Create clear event
            clear_event = EventFactory.create_event_from_json(clear_event_data)
            
            # Process event
            result = mock_handler(clear_event, mock_user)
            
            # Verify buffer was cleared
            assert result["status"] == "buffer_cleared"
            mock_handler.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_dtmf_event_processing(self, mock_websocket, mock_user, sample_protocol_events):
        """Test DTMF event processing"""
        dtmf_event_data = sample_protocol_events['dtmf']
        
        with patch('tutor.websocket_server.handle_dtmf_event') as mock_handler:
            mock_handler.return_value = {"status": "dtmf_processed", "digit": "1"}
            
            # Create DTMF event
            dtmf_event = EventFactory.create_event_from_json(dtmf_event_data)
            
            # Process event
            result = mock_handler(dtmf_event, mock_user)
            
            # Verify DTMF was processed
            assert result["status"] == "dtmf_processed"
            assert result["digit"] == "1"
            mock_handler.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_invalid_event_handling(self, mock_websocket, mock_user):
        """Test handling of invalid events"""
        invalid_event = {"event": "unknown_event"}
        
        with patch('tutor.websocket_server.handle_unknown_event') as mock_handler:
            mock_handler.return_value = {"error": "Unknown event type"}
            
            # Process invalid event
            result = mock_handler(invalid_event, mock_user)
            
            # Verify error response
            assert "error" in result
            assert result["error"] == "Unknown event type"
    
    @pytest.mark.asyncio
    async def test_malformed_json_handling(self, mock_websocket, mock_user):
        """Test handling of malformed JSON"""
        malformed_json = "{ invalid json }"
        
        with patch('tutor.websocket_server.handle_malformed_message') as mock_handler:
            mock_handler.return_value = {"error": "Invalid JSON format"}
            
            # Process malformed message
            result = mock_handler(malformed_json, mock_user)
            
            # Verify error response
            assert "error" in result
            assert result["error"] == "Invalid JSON format"
    
    @pytest.mark.asyncio
    async def test_stream_lifecycle_management(self, mock_websocket, mock_user, stream_manager):
        """Test complete stream lifecycle"""
        # Start stream
        stream_sid = stream_manager.create_stream(
            from_number="+1234567890",
            to_number="+0987654321"
        )
        
        # Verify stream created
        assert stream_sid in stream_manager.streams
        
        # Get sequence numbers
        seq1 = stream_manager.get_next_sequence_number(stream_sid)
        seq2 = stream_manager.get_next_sequence_number(stream_sid)
        
        assert seq1 == 1
        assert seq2 == 2
        
        # End stream
        stream_manager.end_stream(stream_sid, "completed")
        
        # Verify stream ended
        stream_data = stream_manager.streams[stream_sid]
        assert stream_data["status"] == "ended"
        assert stream_data["end_reason"] == "completed"
    
    @pytest.mark.asyncio
    async def test_concurrent_connections(self, mock_websocket):
        """Test handling multiple concurrent connections"""
        connections = []
        
        for i in range(5):
            # Create mock connection
            mock_conn = AsyncMock()
            mock_conn.send = AsyncMock()
            mock_conn.close = AsyncMock()
            connections.append(mock_conn)
        
        # Simulate concurrent handling
        with patch('tutor.websocket_server.handle_websocket_connection') as mock_handler:
            tasks = []
            for conn in connections:
                task = asyncio.create_task(mock_handler(conn, "/ws"))
                tasks.append(task)
            
            # Wait for all connections to be handled
            await asyncio.gather(*tasks, return_exceptions=True)
            
            # Verify all connections were handled
            assert mock_handler.call_count == 5
    
    @pytest.mark.asyncio
    async def test_audio_data_validation(self, mock_websocket, mock_user, audio_codec):
        """Test validation of audio data in media events"""
        # Create valid media event with audio data
        valid_audio = audio_codec.encode_base64(b'\x00' * 160)
        
        media_event = {
            "event": "media",
            "sequenceNumber": "1",
            "streamSid": "MZ1234567890",
            "media": {
                "chunk": "1",
                "timestamp": "1640995200000",
                "payload": valid_audio
            }
        }
        
        with patch('tutor.websocket_server.validate_media_payload') as mock_validator:
            mock_validator.return_value = True
            
            # Validate media payload
            is_valid = mock_validator(media_event["media"]["payload"])
            
            # Verify validation passed
            assert is_valid == True
            mock_validator.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_sequence_number_validation(self, mock_websocket, mock_user, stream_manager):
        """Test sequence number validation"""
        stream_sid = stream_manager.create_stream()
        
        # Get expected sequence numbers
        expected_seq1 = stream_manager.get_next_sequence_number(stream_sid)
        expected_seq2 = stream_manager.get_next_sequence_number(stream_sid)
        
        # Test with correct sequence
        with patch('tutor.websocket_server.validate_sequence_number') as mock_validator:
            mock_validator.return_value = True
            
            is_valid = mock_validator(stream_sid, expected_seq1)
            assert is_valid == True
            
            # Test with out-of-order sequence
            mock_validator.return_value = False
            is_valid = mock_validator(stream_sid, expected_seq1 - 1)
            assert is_valid == False
    
    @pytest.mark.asyncio
    async def test_error_recovery(self, mock_websocket, mock_user):
        """Test error recovery mechanisms"""
        # Simulate network error
        mock_websocket.send.side_effect = ConnectionError("Network error")
        
        with patch('tutor.websocket_server.handle_connection_error') as mock_handler:
            mock_handler.return_value = {"action": "reconnect"}
            
            # Handle connection error
            result = mock_handler(ConnectionError("Network error"), mock_user)
            
            # Verify error handling
            assert result["action"] == "reconnect"
            mock_handler.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_heartbeat_mechanism(self, mock_websocket, mock_user):
        """Test WebSocket heartbeat/ping-pong mechanism"""
        with patch('tutor.websocket_server.send_ping') as mock_ping:
            mock_ping.return_value = True
            
            # Send ping
            result = mock_ping(mock_websocket)
            
            # Verify ping was sent
            assert result == True
            mock_ping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_resource_cleanup(self, mock_websocket, mock_user, stream_manager):
        """Test proper resource cleanup on disconnection"""
        stream_sid = stream_manager.create_stream()
        
        # Add user to active streams
        mock_user.stream_id = stream_sid
        
        with patch('tutor.websocket_server.cleanup_user_resources') as mock_cleanup:
            mock_cleanup.return_value = True
            
            # Cleanup resources
            result = mock_cleanup(mock_user, stream_manager)
            
            # Verify cleanup was performed
            assert result == True
            mock_cleanup.assert_called_once()


class TestWebSocketServerIntegration:
    """Integration tests for WebSocket server with other components"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_call_flow(self, websocket_server, sample_protocol_events):
        """Test complete end-to-end call flow"""
        # This would require a running WebSocket server
        # For now, we'll test the flow with mocks
        
        events_sequence = [
            sample_protocol_events['connected'],
            sample_protocol_events['start'],
            sample_protocol_events['media'],
            sample_protocol_events['mark'],
            sample_protocol_events['stop']
        ]
        
        responses = []
        
        with patch('tutor.websocket_server.process_event') as mock_processor:
            # Mock responses for each event type
            mock_processor.side_effect = [
                {"event": "connected"},
                {"status": "stream_started"},
                {"status": "media_processed"},
                {"status": "mark_processed"},
                {"status": "stream_stopped"}
            ]
            
            # Process events in sequence
            for event in events_sequence:
                response = mock_processor(event)
                responses.append(response)
            
            # Verify all events were processed
            assert len(responses) == 5
            assert responses[0]["event"] == "connected"
            assert responses[1]["status"] == "stream_started"
            assert responses[4]["status"] == "stream_stopped"
    
    @pytest.mark.asyncio
    async def test_audio_streaming_integration(self, mock_websocket, mock_user, audio_codec):
        """Test audio streaming integration with codec"""
        # Simulate audio streaming over WebSocket
        chunks = []
        
        for i in range(5):
            # Create audio chunk
            audio_data = bytes([i] * 160)
            base64_audio = audio_codec.encode_base64(audio_data)
            
            media_event = {
                "event": "media",
                "sequenceNumber": str(i + 1),
                "streamSid": "MZ1234567890",
                "media": {
                    "chunk": str(i + 1),
                    "timestamp": str(1640995200000 + i * 20),
                    "payload": base64_audio
                }
            }
            
            chunks.append(media_event)
        
        # Process all chunks
        processed_count = 0
        with patch('tutor.websocket_server.process_media_chunk') as mock_processor:
            mock_processor.return_value = {"status": "processed"}
            
            for chunk in chunks:
                result = mock_processor(chunk)
                if result["status"] == "processed":
                    processed_count += 1
            
            # Verify all chunks were processed
            assert processed_count == 5
            assert mock_processor.call_count == 5
    
    @pytest.mark.asyncio
    async def test_platform_integration_flow(self, mock_websocket, mock_user):
        """Test WebSocket server integration with platform adapters"""
        platform_events = {
            "twilio_incoming": {
                "event": "start",
                "platform": "twilio",
                "call_sid": "CA1234567890"
            },
            "google_meet_join": {
                "event": "start", 
                "platform": "google_meet",
                "meeting_id": "abc-defg-hij"
            }
        }
        
        with patch('tutor.websocket_server.handle_platform_event') as mock_handler:
            mock_handler.return_value = {"status": "platform_connected"}
            
            for event_name, event_data in platform_events.items():
                result = mock_handler(event_data, mock_user)
                assert result["status"] == "platform_connected"
            
            # Verify both platform events were handled
            assert mock_handler.call_count == 2
    
    @pytest.mark.asyncio
    async def test_performance_under_load(self, mock_websocket):
        """Test WebSocket server performance under load"""
        import time
        
        start_time = time.time()
        event_count = 100
        
        with patch('tutor.websocket_server.process_event') as mock_processor:
            mock_processor.return_value = {"status": "processed"}
            
            # Simulate processing many events
            tasks = []
            for i in range(event_count):
                event = {"event": "media", "sequenceNumber": str(i)}
                task = asyncio.create_task(mock_processor(event))
                tasks.append(task)
            
            # Wait for all events to be processed
            results = await asyncio.gather(*tasks)
            
            end_time = time.time()
            duration = end_time - start_time
            
            # Verify performance (should process 100 events quickly)
            assert len(results) == event_count
            assert duration < 1.0  # Should complete within 1 second
            assert all(r["status"] == "processed" for r in results)