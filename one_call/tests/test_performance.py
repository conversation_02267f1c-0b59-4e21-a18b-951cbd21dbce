"""
Performance and load testing for One call components
"""

import pytest
import asyncio
import time
import statistics
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from unittest.mock import MagicMock, AsyncMock, patch

from tutor.modules.websocket import <PERSON><PERSON><PERSON><PERSON>, AudioCodec, AudioBuffer
from tutor.modules.websocket.audio_codec import AudioCodec
from production.security.security_middleware import SecurityMiddleware


class TestPerformanceBaseline:
    """Baseline performance tests for core components"""
    
    def test_audio_codec_performance(self, audio_codec, performance_test_data):
        """Test audio codec encoding/decoding performance"""
        chunk_size = performance_test_data['audio_chunk_size']
        iterations = 1000
        
        # Generate test audio data
        import numpy as np
        pcm_samples = np.random.randint(-32768, 32767, chunk_size, dtype=np.int16)
        pcm_data = pcm_samples.tobytes()
        
        # Test PCM to mulaw conversion performance
        start_time = time.time()
        for _ in range(iterations):
            mulaw_data = audio_codec.pcm_to_mulaw(pcm_data)
        pcm_to_mulaw_time = time.time() - start_time
        
        # Test mulaw to PCM conversion performance
        start_time = time.time()
        for _ in range(iterations):
            converted_pcm = audio_codec.mulaw_to_pcm(mulaw_data)
        mulaw_to_pcm_time = time.time() - start_time
        
        # Test base64 encoding performance
        start_time = time.time()
        for _ in range(iterations):
            encoded = audio_codec.encode_base64(mulaw_data)
        encoding_time = time.time() - start_time
        
        # Test base64 decoding performance
        start_time = time.time()
        for _ in range(iterations):
            decoded = audio_codec.decode_base64(encoded)
        decoding_time = time.time() - start_time
        
        # Performance assertions (should process audio in real-time or better)
        pcm_to_mulaw_per_second = iterations / pcm_to_mulaw_time
        mulaw_to_pcm_per_second = iterations / mulaw_to_pcm_time
        encoding_per_second = iterations / encoding_time
        decoding_per_second = iterations / decoding_time
        
        # For 20ms audio chunks at 8000Hz, we need 50 conversions per second minimum
        assert pcm_to_mulaw_per_second > 500, f"PCM to mulaw too slow: {pcm_to_mulaw_per_second} ops/sec"
        assert mulaw_to_pcm_per_second > 500, f"Mulaw to PCM too slow: {mulaw_to_pcm_per_second} ops/sec"
        assert encoding_per_second > 1000, f"Base64 encoding too slow: {encoding_per_second} ops/sec"
        assert decoding_per_second > 1000, f"Base64 decoding too slow: {decoding_per_second} ops/sec"
    
    def test_stream_manager_performance(self, stream_manager, performance_test_data):
        """Test StreamManager performance under load"""
        concurrent_streams = performance_test_data['concurrent_connections']
        operations_per_stream = 100
        
        # Test stream creation performance
        start_time = time.time()
        stream_ids = []
        for i in range(concurrent_streams):
            stream_id = stream_manager.create_stream(
                from_number=f"+123456{i:04d}",
                to_number=f"+987654{i:04d}"
            )
            stream_ids.append(stream_id)
        creation_time = time.time() - start_time
        
        # Test sequence number generation performance
        start_time = time.time()
        for stream_id in stream_ids:
            for _ in range(operations_per_stream):
                stream_manager.get_next_sequence_number(stream_id)
        sequence_time = time.time() - start_time
        
        # Test stream cleanup performance
        start_time = time.time()
        for stream_id in stream_ids:
            stream_manager.cleanup_stream(stream_id)
        cleanup_time = time.time() - start_time
        
        # Performance assertions
        creation_rate = concurrent_streams / creation_time
        sequence_rate = (concurrent_streams * operations_per_stream) / sequence_time
        cleanup_rate = concurrent_streams / cleanup_time
        
        assert creation_rate > 100, f"Stream creation too slow: {creation_rate} streams/sec"
        assert sequence_rate > 1000, f"Sequence generation too slow: {sequence_rate} ops/sec"
        assert cleanup_rate > 200, f"Stream cleanup too slow: {cleanup_rate} streams/sec"
    
    def test_audio_buffer_performance(self):
        """Test AudioBuffer performance under concurrent access"""
        buffer = AudioBuffer(max_size=10000)
        chunk_size = 160
        iterations = 1000
        
        def producer():
            for i in range(iterations):
                data = bytes([i % 256] * chunk_size)
                buffer.add_data(data)
        
        def consumer():
            consumed = 0
            while consumed < iterations * chunk_size:
                data = buffer.get_data(chunk_size)
                if data:
                    consumed += len(data)
                else:
                    time.sleep(0.001)  # Small delay if no data
        
        # Test concurrent producer/consumer performance
        start_time = time.time()
        
        producer_thread = threading.Thread(target=producer)
        consumer_thread = threading.Thread(target=consumer)
        
        producer_thread.start()
        consumer_thread.start()
        
        producer_thread.join()
        consumer_thread.join()
        
        total_time = time.time() - start_time
        
        # Should process audio data quickly
        throughput = (iterations * chunk_size) / total_time
        assert throughput > 100000, f"Buffer throughput too low: {throughput} bytes/sec"


class TestConcurrencyStress:
    """Stress tests for concurrent operations"""
    
    @pytest.mark.asyncio
    async def test_concurrent_websocket_connections(self, performance_test_data):
        """Test handling multiple concurrent WebSocket connections"""
        concurrent_connections = min(performance_test_data['concurrent_connections'], 50)  # Limit for test
        messages_per_connection = performance_test_data['messages_per_connection']
        
        async def simulate_connection(connection_id):
            """Simulate a single WebSocket connection"""
            messages_sent = 0
            messages_received = 0
            start_time = time.time()
            
            for i in range(messages_per_connection):
                # Simulate sending a message
                message = {
                    "event": "media",
                    "sequenceNumber": str(i + 1),
                    "streamSid": f"MZ{connection_id:010d}",
                    "media": {
                        "chunk": str(i + 1),
                        "timestamp": str(int(time.time() * 1000)),
                        "payload": "dGVzdCBhdWRpbyBkYXRh"  # base64 test data
                    }
                }
                
                # Simulate processing delay
                await asyncio.sleep(0.001)  # 1ms processing time
                messages_sent += 1
                
                # Simulate receiving response
                await asyncio.sleep(0.001)  # 1ms response time
                messages_received += 1
            
            end_time = time.time()
            return {
                "connection_id": connection_id,
                "messages_sent": messages_sent,
                "messages_received": messages_received,
                "duration": end_time - start_time
            }
        
        # Run concurrent connections
        start_time = time.time()
        tasks = [simulate_connection(i) for i in range(concurrent_connections)]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Analyze results
        total_messages = sum(r["messages_sent"] for r in results)
        average_duration = statistics.mean(r["duration"] for r in results)
        throughput = total_messages / total_time
        
        # Performance assertions
        assert all(r["messages_sent"] == messages_per_connection for r in results)
        assert all(r["messages_received"] == messages_per_connection for r in results)
        assert throughput > 1000, f"Message throughput too low: {throughput} messages/sec"
        assert average_duration < 5.0, f"Average connection duration too high: {average_duration}s"
    
    def test_concurrent_audio_processing(self, audio_codec, performance_test_data):
        """Test concurrent audio processing performance"""
        concurrent_processors = 10
        chunks_per_processor = 100
        chunk_size = performance_test_data['audio_chunk_size']
        
        def process_audio_chunks(processor_id):
            """Process audio chunks in a thread"""
            processing_times = []
            
            for i in range(chunks_per_processor):
                # Generate test audio data
                import numpy as np
                pcm_samples = np.random.randint(-32768, 32767, chunk_size, dtype=np.int16)
                pcm_data = pcm_samples.tobytes()
                
                # Time the processing
                start = time.time()
                
                # Convert to mulaw
                mulaw_data = audio_codec.pcm_to_mulaw(pcm_data)
                
                # Encode to base64
                encoded = audio_codec.encode_base64(mulaw_data)
                
                # Decode from base64
                decoded = audio_codec.decode_base64(encoded)
                
                # Convert back to PCM
                converted_pcm = audio_codec.mulaw_to_pcm(decoded)
                
                processing_time = time.time() - start
                processing_times.append(processing_time)
            
            return {
                "processor_id": processor_id,
                "chunks_processed": chunks_per_processor,
                "processing_times": processing_times,
                "total_time": sum(processing_times)
            }
        
        # Run concurrent audio processing
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=concurrent_processors) as executor:
            futures = [executor.submit(process_audio_chunks, i) for i in range(concurrent_processors)]
            results = [future.result() for future in as_completed(futures)]
        
        total_time = time.time() - start_time
        
        # Analyze results
        total_chunks = sum(r["chunks_processed"] for r in results)
        all_processing_times = []
        for r in results:
            all_processing_times.extend(r["processing_times"])
        
        average_processing_time = statistics.mean(all_processing_times)
        max_processing_time = max(all_processing_times)
        throughput = total_chunks / total_time
        
        # Performance assertions
        assert average_processing_time < 0.01, f"Average processing time too high: {average_processing_time}s"
        assert max_processing_time < 0.05, f"Max processing time too high: {max_processing_time}s"
        assert throughput > 1000, f"Processing throughput too low: {throughput} chunks/sec"
    
    def test_rate_limiting_performance(self, mock_redis):
        """Test rate limiting performance under load"""
        middleware = SecurityMiddleware(redis_client=mock_redis)
        concurrent_requests = 100
        requests_per_client = 50
        
        # Mock Redis responses to allow some requests and block others
        call_count = 0
        def mock_execute():
            nonlocal call_count
            call_count += 1
            if call_count % 5 == 0:  # Block every 5th request
                return [61, True]  # Over limit
            else:
                return [call_count % 60, True]  # Under limit
        
        mock_redis.pipeline.return_value.execute.side_effect = mock_execute
        
        def make_requests(client_id):
            """Make multiple requests from a single client"""
            results = []
            client_ip = f"192.168.1.{client_id % 255}"
            
            for _ in range(requests_per_client):
                start = time.time()
                allowed = middleware._check_rate_limit(client_ip)
                duration = time.time() - start
                results.append({"allowed": allowed, "duration": duration})
            
            return results
        
        # Run concurrent rate limiting checks
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=concurrent_requests) as executor:
            futures = [executor.submit(make_requests, i) for i in range(concurrent_requests)]
            all_results = []
            for future in as_completed(futures):
                all_results.extend(future.result())
        
        total_time = time.time() - start_time
        
        # Analyze results
        total_requests = len(all_results)
        allowed_requests = sum(1 for r in all_results if r["allowed"])
        blocked_requests = total_requests - allowed_requests
        durations = [r["duration"] for r in all_results]
        
        average_duration = statistics.mean(durations)
        max_duration = max(durations)
        throughput = total_requests / total_time
        
        # Performance assertions
        assert average_duration < 0.01, f"Average rate limit check too slow: {average_duration}s"
        assert max_duration < 0.1, f"Max rate limit check too slow: {max_duration}s"
        assert throughput > 5000, f"Rate limiting throughput too low: {throughput} checks/sec"
        assert blocked_requests > 0, "Rate limiting should block some requests"


class TestMemoryUsage:
    """Memory usage and leak detection tests"""
    
    def test_stream_manager_memory_usage(self, stream_manager):
        """Test StreamManager memory usage with many streams"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Create many streams
        stream_ids = []
        for i in range(1000):
            stream_id = stream_manager.create_stream(
                from_number=f"+123456{i:04d}",
                to_number=f"+987654{i:04d}"
            )
            stream_ids.append(stream_id)
        
        after_creation_memory = process.memory_info().rss
        
        # Generate sequence numbers for all streams
        for stream_id in stream_ids:
            for _ in range(100):
                stream_manager.get_next_sequence_number(stream_id)
        
        after_usage_memory = process.memory_info().rss
        
        # Clean up all streams
        for stream_id in stream_ids:
            stream_manager.cleanup_stream(stream_id)
        
        # Force garbage collection
        gc.collect()
        
        after_cleanup_memory = process.memory_info().rss
        
        # Memory usage assertions
        creation_overhead = after_creation_memory - initial_memory
        usage_overhead = after_usage_memory - after_creation_memory
        cleanup_efficiency = (after_usage_memory - after_cleanup_memory) / after_usage_memory
        
        # Each stream should use reasonable amount of memory
        memory_per_stream = creation_overhead / 1000
        assert memory_per_stream < 10000, f"Memory per stream too high: {memory_per_stream} bytes"
        
        # Memory should be mostly reclaimed after cleanup
        assert cleanup_efficiency > 0.8, f"Memory cleanup inefficient: {cleanup_efficiency * 100}%"
    
    def test_audio_buffer_memory_efficiency(self):
        """Test AudioBuffer memory efficiency"""
        import psutil
        import gc
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Create large audio buffer
        buffer = AudioBuffer(max_size=1000000)  # 1MB buffer
        
        # Fill buffer with data
        chunk_size = 1600  # 200ms at 8000Hz
        for i in range(100):
            data = bytes([i % 256] * chunk_size)
            buffer.add_data(data)
        
        after_filling_memory = process.memory_info().rss
        
        # Read all data from buffer
        while not buffer.is_empty():
            buffer.get_data(chunk_size)
        
        # Clear buffer
        buffer.clear()
        del buffer
        
        # Force garbage collection
        gc.collect()
        
        after_cleanup_memory = process.memory_info().rss
        
        # Memory usage assertions
        max_overhead = after_filling_memory - initial_memory
        cleanup_efficiency = (after_filling_memory - after_cleanup_memory) / after_filling_memory
        
        # Buffer should not use excessive memory beyond its max_size
        assert max_overhead < 2000000, f"Buffer memory overhead too high: {max_overhead} bytes"
        
        # Memory should be reclaimed after cleanup
        assert cleanup_efficiency > 0.5, f"Buffer memory cleanup inefficient: {cleanup_efficiency * 100}%"


class TestLatencyMeasurement:
    """Latency measurement tests for real-time requirements"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_latency(self, audio_codec, performance_test_data):
        """Test end-to-end audio processing latency"""
        iterations = 100
        latencies = []
        expected_max_latency_ms = performance_test_data['expected_latency_ms']
        
        for i in range(iterations):
            # Generate test audio
            import numpy as np
            pcm_samples = np.random.randint(-32768, 32767, 160, dtype=np.int16)
            pcm_data = pcm_samples.tobytes()
            
            # Measure end-to-end processing time
            start = time.time()
            
            # Simulate full pipeline
            mulaw_data = audio_codec.pcm_to_mulaw(pcm_data)
            encoded = audio_codec.encode_base64(mulaw_data)
            
            # Simulate network transmission (mock delay)
            await asyncio.sleep(0.001)  # 1ms network delay
            
            decoded = audio_codec.decode_base64(encoded)
            converted_pcm = audio_codec.mulaw_to_pcm(decoded)
            
            end = time.time()
            latency_ms = (end - start) * 1000
            latencies.append(latency_ms)
        
        # Analyze latency statistics
        average_latency = statistics.mean(latencies)
        max_latency = max(latencies)
        p95_latency = sorted(latencies)[int(0.95 * len(latencies))]
        p99_latency = sorted(latencies)[int(0.99 * len(latencies))]
        
        # Latency assertions for real-time requirements
        assert average_latency < expected_max_latency_ms, f"Average latency too high: {average_latency}ms"
        assert p95_latency < expected_max_latency_ms * 1.5, f"P95 latency too high: {p95_latency}ms"
        assert p99_latency < expected_max_latency_ms * 2, f"P99 latency too high: {p99_latency}ms"
        assert max_latency < expected_max_latency_ms * 3, f"Max latency too high: {max_latency}ms"
    
    def test_websocket_message_latency(self):
        """Test WebSocket message processing latency"""
        message_sizes = [100, 500, 1000, 5000, 10000]  # bytes
        latencies_by_size = {}
        
        for size in message_sizes:
            latencies = []
            
            for _ in range(100):
                # Generate test message
                import json
                message = {
                    "event": "media",
                    "sequenceNumber": "1",
                    "streamSid": "MZ1234567890",
                    "media": {
                        "payload": "A" * size  # Simulate payload of given size
                    }
                }
                
                # Measure serialization/deserialization time
                start = time.time()
                
                # Serialize
                json_str = json.dumps(message)
                
                # Deserialize
                parsed = json.loads(json_str)
                
                end = time.time()
                latency_ms = (end - start) * 1000
                latencies.append(latency_ms)
            
            latencies_by_size[size] = {
                "average": statistics.mean(latencies),
                "max": max(latencies),
                "p95": sorted(latencies)[int(0.95 * len(latencies))]
            }
        
        # Latency should scale reasonably with message size
        for size, stats in latencies_by_size.items():
            assert stats["average"] < 10, f"JSON processing too slow for {size} bytes: {stats['average']}ms"
            assert stats["p95"] < 20, f"P95 JSON processing too slow for {size} bytes: {stats['p95']}ms"


class TestScalabilityLimits:
    """Tests to determine scalability limits"""
    
    def test_maximum_concurrent_streams(self, stream_manager):
        """Test maximum number of concurrent streams the system can handle"""
        max_streams_to_test = 10000
        batch_size = 1000
        
        successful_streams = 0
        stream_ids = []
        
        try:
            for batch in range(0, max_streams_to_test, batch_size):
                start_time = time.time()
                
                # Create batch of streams
                for i in range(batch_size):
                    stream_id = stream_manager.create_stream(
                        from_number=f"+{batch + i:010d}",
                        to_number=f"+{batch + i + 1000000:010d}"
                    )
                    stream_ids.append(stream_id)
                    successful_streams += 1
                
                batch_time = time.time() - start_time
                
                # Check if performance is degrading significantly
                streams_per_second = batch_size / batch_time
                if streams_per_second < 100:  # Arbitrary threshold
                    break
                
        except Exception as e:
            # Expected to hit limits eventually
            pass
        
        finally:
            # Clean up streams
            for stream_id in stream_ids:
                try:
                    stream_manager.cleanup_stream(stream_id)
                except:
                    pass
        
        # Should be able to handle at least 1000 concurrent streams
        assert successful_streams >= 1000, f"Could only handle {successful_streams} concurrent streams"
    
    def test_audio_processing_throughput_limit(self, audio_codec):
        """Test maximum audio processing throughput"""
        chunk_size = 160  # 20ms at 8000Hz
        test_duration = 5  # seconds
        
        processed_chunks = 0
        start_time = time.time()
        
        while time.time() - start_time < test_duration:
            # Generate test audio
            import numpy as np
            pcm_samples = np.random.randint(-32768, 32767, chunk_size, dtype=np.int16)
            pcm_data = pcm_samples.tobytes()
            
            # Process audio
            mulaw_data = audio_codec.pcm_to_mulaw(pcm_data)
            encoded = audio_codec.encode_base64(mulaw_data)
            decoded = audio_codec.decode_base64(encoded)
            converted_pcm = audio_codec.mulaw_to_pcm(decoded)
            
            processed_chunks += 1
        
        total_time = time.time() - start_time
        chunks_per_second = processed_chunks / total_time
        
        # Should be able to process audio much faster than real-time
        # Real-time requires 50 chunks/second (20ms chunks)
        assert chunks_per_second > 500, f"Audio processing too slow: {chunks_per_second} chunks/sec"
    
    @pytest.mark.slow
    def test_sustained_load_stability(self, stream_manager, audio_codec):
        """Test system stability under sustained load"""
        test_duration = 30  # seconds (mark as slow test)
        
        # Track metrics over time
        metrics = {
            "stream_creation_times": [],
            "audio_processing_times": [],
            "memory_usage": [],
            "errors": []
        }
        
        import psutil
        process = psutil.Process()
        
        start_time = time.time()
        
        while time.time() - start_time < test_duration:
            try:
                # Create and cleanup streams continuously
                stream_start = time.time()
                stream_id = stream_manager.create_stream()
                
                # Generate some sequence numbers
                for _ in range(10):
                    stream_manager.get_next_sequence_number(stream_id)
                
                stream_manager.cleanup_stream(stream_id)
                stream_time = time.time() - stream_start
                metrics["stream_creation_times"].append(stream_time)
                
                # Process audio continuously
                audio_start = time.time()
                import numpy as np
                pcm_samples = np.random.randint(-32768, 32767, 160, dtype=np.int16)
                pcm_data = pcm_samples.tobytes()
                
                mulaw_data = audio_codec.pcm_to_mulaw(pcm_data)
                encoded = audio_codec.encode_base64(mulaw_data)
                decoded = audio_codec.decode_base64(encoded)
                converted_pcm = audio_codec.mulaw_to_pcm(decoded)
                
                audio_time = time.time() - audio_start
                metrics["audio_processing_times"].append(audio_time)
                
                # Track memory usage
                memory_mb = process.memory_info().rss / 1024 / 1024
                metrics["memory_usage"].append(memory_mb)
                
                # Small delay to prevent overwhelming the system
                time.sleep(0.001)
                
            except Exception as e:
                metrics["errors"].append(str(e))
        
        # Analyze stability metrics
        if metrics["stream_creation_times"]:
            avg_stream_time = statistics.mean(metrics["stream_creation_times"])
            max_stream_time = max(metrics["stream_creation_times"])
            
            assert avg_stream_time < 0.1, f"Stream operations degraded: {avg_stream_time}s average"
            assert max_stream_time < 1.0, f"Stream operations had spikes: {max_stream_time}s max"
        
        if metrics["audio_processing_times"]:
            avg_audio_time = statistics.mean(metrics["audio_processing_times"])
            max_audio_time = max(metrics["audio_processing_times"])
            
            assert avg_audio_time < 0.01, f"Audio processing degraded: {avg_audio_time}s average"
            assert max_audio_time < 0.1, f"Audio processing had spikes: {max_audio_time}s max"
        
        if metrics["memory_usage"]:
            initial_memory = metrics["memory_usage"][0]
            final_memory = metrics["memory_usage"][-1]
            memory_growth = final_memory - initial_memory
            
            # Memory should not grow excessively during sustained load
            assert memory_growth < 100, f"Excessive memory growth: {memory_growth}MB"
        
        # Should have minimal errors during sustained load
        error_rate = len(metrics["errors"]) / len(metrics["stream_creation_times"]) if metrics["stream_creation_times"] else 0
        assert error_rate < 0.01, f"Too many errors during sustained load: {error_rate * 100}%"