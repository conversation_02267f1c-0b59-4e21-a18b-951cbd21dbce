"""
Tests for platform integration adapters (<PERSON><PERSON><PERSON>, <PERSON> Meet, Smartflo)
"""

import pytest
import asyncio
import json
from unittest.mock import MagicMock, AsyncMock, patch

from integrations import (
    BaseIntegration, TwilioIntegration, GoogleMeetIntegration, 
    SmartfloIntegration, IntegrationConfig, IntegrationError
)


class TestIntegrationConfig:
    """Test IntegrationConfig functionality"""
    
    def test_integration_config_creation(self):
        """Test creating IntegrationConfig with all parameters"""
        config = IntegrationConfig(
            platform_name="test_platform",
            api_key="test_api_key",
            api_secret="test_api_secret",
            endpoint_url="https://api.test.com",
            webhook_url="https://webhook.test.com",
            custom_parameters={"param1": "value1"}
        )
        
        assert config.platform_name == "test_platform"
        assert config.api_key == "test_api_key"
        assert config.api_secret == "test_api_secret"
        assert config.endpoint_url == "https://api.test.com"
        assert config.webhook_url == "https://webhook.test.com"
        assert config.custom_parameters["param1"] == "value1"
    
    def test_integration_config_minimal(self):
        """Test creating IntegrationConfig with minimal parameters"""
        config = IntegrationConfig(
            platform_name="minimal_test",
            api_key="key123"
        )
        
        assert config.platform_name == "minimal_test"
        assert config.api_key == "key123"
        assert config.api_secret is None
        assert config.endpoint_url is None
        assert config.webhook_url is None
        assert config.custom_parameters == {}
    
    def test_integration_config_to_dict(self):
        """Test converting IntegrationConfig to dictionary"""
        config = IntegrationConfig(
            platform_name="test",
            api_key="key",
            api_secret="secret"
        )
        
        config_dict = config.to_dict()
        
        assert config_dict["platform_name"] == "test"
        assert config_dict["api_key"] == "key"
        assert config_dict["api_secret"] == "secret"


class TestBaseIntegration:
    """Test BaseIntegration abstract class functionality"""
    
    def test_base_integration_initialization(self, integration_config):
        """Test BaseIntegration initialization"""
        base = BaseIntegration(integration_config)
        
        assert base.config == integration_config
        assert base.is_connected == False
        assert base.session_data == {}
    
    @pytest.mark.asyncio
    async def test_base_integration_abstract_methods(self, integration_config):
        """Test that abstract methods raise NotImplementedError"""
        base = BaseIntegration(integration_config)
        
        with pytest.raises(NotImplementedError):
            await base.connect()
        
        with pytest.raises(NotImplementedError):
            await base.disconnect()
        
        with pytest.raises(NotImplementedError):
            await base.start_call("test")
        
        with pytest.raises(NotImplementedError):
            await base.end_call("test")
    
    def test_base_integration_validation(self, integration_config):
        """Test configuration validation"""
        base = BaseIntegration(integration_config)
        
        # Test with valid config
        assert base.validate_config() == True
        
        # Test with invalid config (missing api_key)
        invalid_config = IntegrationConfig(platform_name="test")
        base_invalid = BaseIntegration(invalid_config)
        assert base_invalid.validate_config() == False


class TestTwilioIntegration:
    """Test Twilio integration functionality"""
    
    def test_twilio_integration_initialization(self):
        """Test Twilio integration initialization"""
        config = IntegrationConfig(
            platform_name="twilio",
            api_key="twilio_account_sid",
            api_secret="twilio_auth_token",
            endpoint_url="https://api.twilio.com",
            custom_parameters={"phone_number": "+**********"}
        )
        
        twilio = TwilioIntegration(config)
        
        assert twilio.config.platform_name == "twilio"
        assert twilio.account_sid == "twilio_account_sid"
        assert twilio.auth_token == "twilio_auth_token"
        assert twilio.phone_number == "+**********"
    
    @pytest.mark.asyncio
    async def test_twilio_connect(self, mock_aiohttp_session):
        """Test Twilio connection establishment"""
        config = IntegrationConfig(
            platform_name="twilio",
            api_key="account_sid",
            api_secret="auth_token"
        )
        
        twilio = TwilioIntegration(config)
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            result = await twilio.connect()
            
            assert result == True
            assert twilio.is_connected == True
    
    @pytest.mark.asyncio
    async def test_twilio_start_call(self, mock_aiohttp_session):
        """Test starting a Twilio call"""
        config = IntegrationConfig(
            platform_name="twilio",
            api_key="account_sid",
            api_secret="auth_token",
            custom_parameters={"phone_number": "+**********"}
        )
        
        twilio = TwilioIntegration(config)
        twilio.is_connected = True
        
        # Mock successful call creation response
        mock_response = {
            "sid": "CA**********abcdef",
            "from": "+**********",
            "to": "+**********",
            "status": "initiated"
        }
        
        mock_aiohttp_session.post.return_value.__aenter__.return_value.json.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            call_id = await twilio.start_call("+**********")
            
            assert call_id == "CA**********abcdef"
            mock_aiohttp_session.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_twilio_end_call(self, mock_aiohttp_session):
        """Test ending a Twilio call"""
        config = IntegrationConfig(
            platform_name="twilio",
            api_key="account_sid",
            api_secret="auth_token"
        )
        
        twilio = TwilioIntegration(config)
        twilio.is_connected = True
        
        mock_response = {"status": "completed"}
        mock_aiohttp_session.post.return_value.__aenter__.return_value.json.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            result = await twilio.end_call("CA**********abcdef")
            
            assert result == True
            mock_aiohttp_session.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_twilio_webhook_handling(self):
        """Test Twilio webhook handling"""
        config = IntegrationConfig(
            platform_name="twilio",
            api_key="account_sid",
            webhook_url="https://example.com/webhook"
        )
        
        twilio = TwilioIntegration(config)
        
        # Mock webhook data
        webhook_data = {
            "CallSid": "CA**********abcdef",
            "CallStatus": "completed",
            "From": "+**********",
            "To": "+**********"
        }
        
        result = await twilio.handle_webhook(webhook_data)
        
        assert result["call_id"] == "CA**********abcdef"
        assert result["status"] == "completed"


class TestGoogleMeetIntegration:
    """Test Google Meet integration functionality"""
    
    def test_google_meet_initialization(self):
        """Test Google Meet integration initialization"""
        config = IntegrationConfig(
            platform_name="google_meet",
            api_key="google_client_id",
            api_secret="google_client_secret",
            custom_parameters={
                "access_token": "access_token_123",
                "refresh_token": "refresh_token_456"
            }
        )
        
        meet = GoogleMeetIntegration(config)
        
        assert meet.config.platform_name == "google_meet"
        assert meet.client_id == "google_client_id"
        assert meet.client_secret == "google_client_secret"
        assert meet.access_token == "access_token_123"
        assert meet.refresh_token == "refresh_token_456"
    
    @pytest.mark.asyncio
    async def test_google_meet_connect(self, mock_aiohttp_session):
        """Test Google Meet connection with OAuth"""
        config = IntegrationConfig(
            platform_name="google_meet",
            api_key="client_id",
            api_secret="client_secret",
            custom_parameters={"access_token": "token123"}
        )
        
        meet = GoogleMeetIntegration(config)
        
        # Mock token validation response
        mock_response = {"expires_in": 3600, "scope": "calendar"}
        mock_aiohttp_session.get.return_value.__aenter__.return_value.json.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            result = await meet.connect()
            
            assert result == True
            assert meet.is_connected == True
    
    @pytest.mark.asyncio
    async def test_google_meet_create_meeting(self, mock_aiohttp_session):
        """Test creating a Google Meet meeting"""
        config = IntegrationConfig(
            platform_name="google_meet",
            api_key="client_id",
            custom_parameters={"access_token": "token123"}
        )
        
        meet = GoogleMeetIntegration(config)
        meet.is_connected = True
        
        # Mock meeting creation response
        mock_response = {
            "id": "abc-defg-hij",
            "hangoutLink": "https://meet.google.com/abc-defg-hij",
            "summary": "One call Call"
        }
        
        mock_aiohttp_session.post.return_value.__aenter__.return_value.json.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            meeting_id = await meet.start_call()
            
            assert meeting_id == "abc-defg-hij"
            mock_aiohttp_session.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_google_meet_token_refresh(self, mock_aiohttp_session):
        """Test Google Meet token refresh"""
        config = IntegrationConfig(
            platform_name="google_meet",
            api_key="client_id",
            api_secret="client_secret",
            custom_parameters={"refresh_token": "refresh123"}
        )
        
        meet = GoogleMeetIntegration(config)
        
        # Mock token refresh response
        mock_response = {
            "access_token": "new_access_token",
            "expires_in": 3600
        }
        
        mock_aiohttp_session.post.return_value.__aenter__.return_value.json.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            new_token = await meet.refresh_access_token()
            
            assert new_token == "new_access_token"
            assert meet.access_token == "new_access_token"


class TestSmartfloIntegration:
    """Test Smartflo integration functionality"""
    
    def test_smartflo_initialization(self):
        """Test Smartflo integration initialization"""
        config = IntegrationConfig(
            platform_name="smartflo",
            api_key="smartflo_api_key",
            api_secret="smartflo_secret",
            endpoint_url="https://api.smartflo.com/v1"
        )
        
        smartflo = SmartfloIntegration(config)
        
        assert smartflo.config.platform_name == "smartflo"
        assert smartflo.api_key == "smartflo_api_key"
        assert smartflo.api_secret == "smartflo_secret"
        assert smartflo.endpoint_url == "https://api.smartflo.com/v1"
    
    @pytest.mark.asyncio
    async def test_smartflo_connect(self, mock_aiohttp_session):
        """Test Smartflo connection establishment"""
        config = IntegrationConfig(
            platform_name="smartflo",
            api_key="api_key_123",
            api_secret="secret_456",
            endpoint_url="https://api.smartflo.com/v1"
        )
        
        smartflo = SmartfloIntegration(config)
        
        # Mock authentication response
        mock_response = {
            "access_token": "sf_token_123",
            "token_type": "bearer",
            "expires_in": 7200
        }
        
        mock_aiohttp_session.post.return_value.__aenter__.return_value.json.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            result = await smartflo.connect()
            
            assert result == True
            assert smartflo.is_connected == True
            assert smartflo.access_token == "sf_token_123"
    
    @pytest.mark.asyncio
    async def test_smartflo_start_call(self, mock_aiohttp_session):
        """Test starting a Smartflo call"""
        config = IntegrationConfig(
            platform_name="smartflo",
            api_key="api_key",
            endpoint_url="https://api.smartflo.com/v1"
        )
        
        smartflo = SmartfloIntegration(config)
        smartflo.is_connected = True
        smartflo.access_token = "sf_token_123"
        
        # Mock call initiation response
        mock_response = {
            "call_id": "SF_CALL_123456",
            "status": "initiated",
            "destination": "+**********"
        }
        
        mock_aiohttp_session.post.return_value.__aenter__.return_value.json.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            call_id = await smartflo.start_call("+**********")
            
            assert call_id == "SF_CALL_123456"
            mock_aiohttp_session.post.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_smartflo_get_call_status(self, mock_aiohttp_session):
        """Test getting Smartflo call status"""
        config = IntegrationConfig(
            platform_name="smartflo",
            api_key="api_key",
            endpoint_url="https://api.smartflo.com/v1"
        )
        
        smartflo = SmartfloIntegration(config)
        smartflo.is_connected = True
        smartflo.access_token = "sf_token_123"
        
        # Mock call status response
        mock_response = {
            "call_id": "SF_CALL_123456",
            "status": "in_progress",
            "duration": 45,
            "quality_score": 0.95
        }
        
        mock_aiohttp_session.get.return_value.__aenter__.return_value.json.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            status = await smartflo.get_call_status("SF_CALL_123456")
            
            assert status["status"] == "in_progress"
            assert status["duration"] == 45
            assert status["quality_score"] == 0.95


class TestIntegrationErrorHandling:
    """Test error handling across all integrations"""
    
    @pytest.mark.asyncio
    async def test_connection_timeout_handling(self, mock_aiohttp_session):
        """Test handling of connection timeouts"""
        config = IntegrationConfig(
            platform_name="test",
            api_key="key"
        )
        
        # Mock timeout error
        mock_aiohttp_session.post.side_effect = asyncio.TimeoutError("Connection timeout")
        
        base = BaseIntegration(config)
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            with pytest.raises(IntegrationError):
                await base._make_request("POST", "/test", {})
    
    @pytest.mark.asyncio
    async def test_authentication_failure_handling(self, mock_aiohttp_session):
        """Test handling of authentication failures"""
        config = IntegrationConfig(
            platform_name="twilio",
            api_key="invalid_key",
            api_secret="invalid_secret"
        )
        
        twilio = TwilioIntegration(config)
        
        # Mock 401 response
        mock_response = AsyncMock()
        mock_response.status = 401
        mock_response.json.return_value = {"error": "Authentication failed"}
        mock_aiohttp_session.post.return_value.__aenter__.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            with pytest.raises(IntegrationError):
                await twilio.connect()
    
    @pytest.mark.asyncio
    async def test_rate_limit_handling(self, mock_aiohttp_session):
        """Test handling of rate limits"""
        config = IntegrationConfig(
            platform_name="smartflo",
            api_key="key"
        )
        
        smartflo = SmartfloIntegration(config)
        
        # Mock 429 rate limit response
        mock_response = AsyncMock()
        mock_response.status = 429
        mock_response.headers = {"Retry-After": "60"}
        mock_aiohttp_session.post.return_value.__aenter__.return_value = mock_response
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            with pytest.raises(IntegrationError) as exc_info:
                await smartflo._make_request("POST", "/calls", {})
            
            assert "rate limit" in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_network_error_handling(self, mock_aiohttp_session):
        """Test handling of network errors"""
        config = IntegrationConfig(
            platform_name="google_meet",
            api_key="key"
        )
        
        meet = GoogleMeetIntegration(config)
        
        # Mock network error
        mock_aiohttp_session.get.side_effect = Exception("Network unreachable")
        
        with patch('aiohttp.ClientSession', return_value=mock_aiohttp_session):
            with pytest.raises(IntegrationError):
                await meet.connect()


class TestIntegrationManager:
    """Test integration manager functionality"""
    
    def test_integration_factory(self):
        """Test creating integrations via factory method"""
        from integrations import create_integration
        
        # Test Twilio creation
        twilio_config = IntegrationConfig(platform_name="twilio", api_key="key")
        twilio = create_integration(twilio_config)
        assert isinstance(twilio, TwilioIntegration)
        
        # Test Google Meet creation
        meet_config = IntegrationConfig(platform_name="google_meet", api_key="key")
        meet = create_integration(meet_config)
        assert isinstance(meet, GoogleMeetIntegration)
        
        # Test Smartflo creation
        smartflo_config = IntegrationConfig(platform_name="smartflo", api_key="key")
        smartflo = create_integration(smartflo_config)
        assert isinstance(smartflo, SmartfloIntegration)
    
    def test_unsupported_platform(self):
        """Test handling of unsupported platforms"""
        from integrations import create_integration
        
        unsupported_config = IntegrationConfig(platform_name="unsupported", api_key="key")
        
        with pytest.raises(ValueError):
            create_integration(unsupported_config)
    
    @pytest.mark.asyncio
    async def test_multi_platform_integration(self):
        """Test managing multiple platform integrations simultaneously"""
        from integrations import IntegrationManager
        
        manager = IntegrationManager()
        
        # Add multiple platforms
        twilio_config = IntegrationConfig(platform_name="twilio", api_key="twilio_key")
        meet_config = IntegrationConfig(platform_name="google_meet", api_key="meet_key")
        
        manager.add_integration("twilio", twilio_config)
        manager.add_integration("google_meet", meet_config)
        
        # Verify integrations were added
        assert "twilio" in manager.integrations
        assert "google_meet" in manager.integrations
        assert len(manager.integrations) == 2
        
        # Test getting integration
        twilio_integration = manager.get_integration("twilio")
        assert isinstance(twilio_integration, TwilioIntegration)
    
    @pytest.mark.asyncio
    async def test_integration_health_check(self):
        """Test health checking of integrations"""
        from integrations import IntegrationManager
        
        manager = IntegrationManager()
        
        config = IntegrationConfig(platform_name="twilio", api_key="key")
        manager.add_integration("twilio", config)
        
        # Mock health check
        with patch.object(manager.integrations["twilio"], 'health_check') as mock_health:
            mock_health.return_value = {"status": "healthy", "latency": 50}
            
            health_status = await manager.check_integration_health("twilio")
            
            assert health_status["status"] == "healthy"
            assert health_status["latency"] == 50
            mock_health.assert_called_once()