"""
Tests for security middleware and authentication functionality
"""

import pytest
import time
import jwt
from datetime import datetime, timedelta
from unittest.mock import <PERSON>Mock, patch
from flask import Flask, request, g

from production.security.security_middleware import (
    SecurityMiddleware, require_api_key, require_jwt_auth, 
    require_role, generate_jwt_token
)


class TestSecurityMiddleware:
    """Test SecurityMiddleware functionality"""
    
    def test_security_middleware_initialization(self, mock_redis, mock_flask_app):
        """Test SecurityMiddleware initialization"""
        middleware = SecurityMiddleware(mock_flask_app, mock_redis)
        
        assert middleware.app == mock_flask_app
        assert middleware.redis_client == mock_redis
        assert isinstance(middleware.rate_limiters, dict)
        assert isinstance(middleware.blacklisted_ips, set)
        assert isinstance(middleware.whitelisted_ips, set)
    
    def test_init_app_configuration(self, mock_flask_app, mock_redis):
        """Test app initialization and configuration"""
        middleware = SecurityMiddleware(redis_client=mock_redis)
        middleware.init_app(mock_flask_app)
        
        # Verify configuration defaults were set
        assert mock_flask_app.config['RATE_LIMIT_PER_MINUTE'] == 60
        assert mock_flask_app.config['RATE_LIMIT_PER_HOUR'] == 1000
        assert mock_flask_app.config['MAX_FAILED_ATTEMPTS'] == 5
        assert mock_flask_app.config['LOCKOUT_DURATION_MINUTES'] == 30
    
    def test_missing_secret_key_error(self, mock_redis):
        """Test error when SECRET_KEY is missing"""
        app = Flask(__name__)
        # Don't set SECRET_KEY
        
        middleware = SecurityMiddleware(redis_client=mock_redis)
        
        with pytest.raises(ValueError, match="SECRET_KEY must be set"):
            middleware.init_app(app)
    
    def test_get_client_ip_x_forwarded_for(self, security_middleware, mock_flask_app):
        """Test getting client IP from X-Forwarded-For header"""
        with mock_flask_app.test_request_context('/', headers={'X-Forwarded-For': '*************, ********'}):
            ip = security_middleware._get_client_ip()
            assert ip == '*************'
    
    def test_get_client_ip_x_real_ip(self, security_middleware, mock_flask_app):
        """Test getting client IP from X-Real-IP header"""
        with mock_flask_app.test_request_context('/', headers={'X-Real-IP': '*************'}):
            ip = security_middleware._get_client_ip()
            assert ip == '*************'
    
    def test_get_client_ip_remote_addr(self, security_middleware, mock_flask_app):
        """Test fallback to remote_addr"""
        with mock_flask_app.test_request_context('/', environ_base={'REMOTE_ADDR': '192.168.1.300'}):
            ip = security_middleware._get_client_ip()
            assert ip == '192.168.1.300'
    
    def test_check_ip_access_whitelist(self, security_middleware):
        """Test IP access with whitelist"""
        security_middleware.whitelisted_ips.add('*************')
        
        # Whitelisted IP should be allowed
        assert security_middleware._check_ip_access('*************') == True
        
        # Non-whitelisted IP should be blocked
        assert security_middleware._check_ip_access('*************') == False
    
    def test_check_ip_access_blacklist(self, security_middleware):
        """Test IP access with blacklist"""
        security_middleware.blacklisted_ips.add('*************')
        
        # Blacklisted IP should be blocked
        assert security_middleware._check_ip_access('*************') == False
        
        # Non-blacklisted IP should be allowed
        assert security_middleware._check_ip_access('*************') == True
    
    def test_rate_limiting_with_redis(self, security_middleware, mock_redis):
        """Test rate limiting with Redis backend"""
        security_middleware.redis_client = mock_redis
        client_ip = '*************'
        
        # Mock Redis responses - first call under limit
        mock_redis.pipeline.return_value.execute.return_value = [5, True]
        
        result = security_middleware._check_rate_limit(client_ip)
        assert result == True
        
        # Mock Redis responses - over limit
        mock_redis.pipeline.return_value.execute.return_value = [61, True]
        
        result = security_middleware._check_rate_limit(client_ip)
        assert result == False
    
    def test_rate_limiting_in_memory(self, security_middleware):
        """Test rate limiting with in-memory storage"""
        security_middleware.redis_client = None
        client_ip = '*************'
        
        # First request should be allowed
        result = security_middleware._check_rate_limit(client_ip)
        assert result == True
        
        # Simulate many rapid requests
        current_time = time.time()
        minute_key = f"rate_limit:minute:{client_ip}"
        
        # Add 60 requests to reach limit
        for _ in range(60):
            security_middleware.rate_limiters[minute_key].append(current_time)
        
        # Next request should be blocked
        result = security_middleware._check_rate_limit(client_ip)
        assert result == False
    
    def test_api_key_validation_valid(self, security_middleware, mock_flask_app):
        """Test API key validation with valid key"""
        mock_flask_app.config['VALID_API_KEYS'] = ['test_api_key_1', 'test_api_key_2']
        
        with mock_flask_app.test_request_context('/', headers={'X-API-Key': 'test_api_key_1'}):
            result = security_middleware._validate_api_key()
            assert result == True
    
    def test_api_key_validation_invalid(self, security_middleware, mock_flask_app):
        """Test API key validation with invalid key"""
        mock_flask_app.config['VALID_API_KEYS'] = ['test_api_key_1', 'test_api_key_2']
        
        with mock_flask_app.test_request_context('/', headers={'X-API-Key': 'invalid_key'}):
            result = security_middleware._validate_api_key()
            assert result == False
    
    def test_api_key_validation_missing(self, security_middleware, mock_flask_app):
        """Test API key validation with missing key"""
        with mock_flask_app.test_request_context('/'):
            result = security_middleware._validate_api_key()
            assert result == False
    
    def test_jwt_token_validation_valid(self, security_middleware, mock_flask_app):
        """Test JWT token validation with valid token"""
        secret_key = mock_flask_app.config['SECRET_KEY']
        
        # Generate valid token
        payload = {
            'user_id': 'user123',
            'email': '<EMAIL>',
            'exp': datetime.utcnow() + timedelta(hours=1)
        }
        token = jwt.encode(payload, secret_key, algorithm='HS256')
        
        with mock_flask_app.test_request_context('/', headers={'Authorization': f'Bearer {token}'}):
            result = security_middleware._validate_jwt_token()
            assert result == True
            assert g.user_id == 'user123'
            assert g.user_email == '<EMAIL>'
    
    def test_jwt_token_validation_expired(self, security_middleware, mock_flask_app):
        """Test JWT token validation with expired token"""
        secret_key = mock_flask_app.config['SECRET_KEY']
        
        # Generate expired token
        payload = {
            'user_id': 'user123',
            'exp': datetime.utcnow() - timedelta(hours=1)  # Expired 1 hour ago
        }
        token = jwt.encode(payload, secret_key, algorithm='HS256')
        
        with mock_flask_app.test_request_context('/', headers={'Authorization': f'Bearer {token}'}):
            result = security_middleware._validate_jwt_token()
            assert result == False
    
    def test_jwt_token_validation_invalid(self, security_middleware, mock_flask_app):
        """Test JWT token validation with invalid token"""
        with mock_flask_app.test_request_context('/', headers={'Authorization': 'Bearer invalid_token'}):
            result = security_middleware._validate_jwt_token()
            assert result == False
    
    def test_jwt_token_validation_missing(self, security_middleware, mock_flask_app):
        """Test JWT token validation with missing token"""
        with mock_flask_app.test_request_context('/'):
            result = security_middleware._validate_jwt_token()
            assert result == False
    
    def test_request_validation_content_length(self, security_middleware, mock_flask_app):
        """Test request validation for content length"""
        mock_flask_app.config['MAX_CONTENT_LENGTH'] = 1024  # 1KB limit
        
        # Test valid content length
        with mock_flask_app.test_request_context('/', content_length=500):
            result = security_middleware._validate_request()
            assert result == True
        
        # Test oversized content
        with mock_flask_app.test_request_context('/', content_length=2048):
            result = security_middleware._validate_request()
            assert result == False
    
    def test_request_validation_malicious_url(self, security_middleware, mock_flask_app):
        """Test request validation for malicious URL patterns"""
        malicious_urls = [
            '/api/test?param=../../../etc/passwd',
            '/api/test?param=<script>alert(1)</script>',
            '/api/test?param=javascript:alert(1)',
            '/api/test?param=data:text/html,<script>alert(1)</script>'
        ]
        
        for url in malicious_urls:
            with mock_flask_app.test_request_context(url):
                result = security_middleware._validate_request()
                assert result == False
    
    def test_security_headers_added(self, security_middleware, mock_flask_app):
        """Test that security headers are added to responses"""
        with mock_flask_app.test_request_context('/'):
            from flask import make_response
            response = make_response('test')
            
            security_middleware._add_security_headers(response)
            
            # Check security headers
            assert 'X-Content-Type-Options' in response.headers
            assert response.headers['X-Content-Type-Options'] == 'nosniff'
            assert response.headers['X-Frame-Options'] == 'DENY'
            assert response.headers['X-XSS-Protection'] == '1; mode=block'
            assert 'Strict-Transport-Security' in response.headers
            assert 'Content-Security-Policy' in response.headers
    
    def test_cors_preflight_handling(self, security_middleware, mock_flask_app):
        """Test CORS preflight request handling"""
        with mock_flask_app.test_request_context('/', method='OPTIONS', headers={'Origin': 'https://localhost:3000'}):
            response = security_middleware._handle_preflight()
            
            assert 'Access-Control-Allow-Origin' in response.headers
            assert 'Access-Control-Allow-Methods' in response.headers
            assert 'Access-Control-Allow-Headers' in response.headers
    
    def test_ip_management_methods(self, security_middleware):
        """Test IP blacklist/whitelist management methods"""
        test_ip = '*************'
        
        # Test blacklist management
        security_middleware.add_to_blacklist(test_ip)
        assert test_ip in security_middleware.blacklisted_ips
        
        security_middleware.remove_from_blacklist(test_ip)
        assert test_ip not in security_middleware.blacklisted_ips
        
        # Test whitelist management
        security_middleware.add_to_whitelist(test_ip)
        assert test_ip in security_middleware.whitelisted_ips
        
        security_middleware.remove_from_whitelist(test_ip)
        assert test_ip not in security_middleware.whitelisted_ips


class TestSecurityDecorators:
    """Test security decorator functions"""
    
    def test_require_api_key_decorator_valid(self, mock_flask_app):
        """Test require_api_key decorator with valid key"""
        mock_flask_app.config['VALID_API_KEYS'] = ['test_key']
        
        @require_api_key
        def test_endpoint():
            return 'success'
        
        with mock_flask_app.test_request_context('/', headers={'X-API-Key': 'test_key'}):
            result = test_endpoint()
            assert result == 'success'
    
    def test_require_api_key_decorator_invalid(self, mock_flask_app):
        """Test require_api_key decorator with invalid key"""
        mock_flask_app.config['VALID_API_KEYS'] = ['test_key']
        
        @require_api_key
        def test_endpoint():
            return 'success'
        
        with mock_flask_app.test_request_context('/', headers={'X-API-Key': 'invalid_key'}):
            response, status_code = test_endpoint()
            assert status_code == 401
            assert 'error' in response.get_json()
    
    def test_require_jwt_auth_decorator_valid(self, mock_flask_app):
        """Test require_jwt_auth decorator with valid user"""
        @require_jwt_auth
        def test_endpoint():
            return f'user: {g.user_id}'
        
        with mock_flask_app.test_request_context('/'):
            g.user_id = 'user123'
            result = test_endpoint()
            assert result == 'user: user123'
    
    def test_require_jwt_auth_decorator_invalid(self, mock_flask_app):
        """Test require_jwt_auth decorator without authentication"""
        @require_jwt_auth
        def test_endpoint():
            return 'success'
        
        with mock_flask_app.test_request_context('/'):
            response, status_code = test_endpoint()
            assert status_code == 401
            assert 'error' in response.get_json()
    
    def test_require_role_decorator_valid(self, mock_flask_app):
        """Test require_role decorator with valid role"""
        @require_role('admin')
        def test_endpoint():
            return 'admin access'
        
        with mock_flask_app.test_request_context('/'):
            g.user_roles = ['admin', 'user']
            result = test_endpoint()
            assert result == 'admin access'
    
    def test_require_role_decorator_invalid(self, mock_flask_app):
        """Test require_role decorator with insufficient permissions"""
        @require_role('admin')
        def test_endpoint():
            return 'admin access'
        
        with mock_flask_app.test_request_context('/'):
            g.user_roles = ['user']
            response, status_code = test_endpoint()
            assert status_code == 403
            assert 'error' in response.get_json()


class TestJWTTokenGeneration:
    """Test JWT token generation functionality"""
    
    def test_generate_jwt_token_basic(self):
        """Test basic JWT token generation"""
        secret_key = 'test_secret_key'
        user_id = 'user123'
        email = '<EMAIL>'
        
        token = generate_jwt_token(user_id, email, secret_key=secret_key)
        
        # Decode and verify token
        payload = jwt.decode(token, secret_key, algorithms=['HS256'])
        
        assert payload['user_id'] == user_id
        assert payload['email'] == email
        assert 'iat' in payload
        assert 'exp' in payload
    
    def test_generate_jwt_token_with_roles(self):
        """Test JWT token generation with roles"""
        secret_key = 'test_secret_key'
        user_id = 'user123'
        email = '<EMAIL>'
        roles = ['admin', 'user']
        
        token = generate_jwt_token(user_id, email, roles=roles, secret_key=secret_key)
        
        # Decode and verify token
        payload = jwt.decode(token, secret_key, algorithms=['HS256'])
        
        assert payload['roles'] == roles
    
    def test_generate_jwt_token_custom_expiration(self):
        """Test JWT token generation with custom expiration"""
        secret_key = 'test_secret_key'
        user_id = 'user123'
        email = '<EMAIL>'
        expires_hours = 48
        
        token = generate_jwt_token(
            user_id, email, 
            secret_key=secret_key, 
            expires_hours=expires_hours
        )
        
        # Decode and verify token
        payload = jwt.decode(token, secret_key, algorithms=['HS256'])
        
        exp_time = datetime.utcfromtimestamp(payload['exp'])
        iat_time = datetime.utcfromtimestamp(payload['iat'])
        
        # Check that expiration is approximately 48 hours from issued time
        time_diff = exp_time - iat_time
        assert abs(time_diff.total_seconds() - (48 * 3600)) < 60  # Within 1 minute tolerance


class TestSecurityIntegration:
    """Integration tests for security middleware"""
    
    def test_full_security_pipeline(self, mock_flask_app, mock_redis):
        """Test complete security pipeline"""
        # Set up security middleware
        middleware = SecurityMiddleware(mock_flask_app, mock_redis)
        
        # Mock successful rate limiting
        mock_redis.pipeline.return_value.execute.return_value = [5, True]
        
        # Test endpoint that requires authentication
        @mock_flask_app.route('/api/protected')
        @require_api_key
        @require_jwt_auth
        @require_role('user')
        def protected_endpoint():
            return {'message': 'success', 'user': g.user_id}
        
        # Create valid JWT token
        secret_key = mock_flask_app.config['SECRET_KEY']
        token = generate_jwt_token('user123', '<EMAIL>', ['user'], secret_key)
        
        with mock_flask_app.test_client() as client:
            # Request with all required credentials
            response = client.get('/api/protected', headers={
                'X-API-Key': 'test_api_key_1',
                'Authorization': f'Bearer {token}'
            })
            
            # Should succeed if middleware allows it
            # (In real test, would check response.status_code == 200)
    
    def test_security_logging(self, security_middleware, mock_flask_app, caplog):
        """Test security event logging"""
        with mock_flask_app.test_request_context('/'):
            from flask import make_response, g
            
            g.client_ip = '*************'
            
            # Test logging of failed authentication
            response = make_response('', 401)
            security_middleware._log_security_event(response)
            
            assert 'Authentication failed' in caplog.text
            assert '*************' in caplog.text
    
    def test_concurrent_rate_limiting(self, security_middleware, mock_redis):
        """Test rate limiting under concurrent load"""
        import threading
        import time
        
        client_ip = '*************'
        results = []
        
        def make_request():
            result = security_middleware._check_rate_limit(client_ip)
            results.append(result)
        
        # Mock Redis to allow first 5 requests, then block
        call_count = 0
        def mock_execute():
            nonlocal call_count
            call_count += 1
            if call_count <= 5:
                return [call_count, True]
            else:
                return [61, True]  # Over limit
        
        mock_redis.pipeline.return_value.execute.side_effect = mock_execute
        
        # Simulate concurrent requests
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify rate limiting worked
        assert len(results) == 10
        assert results.count(True) <= 5  # Only first 5 should succeed
        assert results.count(False) >= 5  # Rest should be blocked
    
    def test_security_middleware_error_handling(self, security_middleware, mock_flask_app):
        """Test error handling in security middleware"""
        with mock_flask_app.test_request_context('/'):
            # Mock an exception in rate limiting
            with patch.object(security_middleware, '_check_rate_limit', side_effect=Exception("Redis error")):
                # Should not raise exception, should fail open
                result = security_middleware._check_rate_limit('*************')
                # Depending on implementation, might return True (fail open) or False (fail closed)
                assert isinstance(result, bool)