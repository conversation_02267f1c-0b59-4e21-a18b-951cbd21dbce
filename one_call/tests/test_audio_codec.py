"""
Tests for audio codec functionality and format conversion
"""

import pytest
import base64
import struct
import numpy as np
from unittest.mock import MagicMock, patch

from tutor.modules.websocket.audio_codec import AudioCodec, AudioBuffer


class TestAudioCodec:
    """Test AudioCodec functionality"""
    
    def test_audio_codec_initialization(self, audio_codec):
        """Test AudioCodec initialization with default values"""
        assert audio_codec.SAMPLE_RATE == 8000
        assert audio_codec.BIT_DEPTH == 8
        assert audio_codec.CHANNELS == 1
        assert audio_codec.CHUNK_SIZE == 160
        assert audio_codec.ENCODING == "audio/x-mulaw"
    
    def test_pcm_to_mulaw_conversion(self, audio_codec):
        """Test PCM to mulaw conversion"""
        # Create sample PCM data (16-bit signed integers)
        pcm_samples = np.array([0, 1000, -1000, 8000, -8000, 16000, -16000], dtype=np.int16)
        pcm_data = pcm_samples.tobytes()
        
        mulaw_data = audio_codec.pcm_to_mulaw(pcm_data)
        
        # Check that mulaw data has correct length (half of PCM for 16-bit to 8-bit)
        assert len(mulaw_data) == len(pcm_samples)
        
        # Check that all bytes are valid mulaw values (0-255)
        for byte in mulaw_data:
            assert 0 <= byte <= 255
    
    def test_mulaw_to_pcm_conversion(self, audio_codec):
        """Test mulaw to PCM conversion"""
        # Create sample mulaw data
        mulaw_data = bytes([0, 127, 255, 64, 192, 32, 224])
        
        pcm_data = audio_codec.mulaw_to_pcm(mulaw_data)
        
        # Check that PCM data has correct length (double for 8-bit to 16-bit)
        assert len(pcm_data) == len(mulaw_data) * 2
        
        # Verify PCM data can be interpreted as 16-bit signed integers
        pcm_samples = np.frombuffer(pcm_data, dtype=np.int16)
        assert len(pcm_samples) == len(mulaw_data)
    
    def test_round_trip_conversion(self, audio_codec):
        """Test PCM -> mulaw -> PCM round trip conversion"""
        # Create sample PCM data
        original_samples = np.array([0, 1000, -1000, 4000, -4000], dtype=np.int16)
        original_pcm = original_samples.tobytes()
        
        # Convert to mulaw and back
        mulaw_data = audio_codec.pcm_to_mulaw(original_pcm)
        converted_pcm = audio_codec.mulaw_to_pcm(mulaw_data)
        
        # Parse converted PCM
        converted_samples = np.frombuffer(converted_pcm, dtype=np.int16)
        
        # Check that we have same number of samples
        assert len(converted_samples) == len(original_samples)
        
        # Note: mulaw is lossy compression, so we check for reasonable similarity
        # rather than exact equality
        for orig, conv in zip(original_samples, converted_samples):
            if orig == 0:
                assert abs(conv) < 100  # Zero should stay close to zero
            else:
                # For non-zero values, check relative error is reasonable
                relative_error = abs(orig - conv) / abs(orig)
                assert relative_error < 0.2  # 20% tolerance for mulaw compression
    
    def test_encode_base64(self, audio_codec, sample_audio_data):
        """Test base64 encoding of audio data"""
        encoded = audio_codec.encode_base64(sample_audio_data)
        
        # Check that result is a string
        assert isinstance(encoded, str)
        
        # Check that it's valid base64
        try:
            decoded = base64.b64decode(encoded)
            assert decoded == sample_audio_data
        except Exception:
            pytest.fail("Invalid base64 encoding")
    
    def test_decode_base64(self, audio_codec, sample_audio_data):
        """Test base64 decoding of audio data"""
        # First encode the data
        encoded = base64.b64encode(sample_audio_data).decode('utf-8')
        
        # Then decode it
        decoded = audio_codec.decode_base64(encoded)
        
        assert decoded == sample_audio_data
    
    def test_decode_base64_invalid(self, audio_codec):
        """Test base64 decoding with invalid input"""
        with pytest.raises(Exception):
            audio_codec.decode_base64("invalid_base64!")
    
    def test_validate_mulaw_format_valid(self, audio_codec, sample_audio_data):
        """Test validation of valid mulaw format"""
        assert audio_codec.validate_mulaw_format(sample_audio_data) == True
    
    def test_validate_mulaw_format_empty(self, audio_codec):
        """Test validation of empty audio data"""
        assert audio_codec.validate_mulaw_format(b'') == False
    
    def test_validate_mulaw_format_wrong_size(self, audio_codec):
        """Test validation with wrong chunk size"""
        # Create data that's not a multiple of expected chunk size
        wrong_size_data = b'\x00' * 159  # One byte short of expected 160
        
        # Validation should still pass as long as it's not empty
        assert audio_codec.validate_mulaw_format(wrong_size_data) == True
    
    def test_get_audio_info(self, audio_codec):
        """Test getting audio format information"""
        info = audio_codec.get_audio_info()
        
        expected_info = {
            "encoding": "audio/x-mulaw",
            "sampleRate": 8000,
            "bitRate": 64,
            "bitDepth": 8,
            "channels": 1
        }
        
        assert info == expected_info
    
    def test_calculate_duration(self, audio_codec):
        """Test calculating audio duration from data size"""
        # 160 bytes = 20ms at 8000Hz
        duration = audio_codec.calculate_duration(160)
        assert duration == 0.02  # 20ms = 0.02s
        
        # 800 bytes = 100ms at 8000Hz
        duration = audio_codec.calculate_duration(800)
        assert duration == 0.1  # 100ms = 0.1s
    
    def test_split_into_chunks(self, audio_codec):
        """Test splitting audio data into chunks"""
        # Create data larger than chunk size
        large_data = b'\x00' * 500  # 500 bytes
        
        chunks = audio_codec.split_into_chunks(large_data)
        
        # Should have 4 chunks: 160, 160, 160, 20
        assert len(chunks) == 4
        assert len(chunks[0]) == 160
        assert len(chunks[1]) == 160
        assert len(chunks[2]) == 160
        assert len(chunks[3]) == 20  # Remainder
    
    def test_split_into_chunks_exact_size(self, audio_codec):
        """Test splitting data that's exactly chunk size"""
        exact_data = b'\x00' * 160
        
        chunks = audio_codec.split_into_chunks(exact_data)
        
        assert len(chunks) == 1
        assert len(chunks[0]) == 160


class TestAudioBuffer:
    """Test AudioBuffer functionality"""
    
    def test_audio_buffer_initialization(self):
        """Test AudioBuffer initialization"""
        buffer = AudioBuffer(max_size=1000)
        
        assert buffer.max_size == 1000
        assert buffer.get_size() == 0
        assert buffer.is_empty() == True
        assert buffer.is_full() == False
    
    def test_add_audio_data(self):
        """Test adding audio data to buffer"""
        buffer = AudioBuffer(max_size=500)
        data = b'\x00' * 160
        
        buffer.add_data(data)
        
        assert buffer.get_size() == 160
        assert buffer.is_empty() == False
    
    def test_get_audio_data(self):
        """Test getting audio data from buffer"""
        buffer = AudioBuffer()
        data1 = b'\x01' * 160
        data2 = b'\x02' * 160
        
        buffer.add_data(data1)
        buffer.add_data(data2)
        
        # Get first chunk
        retrieved = buffer.get_data(160)
        assert retrieved == data1
        assert buffer.get_size() == 160
        
        # Get second chunk
        retrieved = buffer.get_data(160)
        assert retrieved == data2
        assert buffer.get_size() == 0
    
    def test_get_partial_data(self):
        """Test getting partial data from buffer"""
        buffer = AudioBuffer()
        data = b'\x01' * 160
        
        buffer.add_data(data)
        
        # Get only first 80 bytes
        partial = buffer.get_data(80)
        assert len(partial) == 80
        assert partial == data[:80]
        assert buffer.get_size() == 80
    
    def test_get_more_than_available(self):
        """Test getting more data than available"""
        buffer = AudioBuffer()
        data = b'\x01' * 100
        
        buffer.add_data(data)
        
        # Try to get more than available
        retrieved = buffer.get_data(200)
        assert len(retrieved) == 100
        assert retrieved == data
        assert buffer.get_size() == 0
    
    def test_buffer_overflow(self):
        """Test buffer overflow handling"""
        buffer = AudioBuffer(max_size=200)
        
        # Add data that exceeds max size
        large_data = b'\x00' * 300
        buffer.add_data(large_data)
        
        # Buffer should only contain max_size amount
        assert buffer.get_size() <= 200
    
    def test_clear_buffer(self):
        """Test clearing buffer"""
        buffer = AudioBuffer()
        data = b'\x01' * 160
        
        buffer.add_data(data)
        assert buffer.get_size() == 160
        
        buffer.clear()
        assert buffer.get_size() == 0
        assert buffer.is_empty() == True
    
    def test_peek_data(self):
        """Test peeking at data without removing it"""
        buffer = AudioBuffer()
        data = b'\x01' * 160
        
        buffer.add_data(data)
        
        # Peek at data
        peeked = buffer.peek_data(80)
        assert len(peeked) == 80
        assert peeked == data[:80]
        
        # Buffer size should not change
        assert buffer.get_size() == 160
    
    def test_thread_safety(self):
        """Test thread safety of buffer operations"""
        import threading
        import time
        
        buffer = AudioBuffer(max_size=10000)
        results = []
        
        def producer():
            for i in range(10):
                data = bytes([i]) * 100
                buffer.add_data(data)
                time.sleep(0.001)
        
        def consumer():
            for i in range(10):
                data = buffer.get_data(100)
                results.append(len(data))
                time.sleep(0.001)
        
        # Start producer and consumer threads
        t1 = threading.Thread(target=producer)
        t2 = threading.Thread(target=consumer)
        
        t1.start()
        t2.start()
        
        t1.join()
        t2.join()
        
        # All data should have been processed
        assert len(results) == 10
        assert all(size == 100 for size in results)


class TestAudioCodecIntegration:
    """Integration tests for audio codec with other components"""
    
    def test_websocket_audio_processing(self, audio_codec):
        """Test audio processing for WebSocket protocol"""
        # Simulate receiving PCM audio from microphone
        pcm_data = struct.pack('<' + 'h' * 160, *range(-80, 80))  # 160 samples
        
        # Convert to protocol format
        mulaw_data = audio_codec.pcm_to_mulaw(pcm_data)
        base64_data = audio_codec.encode_base64(mulaw_data)
        
        # Simulate sending over WebSocket (would be JSON)
        websocket_payload = {
            "event": "media",
            "media": {
                "payload": base64_data
            }
        }
        
        # Simulate receiving and processing
        received_base64 = websocket_payload["media"]["payload"]
        received_mulaw = audio_codec.decode_base64(received_base64)
        received_pcm = audio_codec.mulaw_to_pcm(received_mulaw)
        
        # Verify data integrity
        assert len(received_mulaw) == 160
        assert len(received_pcm) == 320  # 160 * 2 bytes per sample
        assert audio_codec.validate_mulaw_format(received_mulaw)
    
    def test_audio_streaming_simulation(self, audio_codec):
        """Test continuous audio streaming simulation"""
        buffer = AudioBuffer(max_size=5000)
        total_duration = 0
        
        # Simulate receiving 10 chunks of audio (200ms total)
        for i in range(10):
            # Generate sample audio chunk
            samples = np.sin(2 * np.pi * 440 * np.linspace(0, 0.02, 160))  # 440Hz sine wave
            samples = (samples * 16000).astype(np.int16)  # Scale to 16-bit range
            pcm_data = samples.tobytes()
            
            # Convert to protocol format
            mulaw_data = audio_codec.pcm_to_mulaw(pcm_data)
            
            # Add to buffer
            buffer.add_data(mulaw_data)
            
            # Calculate duration
            chunk_duration = audio_codec.calculate_duration(len(mulaw_data))
            total_duration += chunk_duration
        
        # Verify total duration and buffer state
        assert abs(total_duration - 0.2) < 0.001  # 200ms ± 1ms
        assert buffer.get_size() == 1600  # 10 chunks * 160 bytes
        
        # Process all buffered audio
        processed_chunks = []
        while not buffer.is_empty():
            chunk = buffer.get_data(160)
            if chunk:
                processed_chunks.append(chunk)
        
        assert len(processed_chunks) == 10
        assert all(len(chunk) == 160 for chunk in processed_chunks)
    
    def test_format_conversion_accuracy(self, audio_codec):
        """Test accuracy of format conversions with known values"""
        # Test with known mulaw/PCM pairs
        test_cases = [
            (0, 0x00),      # Silence
            (1000, 0x87),   # Low amplitude
            (-1000, 0x07),  # Low amplitude negative
            (4000, 0xA7),   # Medium amplitude
            (-4000, 0x27),  # Medium amplitude negative
        ]
        
        for pcm_val, expected_mulaw in test_cases:
            # Convert single PCM sample to mulaw
            pcm_data = struct.pack('<h', pcm_val)
            mulaw_data = audio_codec.pcm_to_mulaw(pcm_data)
            
            # Check that mulaw value is close to expected
            # (allowing some tolerance due to compression)
            mulaw_val = mulaw_data[0]
            assert abs(mulaw_val - expected_mulaw) <= 2