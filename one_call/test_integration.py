#!/usr/bin/env python3
"""
Integration test for Google Cloud Speech in the actual system
"""
import sys
import os
sys.path.insert(0, '/home/<USER>/PycharmProjects/one_call/one_call_demo/one_call')

import time
import logging
from tutor.modules.audio.transcription_factory import TranscriptionFactory
from tutor.modules.models import models

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('IntegrationTest')

def test_google_cloud_integration():
    """Test Google Cloud Speech integration with factory pattern"""
    
    # Mock user object
    class MockUser:
        def __init__(self):
            self.session = "integration_test"
            self.mobile = "1234567890"
            
    user = MockUser()
    
    logger.info("Testing Google Cloud Speech integration via factory...")
    
    # Test factory creation
    try:
        # This should create Google Cloud Speech service since that's the default
        transcription_service = TranscriptionFactory.create_transcription_service(
            user=user, 
            logger_instance=logger, 
            service_type="google_cloud"
        )
        logger.info(f"Successfully created transcription service: {type(transcription_service).__name__}")
        
        # Test connection
        logger.info("Testing connection...")
        transcription_service.connect()
        logger.info("Connection successful!")
        
        # Simulate some audio data
        logger.info("Adding test audio data...")
        test_audio = b'\x00' * 1024  # 1KB of silence
        for i in range(5):
            transcription_service.add_audio_chunk(test_audio)
            logger.info(f"Added audio chunk {i+1}")
            time.sleep(0.1)
        
        # Wait briefly
        time.sleep(2)
        
        # Close cleanly
        logger.info("Closing connection...")
        transcription_service.is_stream_audio_data = False
        time.sleep(0.5)
        transcription_service.close()
        
        logger.info("Integration test completed successfully!")
        return True
        
    except Exception as e:
        logger.error(f"Integration test failed: {e}")
        return False

def test_fallback():
    """Test fallback mechanism"""
    # Mock user object
    class MockUser:
        def __init__(self):
            self.session = "fallback_test"
            self.mobile = "1234567890"
            
    user = MockUser()
    
    logger.info("Testing fallback mechanism...")
    
    try:
        # This should try primary service and fall back if needed
        transcription_service = TranscriptionFactory.create_with_retry(
            user=user, 
            logger_instance=logger, 
            max_retries=1
        )
        logger.info(f"Fallback test created service: {type(transcription_service).__name__}")
        
        # Close immediately
        if transcription_service:
            transcription_service.is_stream_audio_data = False
            transcription_service.close()
        
        return True
        
    except Exception as e:
        logger.error(f"Fallback test failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("Starting integration tests...")
    
    # Test Google Cloud Speech integration
    success1 = test_google_cloud_integration()
    
    # Test fallback mechanism  
    success2 = test_fallback()
    
    if success1 and success2:
        logger.info("All integration tests passed!")
    else:
        logger.error("Some integration tests failed!")