The error message you're encountering:

```
ImportError: libodbc.so.2: cannot open shared object file: No such file or directory
```

indicates that the `libodbc.so.2` library, which is required by `pyodbc`, is missing from your system. This library is part of the **UnixODBC** driver manager, which `pyodbc` relies on to communicate with ODBC databases like MS SQL Server.

To resolve this issue, you need to:

1. **Install UnixODBC driver manager.**
2. **Install the Microsoft ODBC Driver for SQL Server.**
3. **Ensure `pyodbc` is properly installed.**

Below are the detailed steps to fix the error.

---

### **1. Install UnixODBC Driver Manager**

The UnixODBC driver manager provides the `libodbc.so.2` library. Install it using your system's package manager.

**For Ubuntu/Debian systems:**

```bash
sudo apt-get update
sudo apt-get install unixodbc unixodbc-dev
```

**For CentOS/RHEL systems:**

```bash
sudo yum install unixODBC unixODBC-devel
```

**Explanation:**

- `unixodbc`: The main package providing ODBC support.
- `unixodbc-dev` or `unixODBC-devel`: Development headers required for building `pyodbc`.

---

### **2. Install the Microsoft ODBC Driver for SQL Server**

Install the appropriate Microsoft ODBC driver for your Linux distribution. This driver enables connectivity to MS SQL Server.

#### **For Ubuntu 20.04:**

```bash
# Import the public repository GPG keys
curl https://packages.microsoft.com/keys/microsoft.asc | sudo apt-key add -

# Register the Microsoft Ubuntu repository
sudo add-apt-repository "$(wget -qO- https://packages.microsoft.com/config/ubuntu/22.04/prod.list)"

# Update package lists
sudo apt-get update

# Install the ODBC driver
sudo ACCEPT_EULA=Y apt-get install msodbcsql17

# Optional: Install the command-line tools for SQL Server
sudo ACCEPT_EULA=Y apt-get install mssql-tools

# Add tools to PATH (optional)
echo 'export PATH="$PATH:/opt/mssql-tools/bin"' >> ~/.bashrc
source ~/.bashrc
```

#### **For Other Distributions:**

Refer to Microsoft's official installation guide for detailed instructions tailored to your distribution:

- [Installing the Microsoft ODBC Driver for SQL Server (Linux)](https://docs.microsoft.com/en-us/sql/connect/odbc/linux-mac/installing-the-microsoft-odbc-driver-for-sql-server)

**Explanation:**

- **GPG Keys and Repository:** Adds Microsoft's repository to your package manager.
- **ACCEPT_EULA=Y:** Automatically accepts the End-User License Agreement.

---

### **3. Reinstall `pyodbc`**

Ensure that `pyodbc` is installed after `unixodbc-dev` to properly compile the module.

```bash
# Activate your virtual environment if you have one
# source /path/to/venv/bin/activate

pip uninstall pyodbc
pip install pyodbc
```

**Explanation:**

- **Uninstall/Reinstall `pyodbc`:** Ensures that `pyodbc` is compiled against the newly installed ODBC libraries.

---

### **4. Verify the Installation**

Confirm that `libodbc.so.2` is now available:

```bash
ldconfig -p | grep libodbc.so.2
```

You should see output similar to:

```
libodbc.so.2 (libc6,x86-64) => /usr/lib/x86_64-linux-gnu/libodbc.so.2
```

---

### **5. Test Your Script Again**

Now, try running your script:

```bash
python /home/<USER>/PycharmProjects/ai-voice-mate/tutor/modules/mssql_database/database.py
```

The `ImportError` should be resolved.

---

### **Additional Tips**

- **Ensure Correct Driver Version in Connection String:**

  In your Python script, make sure the driver specified in the connection string matches the installed driver version. For example:

  ```python
  self.connection_string = (
      f"DRIVER={{ODBC Driver 17 for SQL Server}};"
      f"SERVER={server};DATABASE={database};UID={user};PWD={password};Connection Timeout={timeout}"
  )
  ```

- **Check for Multiple Python Versions:**

  Ensure that you're installing `pyodbc` for the same Python interpreter that you're using to run your script.

- **Permissions:**

  If you encounter permission issues during installation, prepend `sudo` to your commands (for system-wide installations).

- **Environment Activation:**

  If you're using a virtual environment, ensure it's activated before installing packages:

  ```bash
  source /home/<USER>/PycharmProjects/ai-voice-mate/.venv/bin/activate
  ```

---

### **Summary**

By installing the UnixODBC driver manager and the Microsoft ODBC Driver for SQL Server, you provide the necessary libraries (`libodbc.so.2`) that `pyodbc` depends on. Reinstalling `pyodbc` ensures it is correctly linked to these libraries.

---

### **References**

- [pyodbc GitHub - Installation](https://github.com/mkleehammer/pyodbc/wiki/Install)
- [Microsoft ODBC Driver for SQL Server on Linux](https://docs.microsoft.com/en-us/sql/connect/odbc/linux-mac/installing-the-microsoft-odbc-driver-for-sql-server)
- [Installing UnixODBC](http://www.unixodbc.org/)

---

Let me know if you need further assistance or if you encounter any other issues!