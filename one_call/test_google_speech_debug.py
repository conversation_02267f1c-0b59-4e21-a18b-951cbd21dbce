#!/usr/bin/env python3
"""
Test script to debug Google Cloud Speech audio data flow
"""
import time
import threading
import logging
from collections import deque

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('GoogleSpeechDebugTest')

def test_google_cloud_speech():
    """Test Google Cloud Speech with debugging"""
    try:
        import sys
        import os
        sys.path.insert(0, '/home/<USER>/PycharmProjects/one_call/one_call_demo/one_call')
        
        from tutor.modules.audio.google_cloud.realtime_transcription import RealtimeTranscription
        from tutor.modules.models import models
        
        # Mock user object
        class MockUser:
            def __init__(self):
                self.session = "debug_test"
                self.mobile = "1234567890"
                
        # Create mock user
        user = MockUser()
        
        # Initialize Google Cloud Speech
        project_id = models.env.google_cloud_project_id
        credentials_path = models.env.google_cloud_credentials_path
        
        logger.info(f"Testing Google Cloud Speech with project_id: {project_id}")
        
        transcription = RealtimeTranscription(
            project_id=project_id,
            credentials_path=credentials_path,
            user=user,
            logger=logger,
            sample_rate=16000,
            language_code="en-US"
        )
        
        # Set up callbacks
        def on_data(transcript):
            logger.info(f"TRANSCRIPTION RECEIVED: '{transcript.text}' (final: {transcript.is_final})")
            
        def on_error(error):
            logger.error(f"TRANSCRIPTION ERROR: {error}")
            
        def on_open(session):
            logger.info(f"TRANSCRIPTION SESSION OPENED: {session}")
            
        def on_close():
            logger.info("TRANSCRIPTION SESSION CLOSED")
            
        transcription.on_data(on_data)
        transcription.on_error(on_error) 
        transcription.on_open(on_open)
        transcription.on_close(on_close)
        
        # Connect
        logger.info("Connecting to Google Cloud Speech...")
        transcription.connect()
        
        # Simulate audio data (16kHz, 16-bit, mono)
        # Generate 1 second of 440Hz tone (A note)
        import numpy as np
        sample_rate = 16000
        duration = 1.0
        frequency = 440.0
        
        # Generate simple speech-like audio (varying tones to simulate speech)
        # Use multiple frequencies to better simulate human speech patterns
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        
        # Create a more complex waveform that might be recognized as speech
        speech_like_audio = (
            np.sin(2 * np.pi * 300 * t) * 0.3 +  # Base frequency
            np.sin(2 * np.pi * 600 * t) * 0.2 +  # First harmonic  
            np.sin(2 * np.pi * 900 * t) * 0.1 +  # Second harmonic
            np.random.normal(0, 0.05, len(t))     # Add some noise
        )
        audio_data = (speech_like_audio * 32767).astype(np.int16)
        
        # Convert to bytes
        test_audio = audio_data.tobytes()
        
        logger.info(f"Generated test audio: {len(test_audio)} bytes")
        
        # Pre-populate with some initial audio data to avoid timing issues
        chunk_size = 1024
        initial_chunks = test_audio[:chunk_size * 5]  # First 5 chunks
        for i in range(0, len(initial_chunks), chunk_size):
            chunk = initial_chunks[i:i + chunk_size]
            transcription.add_audio_chunk(chunk)
            logger.info(f"Pre-loaded audio chunk {i//chunk_size + 1}: {len(chunk)} bytes")
        
        # Small delay to let initial audio get processed
        time.sleep(0.5)
        
        # Feed remaining audio data in chunks
        remaining_audio = test_audio[chunk_size * 5:]
        for i in range(0, len(remaining_audio), chunk_size):
            chunk = remaining_audio[i:i + chunk_size]
            transcription.add_audio_chunk(chunk)
            chunk_num = (i // chunk_size) + 6  # Continue numbering from chunk 6
            logger.info(f"Added audio chunk {chunk_num}: {len(chunk)} bytes")
            time.sleep(0.05)  # Smaller delay - real-time requirement
        
        # Wait for processing and check for results
        logger.info("Waiting for transcription results...")
        for i in range(50):  # Wait up to 5 seconds, checking every 0.1s
            time.sleep(0.1)
            # In a real implementation, you'd check for transcript results here
            
        # Close connection properly
        logger.info("Closing transcription connection...")
        transcription.is_stream_audio_data = False  # Stop the audio streaming thread
        time.sleep(0.5)  # Give threads time to notice the flag
        transcription.close()
        
        logger.info("Test completed")
        
    except Exception as e:
        logger.error(f"Test failed: {e}", exc_info=True)

if __name__ == "__main__":
    test_google_cloud_speech()