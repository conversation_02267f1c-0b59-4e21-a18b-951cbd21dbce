#!/bin/bash

# Update Frontend Configuration Script
# This script updates the frontend package.json proxy setting based on port configuration

set -e

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PORTS_CONFIG="$SCRIPT_DIR/ports.conf"
PACKAGE_JSON="$SCRIPT_DIR/frontend/package.json"
FRONTEND_ENV="$SCRIPT_DIR/frontend/.env"

# Default port values
BACKEND_PROXY_PORT=1803
WEBSOCKET_PORT=1802

echo -e "${GREEN}[INFO]${NC} Updating frontend configuration..."

# Load port configuration
if [[ -f "$PORTS_CONFIG" ]]; then
    echo -e "${GREEN}[INFO]${NC} Loading port configuration from $PORTS_CONFIG"
    
    while IFS='=' read -r key value || [[ -n "$key" ]]; do
        # Skip comments and empty lines
        [[ $key =~ ^[[:space:]]*# ]] && continue
        [[ -z "$key" ]] && continue
        
        # Remove any whitespace
        key=$(echo "$key" | xargs)
        value=$(echo "$value" | xargs)
        
        if [[ -n "$key" && -n "$value" ]]; then
            case "$key" in
                "BACKEND_PROXY_PORT") BACKEND_PROXY_PORT="$value" ;;
                "WEBSOCKET_PORT") WEBSOCKET_PORT="$value" ;;
            esac
        fi
    done < "$PORTS_CONFIG"
    
    echo -e "${GREEN}[INFO]${NC} Port configuration loaded: Proxy=$BACKEND_PROXY_PORT, WebSocket=$WEBSOCKET_PORT"
else
    echo -e "${YELLOW}[WARN]${NC} Port configuration file not found, using default values"
fi

# Update package.json proxy setting
if [[ -f "$PACKAGE_JSON" ]]; then
    echo -e "${GREEN}[INFO]${NC} Updating package.json proxy setting..."
    
    # Create a backup
    cp "$PACKAGE_JSON" "$PACKAGE_JSON.backup"
    
    # Update the proxy setting using sed
    sed -i.tmp "s|\"proxy\": \"http://localhost:[0-9]*\"|\"proxy\": \"http://localhost:$BACKEND_PROXY_PORT\"|g" "$PACKAGE_JSON"
    rm -f "$PACKAGE_JSON.tmp"
    
    echo -e "${GREEN}[INFO]${NC} Updated proxy setting to http://localhost:$BACKEND_PROXY_PORT"
else
    echo -e "${RED}[ERROR]${NC} package.json not found at $PACKAGE_JSON"
    exit 1
fi

# Create/update frontend .env file
echo -e "${GREEN}[INFO]${NC} Updating frontend .env file..."

cat > "$FRONTEND_ENV" << EOF
# Port Configuration (Auto-generated by update-frontend-config.sh)
REACT_APP_API_PORT=1801
REACT_APP_WEBSOCKET_PORT=$WEBSOCKET_PORT
REACT_APP_FRONTEND_PORT=1800
REACT_APP_BACKEND_PROXY_PORT=$BACKEND_PROXY_PORT

# WebSocket Server Configuration (dynamically constructed from WEBSOCKET_PORT)
REACT_APP_WS_URL=ws://localhost:$WEBSOCKET_PORT

# Auto-connect to server on page load
REACT_APP_AUTO_CONNECT=true

# Audio Configuration
REACT_APP_SAMPLE_RATE=8000
REACT_APP_CHUNK_DURATION=20

# Development Settings
REACT_APP_DEBUG_AUDIO=false
REACT_APP_LOG_LEVEL=info
EOF

echo -e "${GREEN}[INFO]${NC} Frontend configuration updated successfully!"
echo -e "${GREEN}[INFO]${NC} You may need to restart the React development server for changes to take effect."