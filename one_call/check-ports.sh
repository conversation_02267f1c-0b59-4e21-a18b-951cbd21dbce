#!/bin/bash

# Port Checker Utility
# This script checks the status of One call application ports

set -e

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PORTS_CONFIG="$SCRIPT_DIR/ports.conf"

# Default port values
API_PORT=1801
WEBSOCKET_PORT=1802
FRONTEND_PORT=1800
BACKEND_PROXY_PORT=1803

# Print functions
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[One call Port Checker]${NC} $1"
}

# Load port configuration
load_port_config() {
    if [[ -f "$PORTS_CONFIG" ]]; then
        print_status "Loading port configuration from $PORTS_CONFIG"
        
        while IFS='=' read -r key value || [[ -n "$key" ]]; do
            # Skip comments and empty lines
            [[ $key =~ ^[[:space:]]*# ]] && continue
            [[ -z "$key" ]] && continue
            
            # Remove any whitespace
            key=$(echo "$key" | xargs)
            value=$(echo "$value" | xargs)
            
            if [[ -n "$key" && -n "$value" ]]; then
                case "$key" in
                    "API_PORT") API_PORT="$value" ;;
                    "WEBSOCKET_PORT") WEBSOCKET_PORT="$value" ;;
                    "FRONTEND_PORT") FRONTEND_PORT="$value" ;;
                    "BACKEND_PROXY_PORT") BACKEND_PROXY_PORT="$value" ;;
                esac
            fi
        done < "$PORTS_CONFIG"
    else
        print_warning "Port configuration file not found, using default values"
    fi
}

# Check if a port is in use
check_port() {
    local port=$1
    local service_name=$2
    
    if command -v lsof &> /dev/null; then
        local pid=$(lsof -t -i:$port 2>/dev/null || true)
        if [[ -n "$pid" ]]; then
            local process_name=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
            echo -e "${RED}✗${NC} $service_name (port $port): ${RED}IN USE${NC} by PID $pid ($process_name)"
            return 1
        else
            echo -e "${GREEN}✓${NC} $service_name (port $port): ${GREEN}AVAILABLE${NC}"
            return 0
        fi
    else
        # Fallback method using netstat
        if command -v netstat &> /dev/null; then
            if netstat -ln | grep -q ":$port "; then
                echo -e "${RED}✗${NC} $service_name (port $port): ${RED}IN USE${NC}"
                return 1
            else
                echo -e "${GREEN}✓${NC} $service_name (port $port): ${GREEN}AVAILABLE${NC}"
                return 0
            fi
        else
            echo -e "${YELLOW}?${NC} $service_name (port $port): ${YELLOW}UNKNOWN${NC} (lsof and netstat not available)"
            return 0
        fi
    fi
}

# Check all ports
check_all_ports() {
    print_header "Checking One call Application Ports"
    echo ""
    
    local all_available=true
    
    # Check each service port
    if ! check_port $API_PORT "Backend API Server"; then
        all_available=false
    fi
    
    if ! check_port $WEBSOCKET_PORT "WebSocket Server"; then
        all_available=false
    fi
    
    if ! check_port $FRONTEND_PORT "Frontend Development Server"; then
        all_available=false
    fi
    
    if ! check_port $BACKEND_PROXY_PORT "Backend Proxy"; then
        all_available=false
    fi
    
    echo ""
    
    if $all_available; then
        print_status "All ports are available! ✨"
        echo ""
        print_status "You can start the application with: ./start.sh"
    else
        print_warning "Some ports are already in use!"
        echo ""
        print_status "Options to resolve conflicts:"
        print_status "1. Stop conflicting processes with: ./stop.sh"
        print_status "2. Change port configuration in: $PORTS_CONFIG"
        print_status "3. Use different ports and run: ./update-frontend-config.sh"
    fi
}

# Show detailed port information
show_port_details() {
    print_header "One call Port Configuration"
    echo ""
    
    echo "Current port configuration:"
    echo "  • API Server:          $API_PORT"
    echo "  • WebSocket Server:    $WEBSOCKET_PORT"
    echo "  • Frontend Server:     $FRONTEND_PORT"
    echo "  • Backend Proxy:       $BACKEND_PROXY_PORT"
    echo ""
    
    if [[ -f "$PORTS_CONFIG" ]]; then
        print_status "Configuration file: $PORTS_CONFIG"
    else
        print_warning "Configuration file not found: $PORTS_CONFIG"
        print_status "Using default port values"
    fi
    echo ""
}

# Main execution
main() {
    case "${1:-check}" in
        "check")
            load_port_config
            check_all_ports
            ;;
        "details"|"info")
            load_port_config
            show_port_details
            check_all_ports
            ;;
        "help"|"-h"|"--help")
            print_header "Usage"
            echo ""
            echo "  $0 [command]"
            echo ""
            echo "Commands:"
            echo "  check     Check if ports are available (default)"
            echo "  details   Show detailed port configuration and availability"
            echo "  help      Show this help message"
            echo ""
            ;;
        *)
            print_error "Unknown command: $1"
            print_status "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"