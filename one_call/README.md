# One call

A versatile, AI-powered voice assistant designed to streamline your daily tasks, help you practice English, and integrate with your smart home devices. This project is built with Python and leverages various APIs and local models for a comprehensive assistant experience.

## ✨ Features

One call is packed with a wide range of capabilities, triggered by voice commands:

-   **Communication:**
    -   Read and send emails via Gmail.
    -   Send SMS notifications.
-   **AI Language Tutor:**
    -   Practice spoken English with an interactive AI tutor powered by modern LLMs (like Llama or Mistral).
-   **Information Retrieval:**
    -   Get weather updates, news headlines, and Wikipedia summaries.
    -   Look up word definitions.
    -   Get the current time and date.
-   **Task & Schedule Management:**
    -   Manage a to-do list.
    -   Set alarms, timers, and reminders.
    -   Schedule recurring tasks using cron jobs.
-   **System Control:**
    -   Monitor system vitals (CPU, memory).
    -   Restart or shut down the host machine.
    -   Control screen brightness.
-   **Smart Home & IoT (Conceptual):**
    -   Control lights, TV, and volume (requires integration with your specific devices).
    -   Get status from a connected vehicle.
-   **And more...**
    -   Tell jokes, get interesting facts, play music, take notes, and much more.

## 🛠️ Installation

Follow these steps to get your One call up and running.

### Prerequisites

-   Python 3.8+
-   `git`
-   (Optional but recommended) A virtual environment tool like `venv`.

### Setup

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/YourUsername/ai-voice-mate.git
    cd ai-voice-mate
    ```

2.  **Create and activate a virtual environment:**
    ```bash
    python3 -m venv .venv
    source .venv/bin/activate
    # On Windows, use: .venv\Scripts\activate
    ```

3.  **Install Python dependencies:**
    *(Note: A `requirements.txt` file is assumed. If not present, you'll need to create one from the project's dependencies.)*
    ```bash
    pip install -r requirements.txt
    ```

4.  **Install external dependencies:**
    The project may require external tools like `ffmpeg`. An installation script might be provided.
    ```bash
    # If an install.sh script is present for tools like ffmpeg
    chmod +x install.sh
    ./install.sh
    ```

5.  **Configure Environment Variables:**
    Create a `.env` file in the project root. This file will store your sensitive credentials.

    ```dotenv
    # .env file
    
    # User Info
    TITLE="Sir"
    NAME="Your Name"
    
    # Gmail Credentials (for sending/reading emails and SMS)
    # Note: You may need to create an "App Password" for this to work.
    # See: https://support.google.com/accounts/answer/185833
    GMAIL_USER="<EMAIL>"
    GMAIL_PASS="your_gmail_app_password"
    
    # Recipient Email for notifications
    RECIPIENT="<EMAIL>"
    
    # Phone Number for SMS notifications (requires carrier's email-to-SMS gateway)
    PHONE_NUMBER="<EMAIL>"
    
    # Add any other required API keys or configuration variables here
    ```

## 🚀 Usage

### ⚡ Super Quick Start (Recommended)

**One command to rule them all!** 🎯

```bash
# Complete setup: check ports + update config + start services
./setup.sh
```

That's it! The setup script will:
- ✅ Check if ports are available
- ⚙️ Update frontend configuration automatically  
- 🚀 Start both backend and frontend services
- 📊 Show you the access URLs when ready

**Additional options:**
```bash
./setup.sh --check-only    # Just check if ports are available
./setup.sh --force         # Start even if some ports are in use  
./setup.sh --help          # Show help and options
```

### Manual Control

If you prefer step-by-step control:

```bash
# Start both backend and frontend services
./start.sh

# Stop all services
./stop.sh
```

The application will be available at:
- **Frontend**: http://localhost:1800 (or your configured port)
- **Backend API**: http://localhost:1801 (or your configured port)
- **WebSocket**: ws://localhost:1802 (or your configured port)

### Manual Start

You can also start the services manually:

```bash
# Start backend only
python -m tutor

# Start frontend (in another terminal)
cd frontend
npm start
```

## ⚙️ Port Configuration

The application uses multiple ports for different services. You can customize these ports to avoid conflicts with other applications:

### Default Ports
- **Frontend**: 1800
- **Backend API**: 1801  
- **WebSocket**: 1802
- **Proxy**: 1803

### Configuration Methods

1. **Edit `ports.conf`** (recommended):
   ```bash
   API_PORT=1801
   WEBSOCKET_PORT=1802
   FRONTEND_PORT=1800
   BACKEND_PROXY_PORT=1803
   ```

2. **Set environment variables**:
   ```bash
   export API_PORT=8801
   export WEBSOCKET_PORT=8802
   ./start.sh
   ```

3. **Update frontend configuration**:
   ```bash
   ./update-frontend-config.sh
   ```

For detailed port configuration instructions, see [PORT_CONFIGURATION.md](PORT_CONFIGURATION.md).