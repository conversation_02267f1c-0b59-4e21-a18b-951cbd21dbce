# AWS Transcribe Configuration Example
# Copy this to your .env file and update with your actual credentials

# Primary transcription service (set to aws_transcribe to use A<PERSON>)
TRANSCRIPTION_SERVICE=aws_transcribe

# Enable fallback to AssemblyAI if <PERSON><PERSON> Transcribe fails
TRANSCRIPTION_FALLBACK_ENABLED=true

# AWS Transcribe Settings
AWS_TRANSCRIBE_REGION=us-east-1
AWS_TRANSCRIBE_ACCESS_KEY_ID=your_aws_access_key_here
AWS_TRANSCRIBE_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_TRANSCRIBE_LANGUAGE_CODE=en-US
AWS_TRANSCRIBE_SAMPLE_RATE=16000
AWS_TRANSCRIBE_MEDIA_ENCODING=pcm

# Optional AWS Transcribe Settings
# AWS_TRANSCRIBE_VOCABULARY_NAME=my_custom_vocabulary
# AWS_TRANSCRIBE_VOCABULARY_FILTER_NAME=my_vocabulary_filter
AWS_TRANSCRIBE_ENABLE_PARTIAL_RESULTS_STABILIZATION=true
AWS_TRANSCRIBE_PARTIAL_RESULTS_STABILITY=medium

# Existing AssemblyAI Configuration (for fallback)
ASSEMBLYAI_API_KEY=********************************

# Other existing configuration...
FLASK_ENV=production
VEHICLE_API_KEY=VEHICLETRAN1000000001
VEHICLE_TOKEN=160171
VEHICLE_UNIQUE_ID=VEHICLEID00001
VEHICLE_BASE_URL=https://app.ongo.co.in:8443/fservices/api/reqas/postdata
LOG_DATE_FORMAT=%d-%m-%Y_%H-%M
PORT=5001
