#!/usr/bin/env python3
"""
Test script to verify the fixes for AWS Transcribe issues
"""

import sys
import os
import time
import threading
from unittest.mock import Mock

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

def test_file_creation_fix():
    """Test that the FileExistsError is fixed"""
    print("Testing file creation fix...")
    
    try:
        from tutor.executors import files
        
        # Test creating the same session multiple times
        mobile = "+1234567890"
        session = "test_session_fix"
        filename = "test_file.yaml"
        
        # Create first time
        test_data = {"test": "data1"}
        files.save_yaml_to_session(mobile, session, filename, test_data)
        print("✓ First file creation successful")
        
        # Create second time (should not fail)
        test_data2 = {"test": "data2"}
        files.save_yaml_to_session(mobile, session, filename, test_data2)
        print("✓ Second file creation successful (no FileExistsError)")
        
        # Load and verify
        loaded_data = files.load_yaml_from_session(mobile, session, filename)
        if loaded_data and loaded_data.get("test") == "data2":
            print("✓ File content verification successful")
            return True
        else:
            print("✗ File content verification failed")
            return False
            
    except Exception as e:
        print(f"✗ File creation test failed: {e}")
        return False

def test_aws_transcribe_improvements():
    """Test the improved AWS Transcribe implementation"""
    print("\nTesting AWS Transcribe improvements...")
    
    try:
        from tutor.modules.audio.transcription_factory import TranscriptionFactory
        
        # Create mock user and logger
        mock_user = Mock()
        mock_user.session = "test_improvements"
        mock_user.mobile = "+1234567890"
        mock_user.loop = None
        mock_user.stream_id = None
        mock_user.bot_manager = Mock()
        mock_user.bot_manager.start_new_call.return_value = "Testing improvements"
        mock_user.sync_send = Mock()
        mock_user.end_ai_call = Mock()
        
        mock_logger = Mock()
        
        print("1. Creating AWS Transcribe service...")
        transcription_service = TranscriptionFactory.create_transcription_service(
            user=mock_user,
            logger_instance=mock_logger,
            service_type="aws_transcribe"
        )
        print("✓ Service created")
        
        print("2. Connecting...")
        transcription_service.connect()
        print("✓ Connected")
        
        print("3. Testing improved transcription...")
        
        # Capture transcripts
        transcripts = []
        def capture_transcript(transcript):
            transcripts.append(transcript)
            print(f"   📝 Received: '{transcript.text}' (Final: {getattr(transcript, 'is_final', False)})")
        
        # Override callback
        original_callback = transcription_service.on_data_callback
        transcription_service.on_data_callback = capture_transcript
        
        # Send multiple audio chunks to trigger different responses
        print("4. Streaming audio chunks...")
        for i in range(25):  # Send enough chunks to trigger multiple responses
            audio_chunk = f"test_audio_chunk_{i}".encode() * 20
            transcription_service.add_audio_chunk(audio_chunk)
            time.sleep(0.1)  # Small delay
            
            if i % 5 == 0:
                print(f"   Sent {i+1}/25 chunks...")
        
        print("5. Waiting for processing...")
        time.sleep(2)
        
        # Restore callback
        transcription_service.on_data_callback = original_callback
        
        print("6. Closing service...")
        transcription_service.close()
        
        # Verify results
        print(f"\n📊 Results:")
        print(f"   Total transcripts received: {len(transcripts)}")
        
        partial_count = sum(1 for t in transcripts if not getattr(t, 'is_final', True))
        final_count = sum(1 for t in transcripts if getattr(t, 'is_final', True))
        
        print(f"   Partial transcripts: {partial_count}")
        print(f"   Final transcripts: {final_count}")
        
        if len(transcripts) > 0:
            print("✓ Transcription improvements working")
            return True
        else:
            print("✗ No transcripts received")
            return False
            
    except Exception as e:
        print(f"✗ AWS Transcribe improvements test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_session_info_frequency():
    """Test that session info is sent less frequently"""
    print("\nTesting session info frequency...")
    
    try:
        from tutor.modules.audio.transcription_factory import TranscriptionFactory
        
        mock_user = Mock()
        mock_user.session = "test_session_info"
        mock_user.mobile = "+1234567890"
        mock_user.loop = None
        mock_user.stream_id = None
        mock_user.bot_manager = Mock()
        mock_user.bot_manager.start_new_call.return_value = "Testing session info"
        mock_user.sync_send = Mock()
        mock_user.end_ai_call = Mock()
        
        mock_logger = Mock()
        
        transcription_service = TranscriptionFactory.create_transcription_service(
            user=mock_user,
            logger_instance=mock_logger,
            service_type="aws_transcribe"
        )
        
        transcription_service.connect()
        
        # Capture session info calls
        session_info_calls = []
        def capture_session_info(info):
            session_info_calls.append(info)
            print(f"   📊 Session info: {info.audio_duration_seconds:.2f}s")
        
        original_callback = transcription_service.on_extra_session_information_callback
        transcription_service.on_extra_session_information_callback = capture_session_info
        
        # Start transcription in background
        def run_transcription():
            transcription_service.start()
        
        transcription_thread = threading.Thread(target=run_transcription, daemon=True)
        transcription_thread.start()
        
        # Run for a short time and count session info calls
        print("Running for 5 seconds to test session info frequency...")
        start_time = time.time()
        while time.time() - start_time < 5:
            # Add some audio to keep it active
            audio_chunk = b"test_audio" * 10
            transcription_service.add_audio_chunk(audio_chunk)
            time.sleep(0.1)
        
        transcription_service.close()
        
        print(f"Session info calls in 5 seconds: {len(session_info_calls)}")
        
        # Should be much fewer calls now (ideally 0 since we're not running for 30 seconds)
        if len(session_info_calls) <= 1:  # Allow for 1 call at most
            print("✓ Session info frequency improved")
            return True
        else:
            print("✗ Too many session info calls")
            return False
            
    except Exception as e:
        print(f"✗ Session info test failed: {e}")
        return False

def main():
    print("AWS Transcribe Fixes Verification")
    print("=" * 50)
    
    # Test all fixes
    test1_success = test_file_creation_fix()
    test2_success = test_aws_transcribe_improvements()
    test3_success = test_session_info_frequency()
    
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    print(f"✓ File creation fix: {'PASS' if test1_success else 'FAIL'}")
    print(f"✓ AWS Transcribe improvements: {'PASS' if test2_success else 'FAIL'}")
    print(f"✓ Session info frequency: {'PASS' if test3_success else 'FAIL'}")
    
    if test1_success and test2_success and test3_success:
        print("\n🎉 All fixes verified successfully!")
        print("The issues have been resolved:")
        print("  - FileExistsError fixed with exist_ok=True")
        print("  - AWS Transcribe now provides varied responses")
        print("  - Session info logging reduced significantly")
        return True
    else:
        print("\n❌ Some fixes need attention!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
