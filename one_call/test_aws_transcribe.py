#!/usr/bin/env python3
"""
Test script for AWS Transcribe integration
"""

import sys
import os
import time
import threading
from unittest.mock import Mock

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

def test_aws_transcribe_integration():
    """Test AWS Transcribe integration"""
    print("Testing AWS Transcribe Integration...")
    
    try:
        # Import required modules
        from tutor.modules.audio.transcription_factory import TranscriptionFactory
        from tutor.modules.models import models
        
        # Mock user and logger
        mock_user = Mock()
        mock_user.session = "test_session"
        mock_user.mobile = "1234567890"
        mock_user.loop = None
        mock_user.stream_id = None
        mock_user.bot_manager = Mock()
        mock_user.bot_manager.start_new_call.return_value = "Hello, how can I help you?"
        mock_user.sync_send = Mock()
        mock_user.end_ai_call = Mock()
        
        mock_logger = Mock()
        
        print("✓ Imports successful")
        
        # Test service availability
        available_services = TranscriptionFactory.get_available_services()
        print(f"Available services: {available_services}")
        
        # Test AWS Transcribe configuration validation
        is_valid, error_msg = TranscriptionFactory.validate_service_config("aws_transcribe")
        print(f"AWS Transcribe config validation: {is_valid}, {error_msg}")
        
        # Test creating AWS Transcribe service (if configured)
        if "aws_transcribe" in available_services:
            print("Creating AWS Transcribe service...")
            try:
                transcription_service = TranscriptionFactory.create_transcription_service(
                    user=mock_user,
                    logger_instance=mock_logger,
                    service_type="aws_transcribe"
                )
                print("✓ AWS Transcribe service created successfully")
                
                # Test connection
                if transcription_service.connect():
                    print("✓ AWS Transcribe connection successful")
                    
                    # Test adding audio chunk
                    test_audio = b"test audio data" * 100
                    transcription_service.add_audio_chunk(test_audio)
                    print("✓ Audio chunk added successfully")
                    
                    # Test close
                    transcription_service.close()
                    print("✓ AWS Transcribe service closed successfully")
                else:
                    print("✗ AWS Transcribe connection failed")
                    
            except Exception as e:
                print(f"✗ Error creating AWS Transcribe service: {e}")
        else:
            print("AWS Transcribe not available (credentials not configured)")
            
        # Test fallback mechanism
        print("\nTesting fallback mechanism...")
        try:
            # Force AWS Transcribe as primary with fallback enabled
            transcription_service = TranscriptionFactory.create_transcription_service(
                user=mock_user,
                logger_instance=mock_logger,
                service_type="aws_transcribe",
                enable_fallback=True
            )
            print("✓ Fallback mechanism working")
        except Exception as e:
            print(f"✓ Fallback triggered as expected: {e}")
            
        print("\n✓ AWS Transcribe integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """Test configuration settings"""
    print("\nTesting Configuration...")
    
    try:
        from tutor.modules.models import models
        
        # Test AWS Transcribe configuration
        print(f"AWS Transcribe region: {models.env.aws_transcribe_region}")
        print(f"AWS Transcribe language: {models.env.aws_transcribe_language_code}")
        print(f"AWS Transcribe sample rate: {models.env.aws_transcribe_sample_rate}")
        print(f"Transcription service pattern: {models.env.transcription_service}")
        
        # Test if aws_transcribe is in the allowed pattern
        try:
            field_info = models.env.__class__.model_fields['transcription_service']
            pattern = getattr(field_info, 'pattern', None)
            if pattern and "aws_transcribe" in pattern:
                print("✓ aws_transcribe is included in transcription service pattern")
            else:
                print("✗ aws_transcribe not in transcription service pattern")
        except Exception as e:
            print(f"Could not check pattern: {e}")
            # Try to create a service to verify it's supported
            try:
                TranscriptionFactory.validate_service_config("aws_transcribe")
                print("✓ aws_transcribe service type is supported")
            except:
                print("✗ aws_transcribe service type not supported")
            
        print("✓ Configuration test completed")
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("AWS Transcribe Integration Test")
    print("=" * 50)
    
    # Test configuration
    config_success = test_configuration()
    
    # Test integration
    integration_success = test_aws_transcribe_integration()
    
    if config_success and integration_success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed!")
        sys.exit(1)
