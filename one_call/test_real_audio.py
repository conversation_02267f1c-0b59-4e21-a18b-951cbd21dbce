#!/usr/bin/env python3
"""
Test Google Cloud Speech with real audio sample to verify transcription responses
"""
import sys
import os
sys.path.insert(0, '/home/<USER>/PycharmProjects/one_call/one_call_demo/one_call')

import time
import logging
import wave
import numpy as np
from scipy.io import wavfile
from scipy import signal

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('RealAudioTest')

def load_and_prepare_audio(file_path):
    """Load and prepare real audio file for Google Cloud Speech"""
    try:
        # Try using scipy first
        sample_rate, audio_data = wavfile.read(file_path)
        logger.info(f"Loaded audio file: {file_path}")
        logger.info(f"Original sample rate: {sample_rate} Hz")
        logger.info(f"Audio shape: {audio_data.shape}")
        logger.info(f"Audio dtype: {audio_data.dtype}")
        logger.info(f"Duration: {len(audio_data) / sample_rate:.2f} seconds")
        
        # Convert to mono if stereo
        if len(audio_data.shape) > 1:
            if audio_data.shape[1] == 2:  # Stereo
                audio_data = np.mean(audio_data, axis=1)
                logger.info("Converted stereo to mono")
            else:
                audio_data = audio_data[:, 0]  # Take first channel
                logger.info(f"Took first channel from {audio_data.shape[1]} channels")
        
        # Convert to 16-bit if needed
        if audio_data.dtype != np.int16:
            if audio_data.dtype == np.float32 or audio_data.dtype == np.float64:
                # Float audio is typically -1.0 to 1.0, convert to int16
                audio_data = (audio_data * 32767).astype(np.int16)
                logger.info("Converted float audio to int16")
            elif audio_data.dtype == np.int32:
                # Scale down to int16
                audio_data = (audio_data // 65536).astype(np.int16)
                logger.info("Converted int32 audio to int16")
        
        # Resample to 16kHz if needed (Google Cloud Speech requirement)
        if sample_rate != 16000:
            # Calculate resampling parameters
            num_samples = int(len(audio_data) * 16000 / sample_rate)
            audio_data = signal.resample(audio_data, num_samples).astype(np.int16)
            logger.info(f"Resampled from {sample_rate} Hz to 16000 Hz")
            sample_rate = 16000
        
        logger.info(f"Final audio: {len(audio_data)} samples, {sample_rate} Hz, {audio_data.dtype}")
        logger.info(f"Final duration: {len(audio_data) / sample_rate:.2f} seconds")
        
        return audio_data.tobytes(), sample_rate
        
    except Exception as e:
        logger.error(f"Error loading audio file {file_path}: {e}")
        return None, None

def create_simple_speech_audio():
    """Create a simple speech-like audio pattern as fallback"""
    logger.info("Creating synthetic speech-like audio...")
    
    sample_rate = 16000
    duration = 3.0  # 3 seconds
    
    # Create speech-like patterns with formants
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # Simulate speech with varying frequency and amplitude
    speech_audio = np.zeros_like(t)
    
    # Add multiple frequency components to simulate formants
    for i, (freq, amp) in enumerate([(200, 0.3), (800, 0.2), (2000, 0.1)]):
        # Modulate frequency slightly to simulate natural speech variation
        freq_modulation = freq + 50 * np.sin(2 * np.pi * 0.5 * t)  # Slow frequency variation
        speech_audio += amp * np.sin(2 * np.pi * freq_modulation * t)
    
    # Add envelope to simulate words/pauses
    envelope = np.ones_like(t)
    for start_time in [0.5, 1.5, 2.5]:  # Three "words"
        word_start = int(start_time * sample_rate)
        word_end = int((start_time + 0.6) * sample_rate)
        if word_end < len(envelope):
            # Create word envelope
            word_length = word_end - word_start
            word_envelope = np.hanning(word_length)
            envelope[word_start:word_end] *= word_envelope
        
        # Silence between words
        silence_start = word_end
        silence_end = min(int((start_time + 0.8) * sample_rate), len(envelope))
        envelope[silence_start:silence_end] *= 0.1
    
    speech_audio *= envelope
    
    # Convert to int16
    speech_audio = (speech_audio * 32767 * 0.8).astype(np.int16)  # Scale to prevent clipping
    
    logger.info(f"Created synthetic speech audio: {len(speech_audio)} samples, {duration:.2f} seconds")
    return speech_audio.tobytes(), sample_rate

def test_with_real_audio():
    """Test Google Cloud Speech with real audio sample"""
    try:
        from tutor.modules.audio.google_cloud.realtime_transcription import RealtimeTranscription
        from tutor.modules.models import models
        
        # Try to load real audio file first
        audio_files_to_try = [
            "/home/<USER>/PycharmProjects/one_call/one_call_demo/one_call/tutor/test/audio/3427830048436826170.wav",
            "/home/<USER>/PycharmProjects/one_call/one_call_demo/one_call/tutor/test/temp_audio.wav"
        ]
        
        audio_data = None
        sample_rate = None
        
        for audio_file in audio_files_to_try:
            if os.path.exists(audio_file):
                logger.info(f"Trying to load: {audio_file}")
                audio_data, sample_rate = load_and_prepare_audio(audio_file)
                if audio_data is not None:
                    logger.info(f"Successfully loaded: {audio_file}")
                    break
            else:
                logger.warning(f"File not found: {audio_file}")
        
        # Fallback to synthetic audio if real audio loading fails
        if audio_data is None:
            logger.warning("Could not load real audio files, using synthetic speech")
            audio_data, sample_rate = create_simple_speech_audio()
        
        # Mock user object
        class MockUser:
            def __init__(self):
                self.session = "real_audio_test"
                self.mobile = "1234567890"
                
        user = MockUser()
        
        # Create transcription service
        project_id = models.env.google_cloud_project_id
        credentials_path = models.env.google_cloud_credentials_path
        
        logger.info(f"Creating Google Cloud Speech transcription service...")
        transcription = RealtimeTranscription(
            project_id=project_id,
            credentials_path=credentials_path,
            user=user,
            logger=logger,
            sample_rate=16000,
            language_code="en-US"
        )
        
        # Track transcription results
        transcription_results = []
        
        # Set up callbacks to capture results
        def on_data(transcript):
            transcription_results.append(transcript)
            logger.info(f"🎯 TRANSCRIPTION RECEIVED: '{transcript.text}' (final: {transcript.is_final})")
            
        def on_error(error):
            logger.error(f"❌ TRANSCRIPTION ERROR: {error}")
            
        def on_open(session):
            logger.info(f"✅ TRANSCRIPTION SESSION OPENED: {session}")
            
        def on_close():
            logger.info("🔒 TRANSCRIPTION SESSION CLOSED")
            
        transcription.on_data(on_data)
        transcription.on_error(on_error) 
        transcription.on_open(on_open)
        transcription.on_close(on_close)
        
        # Connect
        logger.info("🔌 Connecting to Google Cloud Speech...")
        transcription.connect()
        
        # Wait a moment for connection to stabilize
        time.sleep(1)
        
        # Feed real audio data in chunks
        logger.info("🎵 Sending real audio data...")
        chunk_size = 1600  # 100ms chunks at 16kHz
        audio_sent = 0
        
        for i in range(0, len(audio_data), chunk_size):
            chunk = audio_data[i:i + chunk_size]
            transcription.add_audio_chunk(chunk)
            audio_sent += len(chunk)
            
            # Log progress
            progress = (i / len(audio_data)) * 100
            if i % (chunk_size * 10) == 0:  # Every second
                logger.info(f"📊 Audio progress: {progress:.1f}% ({audio_sent} bytes sent)")
            
            # Realistic timing - send chunks at real-time rate
            time.sleep(0.1)  # 100ms chunks
        
        logger.info("📤 Finished sending audio data")
        
        # Wait for final transcription results
        logger.info("⏳ Waiting for transcription results...")
        for i in range(100):  # Wait up to 10 seconds
            time.sleep(0.1)
            if transcription_results:
                logger.info(f"📝 Got {len(transcription_results)} transcription results so far")
        
        # Give it a bit more time for final results
        time.sleep(3)
        
        # Close connection properly
        logger.info("🔚 Closing transcription connection...")
        transcription.is_stream_audio_data = False
        time.sleep(0.5)
        transcription.close()
        
        # Report results
        logger.info("=" * 50)
        logger.info("🎯 FINAL TRANSCRIPTION RESULTS:")
        logger.info("=" * 50)
        
        if transcription_results:
            for i, result in enumerate(transcription_results):
                result_type = "FINAL" if result.is_final else "PARTIAL"
                logger.info(f"{i+1}. [{result_type}] '{result.text}'")
                
            logger.info(f"✅ SUCCESS: Received {len(transcription_results)} transcription results!")
            return True
        else:
            logger.warning("⚠️  No transcription results received - may indicate an issue")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    logger.info("🚀 Starting Google Cloud Speech real audio test...")
    success = test_with_real_audio()
    
    if success:
        logger.info("🎉 Real audio transcription test PASSED!")
    else:
        logger.error("💥 Real audio transcription test FAILED!")