/**
 * AudioWorklet processor for handling microphone audio data
 * Replaces the deprecated ScriptProcessorNode
 */

class AudioProcessorWorklet extends AudioWorkletProcessor {
  constructor() {
    super();
    this.bufferSize = 4096;
    this.buffer = new Float32Array(this.bufferSize);
    this.bufferIndex = 0;
  }

  process(inputs, outputs, parameters) {
    const input = inputs[0];
    
    if (input && input.length > 0) {
      const inputChannel = input[0];
      
      // Accumulate audio data in buffer
      for (let i = 0; i < inputChannel.length; i++) {
        this.buffer[this.bufferIndex] = inputChannel[i];
        this.bufferIndex++;
        
        // When buffer is full, send it to main thread
        if (this.bufferIndex >= this.bufferSize) {
          // Create a copy of the buffer to send
          const bufferCopy = new Float32Array(this.buffer);
          
          // Send audio data to main thread
          this.port.postMessage({
            type: 'audioData',
            buffer: bufferCopy,
            sampleRate: globalThis.sampleRate
          });
          
          // Reset buffer
          this.bufferIndex = 0;
        }
      }
    }
    
    // Keep the processor alive
    return true;
  }
}

registerProcessor('audio-processor-worklet', AudioProcessorWorklet);
