<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .connecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .connect-btn {
            background-color: #007bff;
            color: white;
        }
        .disconnect-btn {
            background-color: #dc3545;
            color: white;
        }
        #log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    
    <div id="status" class="status disconnected">
        Status: Disconnected
    </div>
    
    <button id="connectBtn" class="connect-btn" onclick="connect()">Connect</button>
    <button id="disconnectBtn" class="disconnect-btn" onclick="disconnect()">Disconnect</button>
    
    <h3>Connection Log:</h3>
    <div id="log"></div>
    
    <script>
        let ws = null;
        let connectionState = 'disconnected';
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(state, message) {
            connectionState = state;
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${state}`;
            statusDiv.textContent = `Status: ${message}`;
            log(`Status changed to: ${message}`);
        }
        
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('Already connected');
                return;
            }
            
            updateStatus('connecting', 'Connecting...');
            log('Attempting to connect to ws://localhost:5010');
            
            ws = new WebSocket('ws://localhost:5010');
            
            ws.onopen = function() {
                log('WebSocket connected successfully');
                updateStatus('connecting', 'Connected - Registering user...');
                
                // Send user registration
                const registerMessage = {
                    type: 'user_register',
                    sessionId: 'test_html_session_' + Date.now(),
                    userData: {
                        name: 'HTML Test User',
                        mobile: '+1234567890',
                        userId: 'html_test_user_' + Date.now(),
                        sessionType: 'call',
                        target: 'assistant'
                    }
                };
                
                ws.send(JSON.stringify(registerMessage));
                log('Sent user registration message');
            };
            
            ws.onmessage = function(event) {
                log('Received message: ' + event.data);
                
                try {
                    const data = JSON.parse(event.data);
                    
                    if (data.type === 'user_registered') {
                        updateStatus('connected', 'Connected and Registered');
                        log('User registration successful!');
                    } else if (data.type === 'error') {
                        updateStatus('disconnected', 'Error: ' + data.message);
                        log('Server error: ' + data.message);
                    }
                } catch (e) {
                    log('Error parsing message: ' + e.message);
                }
            };
            
            ws.onclose = function(event) {
                log(`WebSocket closed: code=${event.code}, reason=${event.reason}`);
                updateStatus('disconnected', 'Disconnected');
            };
            
            ws.onerror = function(error) {
                log('WebSocket error: ' + error);
                updateStatus('disconnected', 'Connection Error');
            };
        }
        
        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
            updateStatus('disconnected', 'Disconnected');
        }
        
        // Auto-connect on page load
        window.onload = function() {
            log('Page loaded - starting auto-connect');
            connect();
        };
    </script>
</body>
</html>
