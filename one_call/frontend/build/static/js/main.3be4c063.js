/*! For license information please see main.3be4c063.js.LICENSE.txt */
(()=>{var e={43:(e,t,n)=>{"use strict";e.exports=n(202)},153:(e,t,n)=>{"use strict";var r=n(43),a=Symbol.for("react.element"),l=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,l={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,r)&&!s.hasOwnProperty(r)&&(l[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===l[r]&&(l[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:l,_owner:i.current}}t.Fragment=l,t.jsx=u,t.jsxs=u},202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var x=b.prototype=new v;x.constructor=b,m(x,y.prototype),x.isPureReactComponent=!0;var w=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},C={key:!0,ref:!0,__self:!0,__source:!0};function E(e,t,r){var a,l={},o=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(o=""+t.key),t)k.call(t,a)&&!C.hasOwnProperty(a)&&(l[a]=t[a]);var s=arguments.length-2;if(1===s)l.children=r;else if(1<s){for(var u=Array(s),c=0;c<s;c++)u[c]=arguments[c+2];l.children=u}if(e&&e.defaultProps)for(a in s=e.defaultProps)void 0===l[a]&&(l[a]=s[a]);return{$$typeof:n,type:e,key:o,ref:i,props:l,_owner:S.current}}function N(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var _=/\/+/g;function M(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(e){return t[e]})}(""+e.key):t.toString(36)}function P(e,t,a,l,o){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s=!1;if(null===e)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case n:case r:s=!0}}if(s)return o=o(s=e),e=""===l?"."+M(s,0):l,w(o)?(a="",null!=e&&(a=e.replace(_,"$&/")+"/"),P(o,t,a,"",function(e){return e})):null!=o&&(N(o)&&(o=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(o,a+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(_,"$&/")+"/")+e)),t.push(o)),1;if(s=0,l=""===l?".":l+":",w(e))for(var u=0;u<e.length;u++){var c=l+M(i=e[u],u);s+=P(i,t,a,c,o)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(i=e.next()).done;)s+=P(i=i.value,t,a,c=l+M(i,u++),o);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function z(e,t,n){if(null==e)return e;var r=[],a=0;return P(e,r,"","",function(e){return t.call(n,e,a++)}),r}function j(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)},function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var R={current:null},T={transition:null},F={ReactCurrentDispatcher:R,ReactCurrentBatchConfig:T,ReactCurrentOwner:S};function D(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:z,forEach:function(e,t,n){z(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return z(e,function(){t++}),t},toArray:function(e){return z(e,function(e){return e})||[]},only:function(e){if(!N(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=o,t.PureComponent=b,t.StrictMode=l,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=F,t.act=D,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),l=e.key,o=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(o=t.ref,i=S.current),void 0!==t.key&&(l=""+t.key),e.type&&e.type.defaultProps)var s=e.type.defaultProps;for(u in t)k.call(t,u)&&!C.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==s?s[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){s=Array(u);for(var c=0;c<u;c++)s[c]=arguments[c+2];a.children=s}return{$$typeof:n,type:e.type,key:l,ref:o,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=E,t.createFactory=function(e){var t=E.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=N,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:j}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=T.transition;T.transition={};try{e()}finally{T.transition=t}},t.unstable_act=D,t.useCallback=function(e,t){return R.current.useCallback(e,t)},t.useContext=function(e){return R.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return R.current.useDeferredValue(e)},t.useEffect=function(e,t){return R.current.useEffect(e,t)},t.useId=function(){return R.current.useId()},t.useImperativeHandle=function(e,t,n){return R.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return R.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return R.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return R.current.useMemo(e,t)},t.useReducer=function(e,t,n){return R.current.useReducer(e,t,n)},t.useRef=function(e){return R.current.useRef(e)},t.useState=function(e){return R.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return R.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return R.current.useTransition()},t.version="18.3.1"},234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<l(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,o=a>>>1;r<o;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>l(s,n))u<a&&0>l(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>l(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function l(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var o=performance;t.unstable_now=function(){return o.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function x(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function w(e){if(g=!1,x(e),!m)if(null!==r(u))m=!0,T(k);else{var t=r(c);null!==t&&F(w,t.startTime-e)}}function k(e,n){m=!1,g&&(g=!1,v(N),N=-1),h=!0;var l=p;try{for(x(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!P());){var o=f.callback;if("function"===typeof o){f.callback=null,p=f.priorityLevel;var i=o(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(u)&&a(u),x(n)}else a(u);f=r(u)}if(null!==f)var s=!0;else{var d=r(c);null!==d&&F(w,d.startTime-n),s=!1}return s}finally{f=null,p=l,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,C=!1,E=null,N=-1,_=5,M=-1;function P(){return!(t.unstable_now()-M<_)}function z(){if(null!==E){var e=t.unstable_now();M=e;var n=!0;try{n=E(!0,e)}finally{n?S():(C=!1,E=null)}}else C=!1}if("function"===typeof b)S=function(){b(z)};else if("undefined"!==typeof MessageChannel){var j=new MessageChannel,R=j.port2;j.port1.onmessage=z,S=function(){R.postMessage(null)}}else S=function(){y(z,0)};function T(e){E=e,C||(C=!0,S())}function F(e,n){N=y(function(){e(t.unstable_now())},n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,T(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,l){var o=t.unstable_now();switch("object"===typeof l&&null!==l?l="number"===typeof(l=l.delay)&&0<l?o+l:o:l=o,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:l,expirationTime:i=l+i,sortIndex:-1},l>o?(e.sortIndex=l,n(c,e),null===r(u)&&e===r(c)&&(g?(v(N),N=-1):g=!0,F(w,l-o))):(e.sortIndex=i,n(u,e),m||h||(m=!0,T(k))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},391:(e,t,n)=>{"use strict";var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},507:function(e){e.exports=function(){"use strict";class e{constructor(e){this.init(e)}init(e){const t={inputCodec:"Int16",channels:1,sampleRate:8e3,flushTime:1e3,fftSize:2048};this.option=Object.assign({},t,e),this.samples=new Float32Array,this.interval=setInterval(this.flush.bind(this),this.option.flushTime),this.convertValue=this.getConvertValue(),this.typedArray=this.getTypedArray(),this.initAudioContext(),this.bindAudioContextEvent()}getConvertValue(){const e={Int8:128,Int16:32768,Int32:2147483648,Float32:1};if(!e[this.option.inputCodec])throw new Error("wrong codec.please input one of these codecs:Int8,Int16,Int32,Float32");return e[this.option.inputCodec]}getTypedArray(){const e={Int8:Int8Array,Int16:Int16Array,Int32:Int32Array,Float32:Float32Array};if(!e[this.option.inputCodec])throw new Error("wrong codec.please input one of these codecs:Int8,Int16,Int32,Float32");return e[this.option.inputCodec]}initAudioContext(){this.audioCtx=new(window.AudioContext||window.webkitAudioContext),this.gainNode=this.audioCtx.createGain(),this.gainNode.gain.value=.1,this.gainNode.connect(this.audioCtx.destination),this.startTime=this.audioCtx.currentTime,this.analyserNode=this.audioCtx.createAnalyser(),this.analyserNode.fftSize=this.option.fftSize}static isTypedArray(e){return e.byteLength&&e.buffer&&e.buffer.constructor==ArrayBuffer||e.constructor==ArrayBuffer}isSupported(t){if(!e.isTypedArray(t))throw new Error("\u8bf7\u4f20\u5165ArrayBuffer\u6216\u8005\u4efb\u610fTypedArray");return!0}feed(e){this.isSupported(e),e=this.getFormattedValue(e);const t=new Float32Array(this.samples.length+e.length);t.set(this.samples,0),t.set(e,this.samples.length),this.samples=t}getFormattedValue(e){e=e.constructor==ArrayBuffer?new this.typedArray(e):new this.typedArray(e.buffer);let t=new Float32Array(e.length);for(let n=0;n<e.length;n++)t[n]=e[n]/this.convertValue;return t}volume(e){this.gainNode.gain.value=e}destroy(){this.interval&&clearInterval(this.interval),this.samples=null,this.audioCtx.close(),this.audioCtx=null}flush(){if(!this.samples.length)return;const e=this;var t=this.audioCtx.createBufferSource();"function"===typeof this.option.onended&&(t.onended=function(t){e.option.onended(this,t)});const n=this.samples.length/this.option.channels,r=this.audioCtx.createBuffer(this.option.channels,n,this.option.sampleRate);for(let a=0;a<this.option.channels;a++){const e=r.getChannelData(a);let t=a,l=50;for(let r=0;r<n;r++)e[r]=this.samples[t],r<50&&(e[r]=e[r]*r/50),r>=n-51&&(e[r]=e[r]*l--/50),t+=this.option.channels}this.startTime<this.audioCtx.currentTime&&(this.startTime=this.audioCtx.currentTime),t.buffer=r,t.connect(this.gainNode),t.connect(this.analyserNode),t.start(this.startTime),this.startTime+=r.duration,this.samples=new Float32Array}async pause(){await this.audioCtx.suspend()}async continue(){await this.audioCtx.resume()}bindAudioContextEvent(){const e=this;"function"===typeof e.option.onstatechange&&(this.audioCtx.onstatechange=function(t){e.audioCtx&&e.option.onstatechange(this,t,e.audioCtx.state)})}}return e}()},579:(e,t,n)=>{"use strict";e.exports=n(153)},730:(e,t,n)=>{"use strict";var r=n(43),a=n(853);function l(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var o=new Set,i={};function s(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(i[e]=t,e=0;e<t.length;e++)o.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,a,l,o){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=o}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){g[e]=new m(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){g[e]=new m(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){g[e]=new m(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){g[e]=new m(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){g[e]=new m(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)});var y=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)}),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)});var x=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,w=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),C=Symbol.for("react.strict_mode"),E=Symbol.for("react.profiler"),N=Symbol.for("react.provider"),_=Symbol.for("react.context"),M=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),z=Symbol.for("react.suspense_list"),j=Symbol.for("react.memo"),R=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var T=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var F=Symbol.iterator;function D(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=F&&e[F]||e["@@iterator"])?e:null}var L,A=Object.assign;function I(e){if(void 0===L)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);L=t&&t[1]||""}return"\n"+L+e}var O=!1;function U(e,t){if(!e||O)return"";O=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var a=u.stack.split("\n"),l=r.stack.split("\n"),o=a.length-1,i=l.length-1;1<=o&&0<=i&&a[o]!==l[i];)i--;for(;1<=o&&0<=i;o--,i--)if(a[o]!==l[i]){if(1!==o||1!==i)do{if(o--,0>--i||a[o]!==l[i]){var s="\n"+a[o].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=o&&0<=i);break}}}finally{O=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?I(e):""}function $(e){switch(e.tag){case 5:return I(e.type);case 16:return I("Lazy");case 13:return I("Suspense");case 19:return I("SuspenseList");case 0:case 2:case 15:return e=U(e.type,!1);case 11:return e=U(e.type.render,!1);case 1:return e=U(e.type,!0);default:return""}}function B(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case E:return"Profiler";case C:return"StrictMode";case P:return"Suspense";case z:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case _:return(e.displayName||"Context")+".Consumer";case N:return(e._context.displayName||"Context")+".Provider";case M:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case j:return null!==(t=e.displayName||null)?t:B(e.type)||"Memo";case R:t=e._payload,e=e._init;try{return B(e(t))}catch(n){}}return null}function V(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return B(t);case 8:return t===C?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function q(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function W(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function H(e){e._valueTracker||(e._valueTracker=function(e){var t=W(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,l.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=W(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Y(e,t){var n=t.checked;return A({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function X(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=q(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function G(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function J(e,t){G(e,t);var n=q(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,q(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+q(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(l(91));return A({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(l(92));if(te(n)){if(1<n.length)throw Error(l(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:q(n)}}function le(e,t){var n=q(t.value),r=q(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function oe(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function se(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction(function(){return ce(e,t)})}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach(function(e){he.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]})});var ye=A({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ye[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(l(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(l(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(l(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(l(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var xe=null;function we(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var ke=null,Se=null,Ce=null;function Ee(e){if(e=ba(e)){if("function"!==typeof ke)throw Error(l(280));var t=e.stateNode;t&&(t=wa(t),ke(e.stateNode,e.type,t))}}function Ne(e){Se?Ce?Ce.push(e):Ce=[e]:Se=e}function _e(){if(Se){var e=Se,t=Ce;if(Ce=Se=null,Ee(e),t)for(e=0;e<t.length;e++)Ee(t[e])}}function Me(e,t){return e(t)}function Pe(){}var ze=!1;function je(e,t,n){if(ze)return e(t,n);ze=!0;try{return Me(e,t,n)}finally{ze=!1,(null!==Se||null!==Ce)&&(Pe(),_e())}}function Re(e,t){var n=e.stateNode;if(null===n)return null;var r=wa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var Te=!1;if(c)try{var Fe={};Object.defineProperty(Fe,"passive",{get:function(){Te=!0}}),window.addEventListener("test",Fe,Fe),window.removeEventListener("test",Fe,Fe)}catch(ce){Te=!1}function De(e,t,n,r,a,l,o,i,s){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Le=!1,Ae=null,Ie=!1,Oe=null,Ue={onError:function(e){Le=!0,Ae=e}};function $e(e,t,n,r,a,l,o,i,s){Le=!1,Ae=null,De.apply(Ue,arguments)}function Be(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ve(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function qe(e){if(Be(e)!==e)throw Error(l(188))}function We(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Be(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return qe(a),e;if(o===r)return qe(a),t;o=o.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=o;else{for(var i=!1,s=a.child;s;){if(s===n){i=!0,n=a,r=o;break}if(s===r){i=!0,r=a,n=o;break}s=s.sibling}if(!i){for(s=o.child;s;){if(s===n){i=!0,n=o,r=a;break}if(s===r){i=!0,r=o,n=a;break}s=s.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(e))?He(e):null}function He(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=He(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,Ke=a.unstable_cancelCallback,Ye=a.unstable_shouldYield,Xe=a.unstable_requestPaint,Ge=a.unstable_now,Je=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,lt=null;var ot=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(it(e)/st|0)|0},it=Math.log,st=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,l=e.pingedLanes,o=268435455&n;if(0!==o){var i=o&~a;0!==i?r=dt(i):0!==(l&=o)&&(r=dt(l))}else 0!==(o=n&~a)?r=dt(o):0!==l&&(r=dt(l));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(l=t&-t)||16===a&&0!==(4194240&l)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-ot(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ot(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-ot(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function xt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var wt,kt,St,Ct,Et,Nt=!1,_t=[],Mt=null,Pt=null,zt=null,jt=new Map,Rt=new Map,Tt=[],Ft="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Dt(e,t){switch(e){case"focusin":case"focusout":Mt=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":zt=null;break;case"pointerover":case"pointerout":jt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Rt.delete(t.pointerId)}}function Lt(e,t,n,r,a,l){return null===e||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&kt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function At(e){var t=va(e.target);if(null!==t){var n=Be(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ve(n)))return e.blockedOn=t,void Et(e.priority,function(){St(n)})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function It(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Yt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&kt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);xe=r,n.target.dispatchEvent(r),xe=null,t.shift()}return!0}function Ot(e,t,n){It(e)&&n.delete(t)}function Ut(){Nt=!1,null!==Mt&&It(Mt)&&(Mt=null),null!==Pt&&It(Pt)&&(Pt=null),null!==zt&&It(zt)&&(zt=null),jt.forEach(Ot),Rt.forEach(Ot)}function $t(e,t){e.blockedOn===t&&(e.blockedOn=null,Nt||(Nt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Ut)))}function Bt(e){function t(t){return $t(t,e)}if(0<_t.length){$t(_t[0],e);for(var n=1;n<_t.length;n++){var r=_t[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Mt&&$t(Mt,e),null!==Pt&&$t(Pt,e),null!==zt&&$t(zt,e),jt.forEach(t),Rt.forEach(t),n=0;n<Tt.length;n++)(r=Tt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Tt.length&&null===(n=Tt[0]).blockedOn;)At(n),null===n.blockedOn&&Tt.shift()}var Vt=x.ReactCurrentBatchConfig,qt=!0;function Wt(e,t,n,r){var a=bt,l=Vt.transition;Vt.transition=null;try{bt=1,Qt(e,t,n,r)}finally{bt=a,Vt.transition=l}}function Ht(e,t,n,r){var a=bt,l=Vt.transition;Vt.transition=null;try{bt=4,Qt(e,t,n,r)}finally{bt=a,Vt.transition=l}}function Qt(e,t,n,r){if(qt){var a=Yt(e,t,n,r);if(null===a)qr(e,t,r,Kt,n),Dt(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Mt=Lt(Mt,e,t,n,r,a),!0;case"dragenter":return Pt=Lt(Pt,e,t,n,r,a),!0;case"mouseover":return zt=Lt(zt,e,t,n,r,a),!0;case"pointerover":var l=a.pointerId;return jt.set(l,Lt(jt.get(l)||null,e,t,n,r,a)),!0;case"gotpointercapture":return l=a.pointerId,Rt.set(l,Lt(Rt.get(l)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Dt(e,r),4&t&&-1<Ft.indexOf(e)){for(;null!==a;){var l=ba(a);if(null!==l&&wt(l),null===(l=Yt(e,t,n,r))&&qr(e,t,r,Kt,n),l===a)break;a=l}null!==a&&r.stopPropagation()}else qr(e,t,r,null,n)}}var Kt=null;function Yt(e,t,n,r){if(Kt=null,null!==(e=va(e=we(r))))if(null===(t=Be(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ve(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Xt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Je()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Gt=null,Jt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Jt,r=n.length,a="value"in Gt?Gt.value:Gt.textContent,l=a.length;for(e=0;e<r&&n[e]===a[e];e++);var o=r-e;for(t=1;t<=o&&n[r-t]===a[l-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,l){for(var o in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=l,this.currentTarget=null,e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(a):a[o]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return A(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var ln,on,sn,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=A({},un,{view:0,detail:0}),fn=an(dn),pn=A({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:En,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==sn&&(sn&&"mousemove"===e.type?(ln=e.screenX-sn.screenX,on=e.screenY-sn.screenY):on=ln=0,sn=e),ln)},movementY:function(e){return"movementY"in e?e.movementY:on}}),hn=an(pn),mn=an(A({},pn,{dataTransfer:0})),gn=an(A({},dn,{relatedTarget:0})),yn=an(A({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=A({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(vn),xn=an(A({},un,{data:0})),wn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Cn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function En(){return Cn}var Nn=A({},dn,{key:function(e){if(e.key){var t=wn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:En,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),_n=an(Nn),Mn=an(A({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pn=an(A({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:En})),zn=an(A({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),jn=A({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Rn=an(jn),Tn=[9,13,27,32],Fn=c&&"CompositionEvent"in window,Dn=null;c&&"documentMode"in document&&(Dn=document.documentMode);var Ln=c&&"TextEvent"in window&&!Dn,An=c&&(!Fn||Dn&&8<Dn&&11>=Dn),In=String.fromCharCode(32),On=!1;function Un(e,t){switch(e){case"keyup":return-1!==Tn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $n(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Bn=!1;var Vn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function qn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Vn[e.type]:"textarea"===t}function Wn(e,t,n,r){Ne(r),0<(t=Hr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Hn=null,Qn=null;function Kn(e){Ir(e,0)}function Yn(e){if(Q(xa(e)))return e}function Xn(e,t){if("change"===e)return t}var Gn=!1;if(c){var Jn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Jn=Zn}else Jn=!1;Gn=Jn&&(!document.documentMode||9<document.documentMode)}function tr(){Hn&&(Hn.detachEvent("onpropertychange",nr),Qn=Hn=null)}function nr(e){if("value"===e.propertyName&&Yn(Qn)){var t=[];Wn(t,Qn,e,we(e)),je(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(Hn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Yn(Qn)}function lr(e,t){if("click"===e)return Yn(t)}function or(e,t){if("input"===e||"change"===e)return Yn(t)}var ir="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function sr(e,t){if(ir(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!ir(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,l=Math.min(r.start,a);r=void 0===r.end?l:Math.min(r.end,a),!e.extend&&l>r&&(a=r,r=l,l=a),a=cr(n,l);var o=cr(n,r);a&&o&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,yr=null,vr=null,br=!1;function xr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==K(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},vr&&sr(vr,r)||(vr=r,0<(r=Hr(yr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function wr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:wr("Animation","AnimationEnd"),animationiteration:wr("Animation","AnimationIteration"),animationstart:wr("Animation","AnimationStart"),transitionend:wr("Transition","TransitionEnd")},Sr={},Cr={};function Er(e){if(Sr[e])return Sr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Cr)return Sr[e]=n[t];return e}c&&(Cr=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var Nr=Er("animationend"),_r=Er("animationiteration"),Mr=Er("animationstart"),Pr=Er("transitionend"),zr=new Map,jr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Rr(e,t){zr.set(e,t),s(t,[e])}for(var Tr=0;Tr<jr.length;Tr++){var Fr=jr[Tr];Rr(Fr.toLowerCase(),"on"+(Fr[0].toUpperCase()+Fr.slice(1)))}Rr(Nr,"onAnimationEnd"),Rr(_r,"onAnimationIteration"),Rr(Mr,"onAnimationStart"),Rr("dblclick","onDoubleClick"),Rr("focusin","onFocus"),Rr("focusout","onBlur"),Rr(Pr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),s("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),s("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),s("onBeforeInput",["compositionend","keypress","textInput","paste"]),s("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),s("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Lr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Dr));function Ar(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,o,i,s,u){if($e.apply(this,arguments),Le){if(!Le)throw Error(l(198));var c=Ae;Le=!1,Ae=null,Ie||(Ie=!0,Oe=c)}}(r,t,void 0,e),e.currentTarget=null}function Ir(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var o=r.length-1;0<=o;o--){var i=r[o],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==l&&a.isPropagationStopped())break e;Ar(a,i,u),l=s}else for(o=0;o<r.length;o++){if(s=(i=r[o]).instance,u=i.currentTarget,i=i.listener,s!==l&&a.isPropagationStopped())break e;Ar(a,i,u),l=s}}}if(Ie)throw e=Oe,Ie=!1,Oe=null,e}function Or(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Vr(t,e,2,!1),n.add(r))}function Ur(e,t,n){var r=0;t&&(r|=4),Vr(n,e,r,t)}var $r="_reactListening"+Math.random().toString(36).slice(2);function Br(e){if(!e[$r]){e[$r]=!0,o.forEach(function(t){"selectionchange"!==t&&(Lr.has(t)||Ur(t,!1,e),Ur(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$r]||(t[$r]=!0,Ur("selectionchange",!1,t))}}function Vr(e,t,n,r){switch(Xt(t)){case 1:var a=Wt;break;case 4:a=Ht;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!Te||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function qr(e,t,n,r,a){var l=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var o=r.tag;if(3===o||4===o){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===o)for(o=r.return;null!==o;){var s=o.tag;if((3===s||4===s)&&((s=o.stateNode.containerInfo)===a||8===s.nodeType&&s.parentNode===a))return;o=o.return}for(;null!==i;){if(null===(o=va(i)))return;if(5===(s=o.tag)||6===s){r=l=o;continue e}i=i.parentNode}}r=r.return}je(function(){var r=l,a=we(n),o=[];e:{var i=zr.get(e);if(void 0!==i){var s=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":s=_n;break;case"focusin":u="focus",s=gn;break;case"focusout":u="blur",s=gn;break;case"beforeblur":case"afterblur":s=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Pn;break;case Nr:case _r:case Mr:s=yn;break;case Pr:s=zn;break;case"scroll":s=fn;break;case"wheel":s=Rn;break;case"copy":case"cut":case"paste":s=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Mn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==i?i+"Capture":null:i;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Re(h,f))&&c.push(Wr(h,m,p)))),d)break;h=h.return}0<c.length&&(i=new s(i,u,null,n,a),o.push({event:i,listeners:c}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===xe||!(u=n.relatedTarget||n.fromElement)||!va(u)&&!u[ha])&&(s||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(u=(u=n.relatedTarget||n.toElement)?va(u):null)&&(u!==(d=Be(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(s=null,u=r),s!==u)){if(c=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=Mn,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?i:xa(s),p=null==u?i:xa(u),(i=new c(m,h+"leave",s,n,a)).target=d,i.relatedTarget=p,m=null,va(a)===r&&((c=new c(f,h+"enter",u,n,a)).target=p,c.relatedTarget=d,m=c),d=m,s&&u)e:{for(f=u,h=0,p=c=s;p;p=Qr(p))h++;for(p=0,m=f;m;m=Qr(m))p++;for(;0<h-p;)c=Qr(c),h--;for(;0<p-h;)f=Qr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=Qr(c),f=Qr(f)}c=null}else c=null;null!==s&&Kr(o,i,s,c,!1),null!==u&&null!==d&&Kr(o,d,u,c,!0)}if("select"===(s=(i=r?xa(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var g=Xn;else if(qn(i))if(Gn)g=or;else{g=ar;var y=rr}else(s=i.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(g=lr);switch(g&&(g=g(e,r))?Wn(o,g,n,a):(y&&y(e,i,r),"focusout"===e&&(y=i._wrapperState)&&y.controlled&&"number"===i.type&&ee(i,"number",i.value)),y=r?xa(r):window,e){case"focusin":(qn(y)||"true"===y.contentEditable)&&(gr=y,yr=r,vr=null);break;case"focusout":vr=yr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,xr(o,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":xr(o,n,a)}var v;if(Fn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Bn?Un(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(An&&"ko"!==n.locale&&(Bn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Bn&&(v=en()):(Jt="value"in(Gt=a)?Gt.value:Gt.textContent,Bn=!0)),0<(y=Hr(r,b)).length&&(b=new xn(b,e,null,n,a),o.push({event:b,listeners:y}),v?b.data=v:null!==(v=$n(n))&&(b.data=v))),(v=Ln?function(e,t){switch(e){case"compositionend":return $n(t);case"keypress":return 32!==t.which?null:(On=!0,In);case"textInput":return(e=t.data)===In&&On?null:e;default:return null}}(e,n):function(e,t){if(Bn)return"compositionend"===e||!Fn&&Un(e,t)?(e=en(),Zt=Jt=Gt=null,Bn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return An&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Hr(r,"onBeforeInput")).length&&(a=new xn("onBeforeInput","beforeinput",null,n,a),o.push({event:a,listeners:r}),a.data=v))}Ir(o,t)})}function Wr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Hr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,l=a.stateNode;5===a.tag&&null!==l&&(a=l,null!=(l=Re(e,n))&&r.unshift(Wr(e,l,a)),null!=(l=Re(e,t))&&r.push(Wr(e,l,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var l=t._reactName,o=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(null!==s&&s===r)break;5===i.tag&&null!==u&&(i=u,a?null!=(s=Re(n,l))&&o.unshift(Wr(n,s,i)):a||null!=(s=Re(n,l))&&o.push(Wr(n,s,i))),n=n.return}0!==o.length&&e.push({event:t,listeners:o})}var Yr=/\r\n?/g,Xr=/\u0000|\uFFFD/g;function Gr(e){return("string"===typeof e?e:""+e).replace(Yr,"\n").replace(Xr,"")}function Jr(e,t,n){if(t=Gr(t),Gr(e)!==t&&n)throw Error(l(425))}function Zr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,la="function"===typeof Promise?Promise:void 0,oa="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof la?function(e){return la.resolve(null).then(e).catch(ia)}:ra;function ia(e){setTimeout(function(){throw e})}function sa(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Bt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Bt(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ha="__reactContainer$"+da,ma="__reactEvents$"+da,ga="__reactListeners$"+da,ya="__reactHandles$"+da;function va(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ha]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[ha])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(l(33))}function wa(e){return e[pa]||null}var ka=[],Sa=-1;function Ca(e){return{current:e}}function Ea(e){0>Sa||(e.current=ka[Sa],ka[Sa]=null,Sa--)}function Na(e,t){Sa++,ka[Sa]=e.current,e.current=t}var _a={},Ma=Ca(_a),Pa=Ca(!1),za=_a;function ja(e,t){var n=e.type.contextTypes;if(!n)return _a;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,l={};for(a in n)l[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function Ra(e){return null!==(e=e.childContextTypes)&&void 0!==e}function Ta(){Ea(Pa),Ea(Ma)}function Fa(e,t,n){if(Ma.current!==_a)throw Error(l(168));Na(Ma,t),Na(Pa,n)}function Da(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(l(108,V(e)||"Unknown",a));return A({},n,r)}function La(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||_a,za=Ma.current,Na(Ma,e),Na(Pa,Pa.current),!0}function Aa(e,t,n){var r=e.stateNode;if(!r)throw Error(l(169));n?(e=Da(e,t,za),r.__reactInternalMemoizedMergedChildContext=e,Ea(Pa),Ea(Ma),Na(Ma,e)):Ea(Pa),Na(Pa,n)}var Ia=null,Oa=!1,Ua=!1;function $a(e){null===Ia?Ia=[e]:Ia.push(e)}function Ba(){if(!Ua&&null!==Ia){Ua=!0;var e=0,t=bt;try{var n=Ia;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ia=null,Oa=!1}catch(a){throw null!==Ia&&(Ia=Ia.slice(e+1)),Qe(Ze,Ba),a}finally{bt=t,Ua=!1}}return null}var Va=[],qa=0,Wa=null,Ha=0,Qa=[],Ka=0,Ya=null,Xa=1,Ga="";function Ja(e,t){Va[qa++]=Ha,Va[qa++]=Wa,Wa=e,Ha=t}function Za(e,t,n){Qa[Ka++]=Xa,Qa[Ka++]=Ga,Qa[Ka++]=Ya,Ya=e;var r=Xa;e=Ga;var a=32-ot(r)-1;r&=~(1<<a),n+=1;var l=32-ot(t)+a;if(30<l){var o=a-a%5;l=(r&(1<<o)-1).toString(32),r>>=o,a-=o,Xa=1<<32-ot(t)+a|n<<a|r,Ga=l+e}else Xa=1<<l|n<<a|r,Ga=e}function el(e){null!==e.return&&(Ja(e,1),Za(e,1,0))}function tl(e){for(;e===Wa;)Wa=Va[--qa],Va[qa]=null,Ha=Va[--qa],Va[qa]=null;for(;e===Ya;)Ya=Qa[--Ka],Qa[Ka]=null,Ga=Qa[--Ka],Qa[Ka]=null,Xa=Qa[--Ka],Qa[Ka]=null}var nl=null,rl=null,al=!1,ll=null;function ol(e,t){var n=ju(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function il(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,nl=e,rl=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,nl=e,rl=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ya?{id:Xa,overflow:Ga}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=ju(18,null,null,0)).stateNode=t,n.return=e,e.child=n,nl=e,rl=null,!0);default:return!1}}function sl(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ul(e){if(al){var t=rl;if(t){var n=t;if(!il(e,t)){if(sl(e))throw Error(l(418));t=ua(n.nextSibling);var r=nl;t&&il(e,t)?ol(r,n):(e.flags=-4097&e.flags|2,al=!1,nl=e)}}else{if(sl(e))throw Error(l(418));e.flags=-4097&e.flags|2,al=!1,nl=e}}}function cl(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;nl=e}function dl(e){if(e!==nl)return!1;if(!al)return cl(e),al=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=rl)){if(sl(e))throw fl(),Error(l(418));for(;t;)ol(e,t),t=ua(t.nextSibling)}if(cl(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){rl=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}rl=null}}else rl=nl?ua(e.stateNode.nextSibling):null;return!0}function fl(){for(var e=rl;e;)e=ua(e.nextSibling)}function pl(){rl=nl=null,al=!1}function hl(e){null===ll?ll=[e]:ll.push(e)}var ml=x.ReactCurrentBatchConfig;function gl(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(l(309));var r=n.stateNode}if(!r)throw Error(l(147,e));var a=r,o=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===o?t.ref:(t=function(e){var t=a.refs;null===e?delete t[o]:t[o]=e},t._stringRef=o,t)}if("string"!==typeof e)throw Error(l(284));if(!n._owner)throw Error(l(290,e))}return e}function yl(e,t){throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function vl(e){return(0,e._init)(e._payload)}function bl(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Tu(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Au(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var l=n.type;return l===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===l||"object"===typeof l&&null!==l&&l.$$typeof===R&&vl(l)===t.type)?((r=a(t,n.props)).ref=gl(e,t,n),r.return=e,r):((r=Fu(n.type,n.key,n.props,null,e.mode,r)).ref=gl(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Iu(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,l){return null===t||7!==t.tag?((t=Du(n,e.mode,r,l)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Au(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case w:return(n=Fu(t.type,t.key,t.props,null,e.mode,n)).ref=gl(e,null,t),n.return=e,n;case k:return(t=Iu(t,e.mode,n)).return=e,t;case R:return f(e,(0,t._init)(t._payload),n)}if(te(t)||D(t))return(t=Du(t,e.mode,n,null)).return=e,t;yl(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case w:return n.key===a?u(e,t,n,r):null;case k:return n.key===a?c(e,t,n,r):null;case R:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||D(n))return null!==a?null:d(e,t,n,r,null);yl(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case w:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case R:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||D(r))return d(t,e=e.get(n)||null,r,a,null);yl(t,r)}return null}function m(a,l,i,s){for(var u=null,c=null,d=l,m=l=0,g=null;null!==d&&m<i.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var y=p(a,d,i[m],s);if(null===y){null===d&&(d=g);break}e&&d&&null===y.alternate&&t(a,d),l=o(y,l,m),null===c?u=y:c.sibling=y,c=y,d=g}if(m===i.length)return n(a,d),al&&Ja(a,m),u;if(null===d){for(;m<i.length;m++)null!==(d=f(a,i[m],s))&&(l=o(d,l,m),null===c?u=d:c.sibling=d,c=d);return al&&Ja(a,m),u}for(d=r(a,d);m<i.length;m++)null!==(g=h(d,a,m,i[m],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),l=o(g,l,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach(function(e){return t(a,e)}),al&&Ja(a,m),u}function g(a,i,s,u){var c=D(s);if("function"!==typeof c)throw Error(l(150));if(null==(s=c.call(s)))throw Error(l(151));for(var d=c=null,m=i,g=i=0,y=null,v=s.next();null!==m&&!v.done;g++,v=s.next()){m.index>g?(y=m,m=null):y=m.sibling;var b=p(a,m,v.value,u);if(null===b){null===m&&(m=y);break}e&&m&&null===b.alternate&&t(a,m),i=o(b,i,g),null===d?c=b:d.sibling=b,d=b,m=y}if(v.done)return n(a,m),al&&Ja(a,g),c;if(null===m){for(;!v.done;g++,v=s.next())null!==(v=f(a,v.value,u))&&(i=o(v,i,g),null===d?c=v:d.sibling=v,d=v);return al&&Ja(a,g),c}for(m=r(a,m);!v.done;g++,v=s.next())null!==(v=h(m,a,g,v.value,u))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),i=o(v,i,g),null===d?c=v:d.sibling=v,d=v);return e&&m.forEach(function(e){return t(a,e)}),al&&Ja(a,g),c}return function e(r,l,o,s){if("object"===typeof o&&null!==o&&o.type===S&&null===o.key&&(o=o.props.children),"object"===typeof o&&null!==o){switch(o.$$typeof){case w:e:{for(var u=o.key,c=l;null!==c;){if(c.key===u){if((u=o.type)===S){if(7===c.tag){n(r,c.sibling),(l=a(c,o.props.children)).return=r,r=l;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===R&&vl(u)===c.type){n(r,c.sibling),(l=a(c,o.props)).ref=gl(r,c,o),l.return=r,r=l;break e}n(r,c);break}t(r,c),c=c.sibling}o.type===S?((l=Du(o.props.children,r.mode,s,o.key)).return=r,r=l):((s=Fu(o.type,o.key,o.props,null,r.mode,s)).ref=gl(r,l,o),s.return=r,r=s)}return i(r);case k:e:{for(c=o.key;null!==l;){if(l.key===c){if(4===l.tag&&l.stateNode.containerInfo===o.containerInfo&&l.stateNode.implementation===o.implementation){n(r,l.sibling),(l=a(l,o.children||[])).return=r,r=l;break e}n(r,l);break}t(r,l),l=l.sibling}(l=Iu(o,r.mode,s)).return=r,r=l}return i(r);case R:return e(r,l,(c=o._init)(o._payload),s)}if(te(o))return m(r,l,o,s);if(D(o))return g(r,l,o,s);yl(r,o)}return"string"===typeof o&&""!==o||"number"===typeof o?(o=""+o,null!==l&&6===l.tag?(n(r,l.sibling),(l=a(l,o)).return=r,r=l):(n(r,l),(l=Au(o,r.mode,s)).return=r,r=l),i(r)):n(r,l)}}var xl=bl(!0),wl=bl(!1),kl=Ca(null),Sl=null,Cl=null,El=null;function Nl(){El=Cl=Sl=null}function _l(e){var t=kl.current;Ea(kl),e._currentValue=t}function Ml(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Pl(e,t){Sl=e,El=Cl=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bi=!0),e.firstContext=null)}function zl(e){var t=e._currentValue;if(El!==e)if(e={context:e,memoizedValue:t,next:null},null===Cl){if(null===Sl)throw Error(l(308));Cl=e,Sl.dependencies={lanes:0,firstContext:e}}else Cl=Cl.next=e;return t}var jl=null;function Rl(e){null===jl?jl=[e]:jl.push(e)}function Tl(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Rl(t)):(n.next=a.next,a.next=n),t.interleaved=n,Fl(e,r)}function Fl(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Dl=!1;function Ll(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Al(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Il(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ol(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Ms)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Fl(e,n)}return null===(a=r.interleaved)?(t.next=t,Rl(r)):(t.next=a.next,a.next=t),r.interleaved=t,Fl(e,n)}function Ul(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function $l(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,l=null;if(null!==(n=n.firstBaseUpdate)){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===l?a=l=o:l=l.next=o,n=n.next}while(null!==n);null===l?a=l=t:l=l.next=t}else a=l=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:l,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Bl(e,t,n,r){var a=e.updateQueue;Dl=!1;var l=a.firstBaseUpdate,o=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===o?l=u:o.next=u,o=s;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==o&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s))}if(null!==l){var d=a.baseState;for(o=0,c=u=s=null,i=l;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var h=e,m=i;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=A({},d,f);break e;case 2:Dl=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=p,s=d):c=c.next=p,o|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{o|=a.lane,a=a.next}while(a!==t)}else null===l&&(a.shared.lanes=0);Ls|=o,e.lanes=o,e.memoizedState=d}}function Vl(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(l(191,a));a.call(r)}}}var ql={},Wl=Ca(ql),Hl=Ca(ql),Ql=Ca(ql);function Kl(e){if(e===ql)throw Error(l(174));return e}function Yl(e,t){switch(Na(Ql,t),Na(Hl,e),Na(Wl,ql),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:se(null,"");break;default:t=se(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ea(Wl),Na(Wl,t)}function Xl(){Ea(Wl),Ea(Hl),Ea(Ql)}function Gl(e){Kl(Ql.current);var t=Kl(Wl.current),n=se(t,e.type);t!==n&&(Na(Hl,e),Na(Wl,n))}function Jl(e){Hl.current===e&&(Ea(Wl),Ea(Hl))}var Zl=Ca(0);function eo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var to=[];function no(){for(var e=0;e<to.length;e++)to[e]._workInProgressVersionPrimary=null;to.length=0}var ro=x.ReactCurrentDispatcher,ao=x.ReactCurrentBatchConfig,lo=0,oo=null,io=null,so=null,uo=!1,co=!1,fo=0,po=0;function ho(){throw Error(l(321))}function mo(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ir(e[n],t[n]))return!1;return!0}function go(e,t,n,r,a,o){if(lo=o,oo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ro.current=null===e||null===e.memoizedState?Zo:ei,e=n(r,a),co){o=0;do{if(co=!1,fo=0,25<=o)throw Error(l(301));o+=1,so=io=null,t.updateQueue=null,ro.current=ti,e=n(r,a)}while(co)}if(ro.current=Jo,t=null!==io&&null!==io.next,lo=0,so=io=oo=null,uo=!1,t)throw Error(l(300));return e}function yo(){var e=0!==fo;return fo=0,e}function vo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===so?oo.memoizedState=so=e:so=so.next=e,so}function bo(){if(null===io){var e=oo.alternate;e=null!==e?e.memoizedState:null}else e=io.next;var t=null===so?oo.memoizedState:so.next;if(null!==t)so=t,io=e;else{if(null===e)throw Error(l(310));e={memoizedState:(io=e).memoizedState,baseState:io.baseState,baseQueue:io.baseQueue,queue:io.queue,next:null},null===so?oo.memoizedState=so=e:so=so.next=e}return so}function xo(e,t){return"function"===typeof t?t(e):t}function wo(e){var t=bo(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=io,a=r.baseQueue,o=n.pending;if(null!==o){if(null!==a){var i=a.next;a.next=o.next,o.next=i}r.baseQueue=a=o,n.pending=null}if(null!==a){o=a.next,r=r.baseState;var s=i=null,u=null,c=o;do{var d=c.lane;if((lo&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(s=u=f,i=r):u=u.next=f,oo.lanes|=d,Ls|=d}c=c.next}while(null!==c&&c!==o);null===u?i=r:u.next=s,ir(r,t.memoizedState)||(bi=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{o=a.lane,oo.lanes|=o,Ls|=o,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ko(e){var t=bo(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{o=e(o,i.action),i=i.next}while(i!==a);ir(o,t.memoizedState)||(bi=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function So(){}function Co(e,t){var n=oo,r=bo(),a=t(),o=!ir(r.memoizedState,a);if(o&&(r.memoizedState=a,bi=!0),r=r.queue,Lo(_o.bind(null,n,r,e),[e]),r.getSnapshot!==t||o||null!==so&&1&so.memoizedState.tag){if(n.flags|=2048,jo(9,No.bind(null,n,r,a,t),void 0,null),null===Ps)throw Error(l(349));0!==(30&lo)||Eo(n,t,a)}return a}function Eo(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function No(e,t,n,r){t.value=n,t.getSnapshot=r,Mo(t)&&Po(e)}function _o(e,t,n){return n(function(){Mo(t)&&Po(e)})}function Mo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ir(e,n)}catch(r){return!0}}function Po(e){var t=Fl(e,1);null!==t&&nu(t,e,1,-1)}function zo(e){var t=vo();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:xo,lastRenderedState:e},t.queue=e,e=e.dispatch=Ko.bind(null,oo,e),[t.memoizedState,e]}function jo(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=oo.updateQueue)?(t={lastEffect:null,stores:null},oo.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ro(){return bo().memoizedState}function To(e,t,n,r){var a=vo();oo.flags|=e,a.memoizedState=jo(1|t,n,void 0,void 0===r?null:r)}function Fo(e,t,n,r){var a=bo();r=void 0===r?null:r;var l=void 0;if(null!==io){var o=io.memoizedState;if(l=o.destroy,null!==r&&mo(r,o.deps))return void(a.memoizedState=jo(t,n,l,r))}oo.flags|=e,a.memoizedState=jo(1|t,n,l,r)}function Do(e,t){return To(8390656,8,e,t)}function Lo(e,t){return Fo(2048,8,e,t)}function Ao(e,t){return Fo(4,2,e,t)}function Io(e,t){return Fo(4,4,e,t)}function Oo(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Uo(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Fo(4,4,Oo.bind(null,t,e),n)}function $o(){}function Bo(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vo(e,t){var n=bo();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mo(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function qo(e,t,n){return 0===(21&lo)?(e.baseState&&(e.baseState=!1,bi=!0),e.memoizedState=n):(ir(n,t)||(n=mt(),oo.lanes|=n,Ls|=n,e.baseState=!0),t)}function Wo(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ao.transition;ao.transition={};try{e(!1),t()}finally{bt=n,ao.transition=r}}function Ho(){return bo().memoizedState}function Qo(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Yo(e))Xo(t,n);else if(null!==(n=Tl(e,t,n,r))){nu(n,e,r,eu()),Go(n,t,r)}}function Ko(e,t,n){var r=tu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yo(e))Xo(t,a);else{var l=e.alternate;if(0===e.lanes&&(null===l||0===l.lanes)&&null!==(l=t.lastRenderedReducer))try{var o=t.lastRenderedState,i=l(o,n);if(a.hasEagerState=!0,a.eagerState=i,ir(i,o)){var s=t.interleaved;return null===s?(a.next=a,Rl(t)):(a.next=s.next,s.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=Tl(e,t,a,r))&&(nu(n,e,r,a=eu()),Go(n,t,r))}}function Yo(e){var t=e.alternate;return e===oo||null!==t&&t===oo}function Xo(e,t){co=uo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Go(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Jo={readContext:zl,useCallback:ho,useContext:ho,useEffect:ho,useImperativeHandle:ho,useInsertionEffect:ho,useLayoutEffect:ho,useMemo:ho,useReducer:ho,useRef:ho,useState:ho,useDebugValue:ho,useDeferredValue:ho,useTransition:ho,useMutableSource:ho,useSyncExternalStore:ho,useId:ho,unstable_isNewReconciler:!1},Zo={readContext:zl,useCallback:function(e,t){return vo().memoizedState=[e,void 0===t?null:t],e},useContext:zl,useEffect:Do,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,To(4194308,4,Oo.bind(null,t,e),n)},useLayoutEffect:function(e,t){return To(4194308,4,e,t)},useInsertionEffect:function(e,t){return To(4,2,e,t)},useMemo:function(e,t){var n=vo();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vo();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Qo.bind(null,oo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},vo().memoizedState=e},useState:zo,useDebugValue:$o,useDeferredValue:function(e){return vo().memoizedState=e},useTransition:function(){var e=zo(!1),t=e[0];return e=Wo.bind(null,e[1]),vo().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=oo,a=vo();if(al){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===Ps)throw Error(l(349));0!==(30&lo)||Eo(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,Do(_o.bind(null,r,o,e),[e]),r.flags|=2048,jo(9,No.bind(null,r,o,n,t),void 0,null),n},useId:function(){var e=vo(),t=Ps.identifierPrefix;if(al){var n=Ga;t=":"+t+"R"+(n=(Xa&~(1<<32-ot(Xa)-1)).toString(32)+n),0<(n=fo++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=po++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ei={readContext:zl,useCallback:Bo,useContext:zl,useEffect:Lo,useImperativeHandle:Uo,useInsertionEffect:Ao,useLayoutEffect:Io,useMemo:Vo,useReducer:wo,useRef:Ro,useState:function(){return wo(xo)},useDebugValue:$o,useDeferredValue:function(e){return qo(bo(),io.memoizedState,e)},useTransition:function(){return[wo(xo)[0],bo().memoizedState]},useMutableSource:So,useSyncExternalStore:Co,useId:Ho,unstable_isNewReconciler:!1},ti={readContext:zl,useCallback:Bo,useContext:zl,useEffect:Lo,useImperativeHandle:Uo,useInsertionEffect:Ao,useLayoutEffect:Io,useMemo:Vo,useReducer:ko,useRef:Ro,useState:function(){return ko(xo)},useDebugValue:$o,useDeferredValue:function(e){var t=bo();return null===io?t.memoizedState=e:qo(t,io.memoizedState,e)},useTransition:function(){return[ko(xo)[0],bo().memoizedState]},useMutableSource:So,useSyncExternalStore:Co,useId:Ho,unstable_isNewReconciler:!1};function ni(e,t){if(e&&e.defaultProps){for(var n in t=A({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ri(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:A({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ai={isMounted:function(e){return!!(e=e._reactInternals)&&Be(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),l=Il(r,a);l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Ol(e,l,a))&&(nu(t,e,a,r),Ul(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),l=Il(r,a);l.tag=1,l.payload=t,void 0!==n&&null!==n&&(l.callback=n),null!==(t=Ol(e,l,a))&&(nu(t,e,a,r),Ul(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),a=Il(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Ol(e,a,r))&&(nu(t,e,r,n),Ul(t,e,r))}};function li(e,t,n,r,a,l,o){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,l,o):!t.prototype||!t.prototype.isPureReactComponent||(!sr(n,r)||!sr(a,l))}function oi(e,t,n){var r=!1,a=_a,l=t.contextType;return"object"===typeof l&&null!==l?l=zl(l):(a=Ra(t)?za:Ma.current,l=(r=null!==(r=t.contextTypes)&&void 0!==r)?ja(e,a):_a),t=new t(n,l),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ai,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=l),t}function ii(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ai.enqueueReplaceState(t,t.state,null)}function si(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Ll(e);var l=t.contextType;"object"===typeof l&&null!==l?a.context=zl(l):(l=Ra(t)?za:Ma.current,a.context=ja(e,l)),a.state=e.memoizedState,"function"===typeof(l=t.getDerivedStateFromProps)&&(ri(e,t,l,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ai.enqueueReplaceState(a,a.state,null),Bl(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function ui(e,t){try{var n="",r=t;do{n+=$(r),r=r.return}while(r);var a=n}catch(l){a="\nError generating stack: "+l.message+"\n"+l.stack}return{value:e,source:t,stack:a,digest:null}}function ci(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function di(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var fi="function"===typeof WeakMap?WeakMap:Map;function pi(e,t,n){(n=Il(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){qs||(qs=!0,Ws=r),di(0,t)},n}function hi(e,t,n){(n=Il(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){di(0,t)}}var l=e.stateNode;return null!==l&&"function"===typeof l.componentDidCatch&&(n.callback=function(){di(0,t),"function"!==typeof r&&(null===Hs?Hs=new Set([this]):Hs.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function mi(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fi;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Eu.bind(null,e,t,n),t.then(e,e))}function gi(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function yi(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Il(-1,1)).tag=2,Ol(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var vi=x.ReactCurrentOwner,bi=!1;function xi(e,t,n,r){t.child=null===e?wl(t,null,n,r):xl(t,e.child,n,r)}function wi(e,t,n,r,a){n=n.render;var l=t.ref;return Pl(t,a),r=go(e,t,n,r,l,a),n=yo(),null===e||bi?(al&&n&&el(t),t.flags|=1,xi(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,qi(e,t,a))}function ki(e,t,n,r,a){if(null===e){var l=n.type;return"function"!==typeof l||Ru(l)||void 0!==l.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Fu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=l,Si(e,t,l,r,a))}if(l=e.child,0===(e.lanes&a)){var o=l.memoizedProps;if((n=null!==(n=n.compare)?n:sr)(o,r)&&e.ref===t.ref)return qi(e,t,a)}return t.flags|=1,(e=Tu(l,r)).ref=t.ref,e.return=t,t.child=e}function Si(e,t,n,r,a){if(null!==e){var l=e.memoizedProps;if(sr(l,r)&&e.ref===t.ref){if(bi=!1,t.pendingProps=r=l,0===(e.lanes&a))return t.lanes=e.lanes,qi(e,t,a);0!==(131072&e.flags)&&(bi=!0)}}return Ni(e,t,n,r,a)}function Ci(e,t,n){var r=t.pendingProps,a=r.children,l=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Na(Ts,Rs),Rs|=n;else{if(0===(1073741824&n))return e=null!==l?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Na(Ts,Rs),Rs|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==l?l.baseLanes:n,Na(Ts,Rs),Rs|=r}else null!==l?(r=l.baseLanes|n,t.memoizedState=null):r=n,Na(Ts,Rs),Rs|=r;return xi(e,t,a,n),t.child}function Ei(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ni(e,t,n,r,a){var l=Ra(n)?za:Ma.current;return l=ja(t,l),Pl(t,a),n=go(e,t,n,r,l,a),r=yo(),null===e||bi?(al&&r&&el(t),t.flags|=1,xi(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,qi(e,t,a))}function _i(e,t,n,r,a){if(Ra(n)){var l=!0;La(t)}else l=!1;if(Pl(t,a),null===t.stateNode)Vi(e,t),oi(t,n,r),si(t,n,r,a),r=!0;else if(null===e){var o=t.stateNode,i=t.memoizedProps;o.props=i;var s=o.context,u=n.contextType;"object"===typeof u&&null!==u?u=zl(u):u=ja(t,u=Ra(n)?za:Ma.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof o.getSnapshotBeforeUpdate;d||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==r||s!==u)&&ii(t,o,r,u),Dl=!1;var f=t.memoizedState;o.state=f,Bl(t,r,o,a),s=t.memoizedState,i!==r||f!==s||Pa.current||Dl?("function"===typeof c&&(ri(t,n,c,r),s=t.memoizedState),(i=Dl||li(t,n,i,r,f,s,u))?(d||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||("function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"===typeof o.componentDidMount&&(t.flags|=4194308)):("function"===typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=s),o.props=r,o.state=s,o.context=u,r=i):("function"===typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,Al(e,t),i=t.memoizedProps,u=t.type===t.elementType?i:ni(t.type,i),o.props=u,d=t.pendingProps,f=o.context,"object"===typeof(s=n.contextType)&&null!==s?s=zl(s):s=ja(t,s=Ra(n)?za:Ma.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof o.getSnapshotBeforeUpdate)||"function"!==typeof o.UNSAFE_componentWillReceiveProps&&"function"!==typeof o.componentWillReceiveProps||(i!==d||f!==s)&&ii(t,o,r,s),Dl=!1,f=t.memoizedState,o.state=f,Bl(t,r,o,a);var h=t.memoizedState;i!==d||f!==h||Pa.current||Dl?("function"===typeof p&&(ri(t,n,p,r),h=t.memoizedState),(u=Dl||li(t,n,u,r,f,h,s)||!1)?(c||"function"!==typeof o.UNSAFE_componentWillUpdate&&"function"!==typeof o.componentWillUpdate||("function"===typeof o.componentWillUpdate&&o.componentWillUpdate(r,h,s),"function"===typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,h,s)),"function"===typeof o.componentDidUpdate&&(t.flags|=4),"function"===typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),o.props=r,o.state=h,o.context=s,r=u):("function"!==typeof o.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof o.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Mi(e,t,n,r,l,a)}function Mi(e,t,n,r,a,l){Ei(e,t);var o=0!==(128&t.flags);if(!r&&!o)return a&&Aa(t,n,!1),qi(e,t,l);r=t.stateNode,vi.current=t;var i=o&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&o?(t.child=xl(t,e.child,null,l),t.child=xl(t,null,i,l)):xi(e,t,i,l),t.memoizedState=r.state,a&&Aa(t,n,!0),t.child}function Pi(e){var t=e.stateNode;t.pendingContext?Fa(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Fa(0,t.context,!1),Yl(e,t.containerInfo)}function zi(e,t,n,r,a){return pl(),hl(a),t.flags|=256,xi(e,t,n,r),t.child}var ji,Ri,Ti,Fi,Di={dehydrated:null,treeContext:null,retryLane:0};function Li(e){return{baseLanes:e,cachePool:null,transitions:null}}function Ai(e,t,n){var r,a=t.pendingProps,o=Zl.current,i=!1,s=0!==(128&t.flags);if((r=s)||(r=(null===e||null!==e.memoizedState)&&0!==(2&o)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(o|=1),Na(Zl,1&o),null===e)return ul(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(s=a.children,e=a.fallback,i?(a=t.mode,i=t.child,s={mode:"hidden",children:s},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=s):i=Lu(s,a,0,null),e=Du(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Li(n),t.memoizedState=Di,e):Ii(t,s));if(null!==(o=e.memoizedState)&&null!==(r=o.dehydrated))return function(e,t,n,r,a,o,i){if(n)return 256&t.flags?(t.flags&=-257,Oi(e,t,i,r=ci(Error(l(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(o=r.fallback,a=t.mode,r=Lu({mode:"visible",children:r.children},a,0,null),(o=Du(o,a,i,null)).flags|=2,r.return=t,o.return=t,r.sibling=o,t.child=r,0!==(1&t.mode)&&xl(t,e.child,null,i),t.child.memoizedState=Li(i),t.memoizedState=Di,o);if(0===(1&t.mode))return Oi(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var s=r.dgst;return r=s,Oi(e,t,i,r=ci(o=Error(l(419)),r,void 0))}if(s=0!==(i&e.childLanes),bi||s){if(null!==(r=Ps)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==o.retryLane&&(o.retryLane=a,Fl(e,a),nu(r,e,a,-1))}return mu(),Oi(e,t,i,r=ci(Error(l(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=_u.bind(null,e),a._reactRetry=t,null):(e=o.treeContext,rl=ua(a.nextSibling),nl=t,al=!0,ll=null,null!==e&&(Qa[Ka++]=Xa,Qa[Ka++]=Ga,Qa[Ka++]=Ya,Xa=e.id,Ga=e.overflow,Ya=t),t=Ii(t,r.children),t.flags|=4096,t)}(e,t,s,a,r,o,n);if(i){i=a.fallback,s=t.mode,r=(o=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&s)&&t.child!==o?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=Tu(o,u)).subtreeFlags=14680064&o.subtreeFlags,null!==r?i=Tu(r,i):(i=Du(i,s,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,s=null===(s=e.child.memoizedState)?Li(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=Di,a}return e=(i=e.child).sibling,a=Tu(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ii(e,t){return(t=Lu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Oi(e,t,n,r){return null!==r&&hl(r),xl(t,e.child,null,n),(e=Ii(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ui(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ml(e.return,t,n)}function $i(e,t,n,r,a){var l=e.memoizedState;null===l?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=a)}function Bi(e,t,n){var r=t.pendingProps,a=r.revealOrder,l=r.tail;if(xi(e,t,r.children,n),0!==(2&(r=Zl.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ui(e,n,t);else if(19===e.tag)Ui(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Na(Zl,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===eo(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),$i(t,!1,a,n,l);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===eo(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}$i(t,!0,n,null,l);break;case"together":$i(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vi(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function qi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Ls|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Tu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Tu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Wi(e,t){if(!al)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Hi(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qi(e,t,n){var r=t.pendingProps;switch(tl(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Hi(t),null;case 1:case 17:return Ra(t.type)&&Ta(),Hi(t),null;case 3:return r=t.stateNode,Xl(),Ea(Pa),Ea(Ma),no(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(dl(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ll&&(ou(ll),ll=null))),Ri(e,t),Hi(t),null;case 5:Jl(t);var a=Kl(Ql.current);if(n=t.type,null!==e&&null!=t.stateNode)Ti(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(l(166));return Hi(t),null}if(e=Kl(Wl.current),dl(t)){r=t.stateNode,n=t.type;var o=t.memoizedProps;switch(r[fa]=t,r[pa]=o,e=0!==(1&t.mode),n){case"dialog":Or("cancel",r),Or("close",r);break;case"iframe":case"object":case"embed":Or("load",r);break;case"video":case"audio":for(a=0;a<Dr.length;a++)Or(Dr[a],r);break;case"source":Or("error",r);break;case"img":case"image":case"link":Or("error",r),Or("load",r);break;case"details":Or("toggle",r);break;case"input":X(r,o),Or("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!o.multiple},Or("invalid",r);break;case"textarea":ae(r,o),Or("invalid",r)}for(var s in ve(n,o),a=null,o)if(o.hasOwnProperty(s)){var u=o[s];"children"===s?"string"===typeof u?r.textContent!==u&&(!0!==o.suppressHydrationWarning&&Jr(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==o.suppressHydrationWarning&&Jr(r.textContent,u,e),a=["children",""+u]):i.hasOwnProperty(s)&&null!=u&&"onScroll"===s&&Or("scroll",r)}switch(n){case"input":H(r),Z(r,o,!0);break;case"textarea":H(r),oe(r);break;case"select":case"option":break;default:"function"===typeof o.onClick&&(r.onclick=Zr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{s=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),"select"===n&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[fa]=t,e[pa]=r,ji(e,t,!1,!1),t.stateNode=e;e:{switch(s=be(n,r),n){case"dialog":Or("cancel",e),Or("close",e),a=r;break;case"iframe":case"object":case"embed":Or("load",e),a=r;break;case"video":case"audio":for(a=0;a<Dr.length;a++)Or(Dr[a],e);a=r;break;case"source":Or("error",e),a=r;break;case"img":case"image":case"link":Or("error",e),Or("load",e),a=r;break;case"details":Or("toggle",e),a=r;break;case"input":X(e,r),a=Y(e,r),Or("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=A({},r,{value:void 0}),Or("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Or("invalid",e)}for(o in ve(n,a),u=a)if(u.hasOwnProperty(o)){var c=u[o];"style"===o?ge(e,c):"dangerouslySetInnerHTML"===o?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===o?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==o&&"suppressHydrationWarning"!==o&&"autoFocus"!==o&&(i.hasOwnProperty(o)?null!=c&&"onScroll"===o&&Or("scroll",e):null!=c&&b(e,o,c,s))}switch(n){case"input":H(e),Z(e,r,!1);break;case"textarea":H(e),oe(e);break;case"option":null!=r.value&&e.setAttribute("value",""+q(r.value));break;case"select":e.multiple=!!r.multiple,null!=(o=r.value)?ne(e,!!r.multiple,o,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Hi(t),null;case 6:if(e&&null!=t.stateNode)Fi(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(n=Kl(Ql.current),Kl(Wl.current),dl(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(o=r.nodeValue!==n)&&null!==(e=nl))switch(e.tag){case 3:Jr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jr(r.nodeValue,n,0!==(1&e.mode))}o&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Hi(t),null;case 13:if(Ea(Zl),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(al&&null!==rl&&0!==(1&t.mode)&&0===(128&t.flags))fl(),pl(),t.flags|=98560,o=!1;else if(o=dl(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(l(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(l(317));o[fa]=t}else pl(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Hi(t),o=!1}else null!==ll&&(ou(ll),ll=null),o=!0;if(!o)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&Zl.current)?0===Fs&&(Fs=3):mu())),null!==t.updateQueue&&(t.flags|=4),Hi(t),null);case 4:return Xl(),Ri(e,t),null===e&&Br(t.stateNode.containerInfo),Hi(t),null;case 10:return _l(t.type._context),Hi(t),null;case 19:if(Ea(Zl),null===(o=t.memoizedState))return Hi(t),null;if(r=0!==(128&t.flags),null===(s=o.rendering))if(r)Wi(o,!1);else{if(0!==Fs||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(s=eo(e))){for(t.flags|=128,Wi(o,!1),null!==(r=s.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(o=n).flags&=14680066,null===(s=o.alternate)?(o.childLanes=0,o.lanes=e,o.child=null,o.subtreeFlags=0,o.memoizedProps=null,o.memoizedState=null,o.updateQueue=null,o.dependencies=null,o.stateNode=null):(o.childLanes=s.childLanes,o.lanes=s.lanes,o.child=s.child,o.subtreeFlags=0,o.deletions=null,o.memoizedProps=s.memoizedProps,o.memoizedState=s.memoizedState,o.updateQueue=s.updateQueue,o.type=s.type,e=s.dependencies,o.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Na(Zl,1&Zl.current|2),t.child}e=e.sibling}null!==o.tail&&Ge()>Bs&&(t.flags|=128,r=!0,Wi(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=eo(s))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Wi(o,!0),null===o.tail&&"hidden"===o.tailMode&&!s.alternate&&!al)return Hi(t),null}else 2*Ge()-o.renderingStartTime>Bs&&1073741824!==n&&(t.flags|=128,r=!0,Wi(o,!1),t.lanes=4194304);o.isBackwards?(s.sibling=t.child,t.child=s):(null!==(n=o.last)?n.sibling=s:t.child=s,o.last=s)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=Ge(),t.sibling=null,n=Zl.current,Na(Zl,r?1&n|2:1&n),t):(Hi(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Rs)&&(Hi(t),6&t.subtreeFlags&&(t.flags|=8192)):Hi(t),null;case 24:case 25:return null}throw Error(l(156,t.tag))}function Ki(e,t){switch(tl(t),t.tag){case 1:return Ra(t.type)&&Ta(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xl(),Ea(Pa),Ea(Ma),no(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Jl(t),null;case 13:if(Ea(Zl),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));pl()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ea(Zl),null;case 4:return Xl(),null;case 10:return _l(t.type._context),null;case 22:case 23:return du(),null;default:return null}}ji=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ri=function(){},Ti=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Kl(Wl.current);var l,o=null;switch(n){case"input":a=Y(e,a),r=Y(e,r),o=[];break;case"select":a=A({},a,{value:void 0}),r=A({},r,{value:void 0}),o=[];break;case"textarea":a=re(e,a),r=re(e,r),o=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ve(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var s=a[c];for(l in s)s.hasOwnProperty(l)&&(n||(n={}),n[l]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?o||(o=[]):(o=o||[]).push(c,null));for(c in r){var u=r[c];if(s=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==s&&(null!=u||null!=s))if("style"===c)if(s){for(l in s)!s.hasOwnProperty(l)||u&&u.hasOwnProperty(l)||(n||(n={}),n[l]="");for(l in u)u.hasOwnProperty(l)&&s[l]!==u[l]&&(n||(n={}),n[l]=u[l])}else n||(o||(o=[]),o.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,s=s?s.__html:void 0,null!=u&&s!==u&&(o=o||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(o=o||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Or("scroll",e),o||s===u||(o=[])):(o=o||[]).push(c,u))}n&&(o=o||[]).push("style",n);var c=o;(t.updateQueue=c)&&(t.flags|=4)}},Fi=function(e,t,n,r){n!==r&&(t.flags|=4)};var Yi=!1,Xi=!1,Gi="function"===typeof WeakSet?WeakSet:Set,Ji=null;function Zi(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Cu(e,t,r)}else n.current=null}function es(e,t,n){try{n()}catch(r){Cu(e,t,r)}}var ts=!1;function ns(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var l=a.destroy;a.destroy=void 0,void 0!==l&&es(t,n,l)}a=a.next}while(a!==r)}}function rs(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function as(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ls(e){var t=e.alternate;null!==t&&(e.alternate=null,ls(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ma],delete t[ga],delete t[ya])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function os(e){return 5===e.tag||3===e.tag||4===e.tag}function is(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||os(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ss(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ss(e,t,n),e=e.sibling;null!==e;)ss(e,t,n),e=e.sibling}function us(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(us(e,t,n),e=e.sibling;null!==e;)us(e,t,n),e=e.sibling}var cs=null,ds=!1;function fs(e,t,n){for(n=n.child;null!==n;)ps(e,t,n),n=n.sibling}function ps(e,t,n){if(lt&&"function"===typeof lt.onCommitFiberUnmount)try{lt.onCommitFiberUnmount(at,n)}catch(i){}switch(n.tag){case 5:Xi||Zi(n,t);case 6:var r=cs,a=ds;cs=null,fs(e,t,n),ds=a,null!==(cs=r)&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cs.removeChild(n.stateNode));break;case 18:null!==cs&&(ds?(e=cs,n=n.stateNode,8===e.nodeType?sa(e.parentNode,n):1===e.nodeType&&sa(e,n),Bt(e)):sa(cs,n.stateNode));break;case 4:r=cs,a=ds,cs=n.stateNode.containerInfo,ds=!0,fs(e,t,n),cs=r,ds=a;break;case 0:case 11:case 14:case 15:if(!Xi&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var l=a,o=l.destroy;l=l.tag,void 0!==o&&(0!==(2&l)||0!==(4&l))&&es(n,t,o),a=a.next}while(a!==r)}fs(e,t,n);break;case 1:if(!Xi&&(Zi(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){Cu(n,t,i)}fs(e,t,n);break;case 21:fs(e,t,n);break;case 22:1&n.mode?(Xi=(r=Xi)||null!==n.memoizedState,fs(e,t,n),Xi=r):fs(e,t,n);break;default:fs(e,t,n)}}function hs(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Gi),t.forEach(function(t){var r=Mu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}}function ms(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var o=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 5:cs=s.stateNode,ds=!1;break e;case 3:case 4:cs=s.stateNode.containerInfo,ds=!0;break e}s=s.return}if(null===cs)throw Error(l(160));ps(o,i,a),cs=null,ds=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(c){Cu(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gs(t,e),t=t.sibling}function gs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ms(t,e),ys(e),4&r){try{ns(3,e,e.return),rs(3,e)}catch(g){Cu(e,e.return,g)}try{ns(5,e,e.return)}catch(g){Cu(e,e.return,g)}}break;case 1:ms(t,e),ys(e),512&r&&null!==n&&Zi(n,n.return);break;case 5:if(ms(t,e),ys(e),512&r&&null!==n&&Zi(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){Cu(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var o=e.memoizedProps,i=null!==n?n.memoizedProps:o,s=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===s&&"radio"===o.type&&null!=o.name&&G(a,o),be(s,i);var c=be(s,o);for(i=0;i<u.length;i+=2){var d=u[i],f=u[i+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,c)}switch(s){case"input":J(a,o);break;case"textarea":le(a,o);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!o.multiple;var h=o.value;null!=h?ne(a,!!o.multiple,h,!1):p!==!!o.multiple&&(null!=o.defaultValue?ne(a,!!o.multiple,o.defaultValue,!0):ne(a,!!o.multiple,o.multiple?[]:"",!1))}a[pa]=o}catch(g){Cu(e,e.return,g)}}break;case 6:if(ms(t,e),ys(e),4&r){if(null===e.stateNode)throw Error(l(162));a=e.stateNode,o=e.memoizedProps;try{a.nodeValue=o}catch(g){Cu(e,e.return,g)}}break;case 3:if(ms(t,e),ys(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Bt(t.containerInfo)}catch(g){Cu(e,e.return,g)}break;case 4:default:ms(t,e),ys(e);break;case 13:ms(t,e),ys(e),8192&(a=e.child).flags&&(o=null!==a.memoizedState,a.stateNode.isHidden=o,!o||null!==a.alternate&&null!==a.alternate.memoizedState||($s=Ge())),4&r&&hs(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Xi=(c=Xi)||d,ms(t,e),Xi=c):ms(t,e),ys(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Ji=e,d=e.child;null!==d;){for(f=Ji=d;null!==Ji;){switch(h=(p=Ji).child,p.tag){case 0:case 11:case 14:case 15:ns(4,p,p.return);break;case 1:Zi(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Cu(r,n,g)}}break;case 5:Zi(p,p.return);break;case 22:if(null!==p.memoizedState){ws(f);continue}}null!==h?(h.return=p,Ji=h):ws(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(o=a.style).setProperty?o.setProperty("display","none","important"):o.display="none":(s=f.stateNode,i=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,s.style.display=me("display",i))}catch(g){Cu(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){Cu(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ms(t,e),ys(e),4&r&&hs(e);case 21:}}function ys(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(os(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),us(e,is(e),a);break;case 3:case 4:var o=r.stateNode.containerInfo;ss(e,is(e),o);break;default:throw Error(l(161))}}catch(i){Cu(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vs(e,t,n){Ji=e,bs(e,t,n)}function bs(e,t,n){for(var r=0!==(1&e.mode);null!==Ji;){var a=Ji,l=a.child;if(22===a.tag&&r){var o=null!==a.memoizedState||Yi;if(!o){var i=a.alternate,s=null!==i&&null!==i.memoizedState||Xi;i=Yi;var u=Xi;if(Yi=o,(Xi=s)&&!u)for(Ji=a;null!==Ji;)s=(o=Ji).child,22===o.tag&&null!==o.memoizedState?ks(a):null!==s?(s.return=o,Ji=s):ks(a);for(;null!==l;)Ji=l,bs(l,t,n),l=l.sibling;Ji=a,Yi=i,Xi=u}xs(e)}else 0!==(8772&a.subtreeFlags)&&null!==l?(l.return=a,Ji=l):xs(e)}}function xs(e){for(;null!==Ji;){var t=Ji;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Xi||rs(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Xi)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ni(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var o=t.updateQueue;null!==o&&Vl(t,o,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Vl(t,i,n)}break;case 5:var s=t.stateNode;if(null===n&&4&t.flags){n=s;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Bt(f)}}}break;default:throw Error(l(163))}Xi||512&t.flags&&as(t)}catch(p){Cu(t,t.return,p)}}if(t===e){Ji=null;break}if(null!==(n=t.sibling)){n.return=t.return,Ji=n;break}Ji=t.return}}function ws(e){for(;null!==Ji;){var t=Ji;if(t===e){Ji=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Ji=n;break}Ji=t.return}}function ks(e){for(;null!==Ji;){var t=Ji;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rs(4,t)}catch(s){Cu(t,n,s)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(s){Cu(t,a,s)}}var l=t.return;try{as(t)}catch(s){Cu(t,l,s)}break;case 5:var o=t.return;try{as(t)}catch(s){Cu(t,o,s)}}}catch(s){Cu(t,t.return,s)}if(t===e){Ji=null;break}var i=t.sibling;if(null!==i){i.return=t.return,Ji=i;break}Ji=t.return}}var Ss,Cs=Math.ceil,Es=x.ReactCurrentDispatcher,Ns=x.ReactCurrentOwner,_s=x.ReactCurrentBatchConfig,Ms=0,Ps=null,zs=null,js=0,Rs=0,Ts=Ca(0),Fs=0,Ds=null,Ls=0,As=0,Is=0,Os=null,Us=null,$s=0,Bs=1/0,Vs=null,qs=!1,Ws=null,Hs=null,Qs=!1,Ks=null,Ys=0,Xs=0,Gs=null,Js=-1,Zs=0;function eu(){return 0!==(6&Ms)?Ge():-1!==Js?Js:Js=Ge()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Ms)&&0!==js?js&-js:null!==ml.transition?(0===Zs&&(Zs=mt()),Zs):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Xt(e.type)}function nu(e,t,n,r){if(50<Xs)throw Xs=0,Gs=null,Error(l(185));yt(e,n,r),0!==(2&Ms)&&e===Ps||(e===Ps&&(0===(2&Ms)&&(As|=n),4===Fs&&iu(e,js)),ru(e,r),1===n&&0===Ms&&0===(1&t.mode)&&(Bs=Ge()+500,Oa&&Ba()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,l=e.pendingLanes;0<l;){var o=31-ot(l),i=1<<o,s=a[o];-1===s?0!==(i&n)&&0===(i&r)||(a[o]=pt(i,t)):s<=t&&(e.expiredLanes|=i),l&=~i}}(e,t);var r=ft(e,e===Ps?js:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Oa=!0,$a(e)}(su.bind(null,e)):$a(su.bind(null,e)),oa(function(){0===(6&Ms)&&Ba()}),n=null;else{switch(xt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Pu(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Js=-1,Zs=0,0!==(6&Ms))throw Error(l(327));var n=e.callbackNode;if(ku()&&e.callbackNode!==n)return null;var r=ft(e,e===Ps?js:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gu(e,r);else{t=r;var a=Ms;Ms|=2;var o=hu();for(Ps===e&&js===t||(Vs=null,Bs=Ge()+500,fu(e,t));;)try{vu();break}catch(s){pu(e,s)}Nl(),Es.current=o,Ms=a,null!==zs?t=0:(Ps=null,js=0,t=Fs)}if(0!==t){if(2===t&&(0!==(a=ht(e))&&(r=a,t=lu(e,a))),1===t)throw n=Ds,fu(e,0),iu(e,r),ru(e,Ge()),n;if(6===t)iu(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],l=a.getSnapshot;a=a.value;try{if(!ir(l(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gu(e,r))&&(0!==(o=ht(e))&&(r=o,t=lu(e,o))),1===t))throw n=Ds,fu(e,0),iu(e,r),ru(e,Ge()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(l(345));case 2:case 5:wu(e,Us,Vs);break;case 3:if(iu(e,r),(130023424&r)===r&&10<(t=$s+500-Ge())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(wu.bind(null,e,Us,Vs),t);break}wu(e,Us,Vs);break;case 4:if(iu(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-ot(r);o=1<<i,(i=t[i])>a&&(a=i),r&=~o}if(r=a,10<(r=(120>(r=Ge()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Cs(r/1960))-r)){e.timeoutHandle=ra(wu.bind(null,e,Us,Vs),r);break}wu(e,Us,Vs);break;default:throw Error(l(329))}}}return ru(e,Ge()),e.callbackNode===n?au.bind(null,e):null}function lu(e,t){var n=Os;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=Us,Us=n,null!==t&&ou(t)),e}function ou(e){null===Us?Us=e:Us.push.apply(Us,e)}function iu(e,t){for(t&=~Is,t&=~As,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ot(t),r=1<<n;e[n]=-1,t&=~r}}function su(e){if(0!==(6&Ms))throw Error(l(327));ku();var t=ft(e,0);if(0===(1&t))return ru(e,Ge()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=lu(e,r))}if(1===n)throw n=Ds,fu(e,0),iu(e,t),ru(e,Ge()),n;if(6===n)throw Error(l(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,wu(e,Us,Vs),ru(e,Ge()),null}function uu(e,t){var n=Ms;Ms|=1;try{return e(t)}finally{0===(Ms=n)&&(Bs=Ge()+500,Oa&&Ba())}}function cu(e){null!==Ks&&0===Ks.tag&&0===(6&Ms)&&ku();var t=Ms;Ms|=1;var n=_s.transition,r=bt;try{if(_s.transition=null,bt=1,e)return e()}finally{bt=r,_s.transition=n,0===(6&(Ms=t))&&Ba()}}function du(){Rs=Ts.current,Ea(Ts)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==zs)for(n=zs.return;null!==n;){var r=n;switch(tl(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&Ta();break;case 3:Xl(),Ea(Pa),Ea(Ma),no();break;case 5:Jl(r);break;case 4:Xl();break;case 13:case 19:Ea(Zl);break;case 10:_l(r.type._context);break;case 22:case 23:du()}n=n.return}if(Ps=e,zs=e=Tu(e.current,null),js=Rs=t,Fs=0,Ds=null,Is=As=Ls=0,Us=Os=null,null!==jl){for(t=0;t<jl.length;t++)if(null!==(r=(n=jl[t]).interleaved)){n.interleaved=null;var a=r.next,l=n.pending;if(null!==l){var o=l.next;l.next=a,r.next=o}n.pending=r}jl=null}return e}function pu(e,t){for(;;){var n=zs;try{if(Nl(),ro.current=Jo,uo){for(var r=oo.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}uo=!1}if(lo=0,so=io=oo=null,co=!1,fo=0,Ns.current=null,null===n||null===n.return){Fs=1,Ds=t,zs=null;break}e:{var o=e,i=n.return,s=n,u=t;if(t=js,s.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=s,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=gi(i);if(null!==h){h.flags&=-257,yi(h,i,s,0,t),1&h.mode&&mi(o,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0===(1&t)){mi(o,c,t),mu();break e}u=Error(l(426))}else if(al&&1&s.mode){var y=gi(i);if(null!==y){0===(65536&y.flags)&&(y.flags|=256),yi(y,i,s,0,t),hl(ui(u,s));break e}}o=u=ui(u,s),4!==Fs&&(Fs=2),null===Os?Os=[o]:Os.push(o),o=i;do{switch(o.tag){case 3:o.flags|=65536,t&=-t,o.lanes|=t,$l(o,pi(0,u,t));break e;case 1:s=u;var v=o.type,b=o.stateNode;if(0===(128&o.flags)&&("function"===typeof v.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Hs||!Hs.has(b)))){o.flags|=65536,t&=-t,o.lanes|=t,$l(o,hi(o,s,t));break e}}o=o.return}while(null!==o)}xu(n)}catch(x){t=x,zs===n&&null!==n&&(zs=n=n.return);continue}break}}function hu(){var e=Es.current;return Es.current=Jo,null===e?Jo:e}function mu(){0!==Fs&&3!==Fs&&2!==Fs||(Fs=4),null===Ps||0===(268435455&Ls)&&0===(268435455&As)||iu(Ps,js)}function gu(e,t){var n=Ms;Ms|=2;var r=hu();for(Ps===e&&js===t||(Vs=null,fu(e,t));;)try{yu();break}catch(a){pu(e,a)}if(Nl(),Ms=n,Es.current=r,null!==zs)throw Error(l(261));return Ps=null,js=0,Fs}function yu(){for(;null!==zs;)bu(zs)}function vu(){for(;null!==zs&&!Ye();)bu(zs)}function bu(e){var t=Ss(e.alternate,e,Rs);e.memoizedProps=e.pendingProps,null===t?xu(e):zs=t,Ns.current=null}function xu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Qi(n,t,Rs)))return void(zs=n)}else{if(null!==(n=Ki(n,t)))return n.flags&=32767,void(zs=n);if(null===e)return Fs=6,void(zs=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(zs=t);zs=t=e}while(null!==t);0===Fs&&(Fs=5)}function wu(e,t,n){var r=bt,a=_s.transition;try{_s.transition=null,bt=1,function(e,t,n,r){do{ku()}while(null!==Ks);if(0!==(6&Ms))throw Error(l(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0;var o=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-ot(n),l=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~l}}(e,o),e===Ps&&(zs=Ps=null,js=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Qs||(Qs=!0,Pu(tt,function(){return ku(),null})),o=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||o){o=_s.transition,_s.transition=null;var i=bt;bt=1;var s=Ms;Ms|=4,Ns.current=null,function(e,t){if(ea=qt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(w){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==o||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===o&&++d===r&&(u=i),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},qt=!1,Ji=t;null!==Ji;)if(e=(t=Ji).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Ji=e;else for(;null!==Ji;){t=Ji;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,y=m.memoizedState,v=t.stateNode,b=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:ni(t.type,g),y);v.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var x=t.stateNode.containerInfo;1===x.nodeType?x.textContent="":9===x.nodeType&&x.documentElement&&x.removeChild(x.documentElement);break;default:throw Error(l(163))}}catch(w){Cu(t,t.return,w)}if(null!==(e=t.sibling)){e.return=t.return,Ji=e;break}Ji=t.return}m=ts,ts=!1}(e,n),gs(n,e),hr(ta),qt=!!ea,ta=ea=null,e.current=n,vs(n,e,a),Xe(),Ms=s,bt=i,_s.transition=o}else e.current=n;if(Qs&&(Qs=!1,Ks=e,Ys=a),o=e.pendingLanes,0===o&&(Hs=null),function(e){if(lt&&"function"===typeof lt.onCommitFiberRoot)try{lt.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Ge()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(qs)throw qs=!1,e=Ws,Ws=null,e;0!==(1&Ys)&&0!==e.tag&&ku(),o=e.pendingLanes,0!==(1&o)?e===Gs?Xs++:(Xs=0,Gs=e):Xs=0,Ba()}(e,t,n,r)}finally{_s.transition=a,bt=r}return null}function ku(){if(null!==Ks){var e=xt(Ys),t=_s.transition,n=bt;try{if(_s.transition=null,bt=16>e?16:e,null===Ks)var r=!1;else{if(e=Ks,Ks=null,Ys=0,0!==(6&Ms))throw Error(l(331));var a=Ms;for(Ms|=4,Ji=e.current;null!==Ji;){var o=Ji,i=o.child;if(0!==(16&Ji.flags)){var s=o.deletions;if(null!==s){for(var u=0;u<s.length;u++){var c=s[u];for(Ji=c;null!==Ji;){var d=Ji;switch(d.tag){case 0:case 11:case 15:ns(8,d,o)}var f=d.child;if(null!==f)f.return=d,Ji=f;else for(;null!==Ji;){var p=(d=Ji).sibling,h=d.return;if(ls(d),d===c){Ji=null;break}if(null!==p){p.return=h,Ji=p;break}Ji=h}}}var m=o.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var y=g.sibling;g.sibling=null,g=y}while(null!==g)}}Ji=o}}if(0!==(2064&o.subtreeFlags)&&null!==i)i.return=o,Ji=i;else e:for(;null!==Ji;){if(0!==(2048&(o=Ji).flags))switch(o.tag){case 0:case 11:case 15:ns(9,o,o.return)}var v=o.sibling;if(null!==v){v.return=o.return,Ji=v;break e}Ji=o.return}}var b=e.current;for(Ji=b;null!==Ji;){var x=(i=Ji).child;if(0!==(2064&i.subtreeFlags)&&null!==x)x.return=i,Ji=x;else e:for(i=b;null!==Ji;){if(0!==(2048&(s=Ji).flags))try{switch(s.tag){case 0:case 11:case 15:rs(9,s)}}catch(k){Cu(s,s.return,k)}if(s===i){Ji=null;break e}var w=s.sibling;if(null!==w){w.return=s.return,Ji=w;break e}Ji=s.return}}if(Ms=a,Ba(),lt&&"function"===typeof lt.onPostCommitFiberRoot)try{lt.onPostCommitFiberRoot(at,e)}catch(k){}r=!0}return r}finally{bt=n,_s.transition=t}}return!1}function Su(e,t,n){e=Ol(e,t=pi(0,t=ui(n,t),1),1),t=eu(),null!==e&&(yt(e,1,t),ru(e,t))}function Cu(e,t,n){if(3===e.tag)Su(e,e,n);else for(;null!==t;){if(3===t.tag){Su(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Hs||!Hs.has(r))){t=Ol(t,e=hi(t,e=ui(n,e),1),1),e=eu(),null!==t&&(yt(t,1,e),ru(t,e));break}}t=t.return}}function Eu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Ps===e&&(js&n)===n&&(4===Fs||3===Fs&&(130023424&js)===js&&500>Ge()-$s?fu(e,0):Is|=n),ru(e,t)}function Nu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=Fl(e,t))&&(yt(e,t,n),ru(e,n))}function _u(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Nu(e,n)}function Mu(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(l(314))}null!==r&&r.delete(t),Nu(e,n)}function Pu(e,t){return Qe(e,t)}function zu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ju(e,t,n,r){return new zu(e,t,n,r)}function Ru(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Tu(e,t){var n=e.alternate;return null===n?((n=ju(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Fu(e,t,n,r,a,o){var i=2;if(r=e,"function"===typeof e)Ru(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case S:return Du(n.children,a,o,t);case C:i=8,a|=8;break;case E:return(e=ju(12,n,t,2|a)).elementType=E,e.lanes=o,e;case P:return(e=ju(13,n,t,a)).elementType=P,e.lanes=o,e;case z:return(e=ju(19,n,t,a)).elementType=z,e.lanes=o,e;case T:return Lu(n,a,o,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case N:i=10;break e;case _:i=9;break e;case M:i=11;break e;case j:i=14;break e;case R:i=16,r=null;break e}throw Error(l(130,null==e?e:typeof e,""))}return(t=ju(i,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Du(e,t,n,r){return(e=ju(7,e,r,t)).lanes=n,e}function Lu(e,t,n,r){return(e=ju(22,e,r,t)).elementType=T,e.lanes=n,e.stateNode={isHidden:!1},e}function Au(e,t,n){return(e=ju(6,e,null,t)).lanes=n,e}function Iu(e,t,n){return(t=ju(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Ou(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Uu(e,t,n,r,a,l,o,i,s){return e=new Ou(e,t,n,i,s),1===t?(t=1,!0===l&&(t|=8)):t=0,l=ju(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Ll(l),e}function $u(e){if(!e)return _a;e:{if(Be(e=e._reactInternals)!==e||1!==e.tag)throw Error(l(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Ra(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(l(171))}if(1===e.tag){var n=e.type;if(Ra(n))return Da(e,n,t)}return t}function Bu(e,t,n,r,a,l,o,i,s){return(e=Uu(n,r,!0,e,0,l,0,i,s)).context=$u(null),n=e.current,(l=Il(r=eu(),a=tu(n))).callback=void 0!==t&&null!==t?t:null,Ol(n,l,a),e.current.lanes=a,yt(e,a,r),ru(e,r),e}function Vu(e,t,n,r){var a=t.current,l=eu(),o=tu(a);return n=$u(n),null===t.context?t.context=n:t.pendingContext=n,(t=Il(l,o)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ol(a,t,o))&&(nu(e,a,o,l),Ul(e,a,o)),o}function qu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Wu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Hu(e,t){Wu(e,t),(e=e.alternate)&&Wu(e,t)}Ss=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Pa.current)bi=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bi=!1,function(e,t,n){switch(t.tag){case 3:Pi(t),pl();break;case 5:Gl(t);break;case 1:Ra(t.type)&&La(t);break;case 4:Yl(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Na(kl,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Na(Zl,1&Zl.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Ai(e,t,n):(Na(Zl,1&Zl.current),null!==(e=qi(e,t,n))?e.sibling:null);Na(Zl,1&Zl.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Bi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Na(Zl,Zl.current),r)break;return null;case 22:case 23:return t.lanes=0,Ci(e,t,n)}return qi(e,t,n)}(e,t,n);bi=0!==(131072&e.flags)}else bi=!1,al&&0!==(1048576&t.flags)&&Za(t,Ha,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Vi(e,t),e=t.pendingProps;var a=ja(t,Ma.current);Pl(t,n),a=go(null,t,r,e,a,n);var o=yo();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Ra(r)?(o=!0,La(t)):o=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Ll(t),a.updater=ai,t.stateNode=a,a._reactInternals=t,si(t,r,e,n),t=Mi(null,t,r,!0,o,n)):(t.tag=0,al&&o&&el(t),xi(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Vi(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Ru(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===M)return 11;if(e===j)return 14}return 2}(r),e=ni(r,e),a){case 0:t=Ni(null,t,r,e,n);break e;case 1:t=_i(null,t,r,e,n);break e;case 11:t=wi(null,t,r,e,n);break e;case 14:t=ki(null,t,r,ni(r.type,e),n);break e}throw Error(l(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ni(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 1:return r=t.type,a=t.pendingProps,_i(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 3:e:{if(Pi(t),null===e)throw Error(l(387));r=t.pendingProps,a=(o=t.memoizedState).element,Al(e,t),Bl(t,r,null,n);var i=t.memoizedState;if(r=i.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=zi(e,t,r,n,a=ui(Error(l(423)),t));break e}if(r!==a){t=zi(e,t,r,n,a=ui(Error(l(424)),t));break e}for(rl=ua(t.stateNode.containerInfo.firstChild),nl=t,al=!0,ll=null,n=wl(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pl(),r===a){t=qi(e,t,n);break e}xi(e,t,r,n)}t=t.child}return t;case 5:return Gl(t),null===e&&ul(t),r=t.type,a=t.pendingProps,o=null!==e?e.memoizedProps:null,i=a.children,na(r,a)?i=null:null!==o&&na(r,o)&&(t.flags|=32),Ei(e,t),xi(e,t,i,n),t.child;case 6:return null===e&&ul(t),null;case 13:return Ai(e,t,n);case 4:return Yl(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xl(t,null,r,n):xi(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,wi(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 7:return xi(e,t,t.pendingProps,n),t.child;case 8:case 12:return xi(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,o=t.memoizedProps,i=a.value,Na(kl,r._currentValue),r._currentValue=i,null!==o)if(ir(o.value,i)){if(o.children===a.children&&!Pa.current){t=qi(e,t,n);break e}}else for(null!==(o=t.child)&&(o.return=t);null!==o;){var s=o.dependencies;if(null!==s){i=o.child;for(var u=s.firstContext;null!==u;){if(u.context===r){if(1===o.tag){(u=Il(-1,n&-n)).tag=2;var c=o.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}o.lanes|=n,null!==(u=o.alternate)&&(u.lanes|=n),Ml(o.return,n,t),s.lanes|=n;break}u=u.next}}else if(10===o.tag)i=o.type===t.type?null:o.child;else if(18===o.tag){if(null===(i=o.return))throw Error(l(341));i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Ml(i,n,t),i=o.sibling}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===t){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}xi(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Pl(t,n),r=r(a=zl(a)),t.flags|=1,xi(e,t,r,n),t.child;case 14:return a=ni(r=t.type,t.pendingProps),ki(e,t,r,a=ni(r.type,a),n);case 15:return Si(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ni(r,a),Vi(e,t),t.tag=1,Ra(r)?(e=!0,La(t)):e=!1,Pl(t,n),oi(t,r,a),si(t,r,a,n),Mi(null,t,r,!0,e,n);case 19:return Bi(e,t,n);case 22:return Ci(e,t,n)}throw Error(l(156,t.tag))};var Qu="function"===typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Yu(e){this._internalRoot=e}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Gu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Ju(){}function Zu(e,t,n,r,a){var l=n._reactRootContainer;if(l){var o=l;if("function"===typeof a){var i=a;a=function(){var e=qu(o);i.call(e)}}Vu(t,o,e,a)}else o=function(e,t,n,r,a){if(a){if("function"===typeof r){var l=r;r=function(){var e=qu(o);l.call(e)}}var o=Bu(t,r,e,0,null,!1,0,"",Ju);return e._reactRootContainer=o,e[ha]=o.current,Br(8===e.nodeType?e.parentNode:e),cu(),o}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=qu(s);i.call(e)}}var s=Uu(e,0,!1,null,0,!1,0,"",Ju);return e._reactRootContainer=s,e[ha]=s.current,Br(8===e.nodeType?e.parentNode:e),cu(function(){Vu(t,s,n,r)}),s}(n,t,e,a,r);return qu(o)}Yu.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Vu(e,t,null,null)},Yu.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu(function(){Vu(null,e,null,null)}),t[ha]=null}},Yu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ct();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Tt.length&&0!==t&&t<Tt[n].priority;n++);Tt.splice(n,0,e),0===n&&At(e)}},wt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),ru(t,Ge()),0===(6&Ms)&&(Bs=Ge()+500,Ba()))}break;case 13:cu(function(){var t=Fl(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}}),Hu(e,1)}},kt=function(e){if(13===e.tag){var t=Fl(e,134217728);if(null!==t)nu(t,e,134217728,eu());Hu(e,134217728)}},St=function(e){if(13===e.tag){var t=tu(e),n=Fl(e,t);if(null!==n)nu(n,e,t,eu());Hu(e,t)}},Ct=function(){return bt},Et=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},ke=function(e,t,n){switch(t){case"input":if(J(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=wa(r);if(!a)throw Error(l(90));Q(r),J(r,a)}}}break;case"textarea":le(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Me=uu,Pe=cu;var ec={usingClientEntryPoint:!1,Events:[ba,xa,wa,Ne,_e,uu]},tc={findFiberByHostInstance:va,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:x.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=We(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),lt=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xu(t))throw Error(l(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Xu(e))throw Error(l(299));var n=!1,r="",a=Qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Uu(e,1,!1,null,0,n,0,r,a),e[ha]=t.current,Br(8===e.nodeType?e.parentNode:e),new Ku(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=null===(e=We(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Gu(t))throw Error(l(200));return Zu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Xu(e))throw Error(l(405));var r=null!=n&&n.hydratedSources||null,a=!1,o="",i=Qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Bu(t,null,e,1,null!=n?n:null,a,0,o,i),e[ha]=t.current,Br(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Yu(t)},t.render=function(e,t,n){if(!Gu(t))throw Error(l(200));return Zu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Gu(e))throw Error(l(40));return!!e._reactRootContainer&&(cu(function(){Zu(null,null,e,!1,function(){e._reactRootContainer=null,e[ha]=null})}),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Gu(n))throw Error(l(200));if(null==e||void 0===e._reactInternals)throw Error(l(38));return Zu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},853:(e,t,n)=>{"use strict";e.exports=n(234)},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var l=t[r]={exports:{}};return e[r].call(l.exports,l,l.exports,n),l.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{"use strict";var e=n(43),t=n(391);let r={data:""},a=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||r,l=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,o=/\/\*[^]*?\*\/|  +/g,i=/\n+/g,s=(e,t)=>{let n="",r="",a="";for(let l in e){let o=e[l];"@"==l[0]?"i"==l[1]?n=l+" "+o+";":r+="f"==l[1]?s(o,l):l+"{"+s(o,"k"==l[1]?"":t)+"}":"object"==typeof o?r+=s(o,t?t.replace(/([^,])+/g,e=>l.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):l):null!=o&&(l=/^--/.test(l)?l:l.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=s.p?s.p(l,o):l+":"+o+";")}return n+(t&&a?t+"{"+a+"}":a)+r},u={},c=e=>{if("object"==typeof e){let t="";for(let n in e)t+=n+c(e[n]);return t}return e},d=(e,t,n,r,a)=>{let d=c(e),f=u[d]||(u[d]=(e=>{let t=0,n=11;for(;t<e.length;)n=101*n+e.charCodeAt(t++)>>>0;return"go"+n})(d));if(!u[f]){let t=d!==e?e:(e=>{let t,n,r=[{}];for(;t=l.exec(e.replace(o,""));)t[4]?r.shift():t[3]?(n=t[3].replace(i," ").trim(),r.unshift(r[0][n]=r[0][n]||{})):r[0][t[1]]=t[2].replace(i," ").trim();return r[0]})(e);u[f]=s(a?{["@keyframes "+f]:t}:t,n?"":"."+f)}let p=n&&u.g?u.g:null;return n&&(u.g=u[f]),((e,t,n,r)=>{r?t.data=t.data.replace(r,e):-1===t.data.indexOf(e)&&(t.data=n?e+t.data:t.data+e)})(u[f],t,r,p),f};function f(e){let t=this||{},n=e.call?e(t.p):e;return d(n.unshift?n.raw?((e,t,n)=>e.reduce((e,r,a)=>{let l=t[a];if(l&&l.call){let e=l(n),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;l=t?"."+t:e&&"object"==typeof e?e.props?"":s(e,""):!1===e?"":e}return e+r+(null==l?"":l)},""))(n,[].slice.call(arguments,1),t.p):n.reduce((e,n)=>Object.assign(e,n&&n.call?n(t.p):n),{}):n,a(t.target),t.g,t.o,t.k)}f.bind({g:1});let p,h,m,g=f.bind({k:1});function y(e,t){let n=this||{};return function(){let r=arguments;function a(l,o){let i=Object.assign({},l),s=i.className||a.className;n.p=Object.assign({theme:h&&h()},i),n.o=/ *go\d+/.test(s),i.className=f.apply(n,r)+(s?" "+s:""),t&&(i.ref=o);let u=e;return e[0]&&(u=i.as||e,delete i.as),m&&u[0]&&m(i),p(u,i)}return t?t(a):a}}var v=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,b=(()=>{let e=0;return()=>(++e).toString()})(),x=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),w=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:n}=t;return w(e,{type:e.toasts.find(e=>e.id===n.id)?1:0,toast:n});case 3:let{toastId:r}=t;return{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+a}))}}},k=[],S={toasts:[],pausedAt:void 0},C=e=>{S=w(S,e),k.forEach(e=>{e(S)})},E={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},N=e=>(t,n)=>{let r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"blank",n=arguments.length>2?arguments[2]:void 0;return{createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...n,id:(null==n?void 0:n.id)||b()}}(t,e,n);return C({type:2,toast:r}),r.id},_=(e,t)=>N("blank")(e,t);_.error=N("error"),_.success=N("success"),_.loading=N("loading"),_.custom=N("custom"),_.dismiss=e=>{C({type:3,toastId:e})},_.remove=e=>C({type:4,toastId:e}),_.promise=(e,t,n)=>{let r=_.loading(t.loading,{...n,...null==n?void 0:n.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let a=t.success?v(t.success,e):void 0;return a?_.success(a,{id:r,...n,...null==n?void 0:n.success}):_.dismiss(r),e}).catch(e=>{let a=t.error?v(t.error,e):void 0;a?_.error(a,{id:r,...n,...null==n?void 0:n.error}):_.dismiss(r)}),e};var M=(e,t)=>{C({type:1,toast:{id:e,height:t}})},P=()=>{C({type:5,time:Date.now()})},z=new Map,j=t=>{let{toasts:n,pausedAt:r}=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[n,r]=(0,e.useState)(S),a=(0,e.useRef)(S);(0,e.useEffect)(()=>(a.current!==S&&r(S),k.push(r),()=>{let e=k.indexOf(r);e>-1&&k.splice(e,1)}),[]);let l=n.toasts.map(e=>{var n,r,a;return{...t,...t[e.type],...e,removeDelay:e.removeDelay||(null==(n=t[e.type])?void 0:n.removeDelay)||(null==t?void 0:t.removeDelay),duration:e.duration||(null==(r=t[e.type])?void 0:r.duration)||(null==t?void 0:t.duration)||E[e.type],style:{...t.style,...null==(a=t[e.type])?void 0:a.style,...e.style}}});return{...n,toasts:l}}(t);(0,e.useEffect)(()=>{if(r)return;let e=Date.now(),t=n.map(t=>{if(t.duration===1/0)return;let n=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(n<0))return setTimeout(()=>_.dismiss(t.id),n);t.visible&&_.dismiss(t.id)});return()=>{t.forEach(e=>e&&clearTimeout(e))}},[n,r]);let a=(0,e.useCallback)(()=>{r&&C({type:6,time:Date.now()})},[r]),l=(0,e.useCallback)((e,t)=>{let{reverseOrder:r=!1,gutter:a=8,defaultPosition:l}=t||{},o=n.filter(t=>(t.position||l)===(e.position||l)&&t.height),i=o.findIndex(t=>t.id===e.id),s=o.filter((e,t)=>t<i&&e.visible).length;return o.filter(e=>e.visible).slice(...r?[s+1]:[0,s]).reduce((e,t)=>e+(t.height||0)+a,0)},[n]);return(0,e.useEffect)(()=>{n.forEach(e=>{if(e.dismissed)!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;if(z.has(e))return;let n=setTimeout(()=>{z.delete(e),C({type:4,toastId:e})},t);z.set(e,n)}(e.id,e.removeDelay);else{let t=z.get(e.id);t&&(clearTimeout(t),z.delete(e.id))}})},[n]),{toasts:n,handlers:{updateHeight:M,startPause:P,endPause:a,calculateOffset:l}}},R=g`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,T=g`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,F=g`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,D=y("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${R} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${T} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${F} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,L=g`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,A=y("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${L} 1s linear infinite;
`,I=g`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,O=g`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,U=y("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${I} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${O} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,$=y("div")`
  position: absolute;
`,B=y("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,V=g`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,q=y("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${V} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,W=t=>{let{toast:n}=t,{icon:r,type:a,iconTheme:l}=n;return void 0!==r?"string"==typeof r?e.createElement(q,null,r):r:"blank"===a?null:e.createElement(B,null,e.createElement(A,{...l}),"loading"!==a&&e.createElement($,null,"error"===a?e.createElement(D,{...l}):e.createElement(U,{...l})))},H=e=>`\n0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`,Q=e=>`\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}\n`,K=y("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Y=y("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,X=e.memo(t=>{let{toast:n,position:r,style:a,children:l}=t,o=n.height?((e,t)=>{let n=e.includes("top")?1:-1,[r,a]=x()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[H(n),Q(n)];return{animation:t?`${g(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${g(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}})(n.position||r||"top-center",n.visible):{opacity:0},i=e.createElement(W,{toast:n}),s=e.createElement(Y,{...n.ariaProps},v(n.message,n));return e.createElement(K,{className:n.className,style:{...o,...a,...n.style}},"function"==typeof l?l({icon:i,message:s}):e.createElement(e.Fragment,null,i,s))});!function(e,t,n,r){s.p=t,p=e,h=n,m=r}(e.createElement);var G=t=>{let{id:n,className:r,style:a,onHeightUpdate:l,children:o}=t,i=e.useCallback(e=>{if(e){let t=()=>{let t=e.getBoundingClientRect().height;l(n,t)};t(),new MutationObserver(t).observe(e,{subtree:!0,childList:!0,characterData:!0})}},[n,l]);return e.createElement("div",{ref:i,className:r,style:a},o)},J=f`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Z=t=>{let{reverseOrder:n,position:r="top-center",toastOptions:a,gutter:l,children:o,containerStyle:i,containerClassName:s}=t,{toasts:u,handlers:c}=j(a);return e.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...i},className:s,onMouseEnter:c.startPause,onMouseLeave:c.endPause},u.map(t=>{let a=t.position||r,i=((e,t)=>{let n=e.includes("top"),r=n?{top:0}:{bottom:0},a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:x()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(n?1:-1)}px)`,...r,...a}})(a,c.calculateOffset(t,{reverseOrder:n,gutter:l,defaultPosition:r}));return e.createElement(G,{id:t.id,key:t.id,onHeightUpdate:c.updateHeight,className:t.visible?J:"",style:i},"custom"===t.type?v(t.message,t):o?o(t):e.createElement(X,{toast:t,position:a}))}))},ee=_,te={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};var ne=(t,n)=>{const r=(0,e.forwardRef)((r,a)=>{let{color:l="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,children:u,...c}=r;return(0,e.createElement)("svg",{ref:a,...te,width:o,height:o,stroke:l,strokeWidth:s?24*Number(i)/Number(o):i,className:`lucide lucide-${d=t,d.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,...c},[...n.map(t=>{let[n,r]=t;return(0,e.createElement)(n,r)}),...(Array.isArray(u)?u:[u])||[]]);var d});return r.displayName=`${t}`,r};const re=ne("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]),ae=ne("Wifi",[["path",{d:"M5 13a10 10 0 0 1 14 0",key:"6v8j51"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),le=ne("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var oe=n(507),ie=n.n(oe);const se=new Int16Array(256);!function(){for(let e=0;e<256;e++){let t=e;t=255&~t;let n=132+((15&t)<<3);n<<=(112&t)>>4;const r=128&t?132-n:n-132;se[e]=r}console.log("\u2705 \xb5-law decode table initialized with",se.length,"entries"),console.log("\ud83d\udcca Key values: 0x00\u2192",se[0],"0x80\u2192",se[128],"0xFF\u2192",se[255],"0x7F\u2192",se[127])}();const ue=new class{constructor(){this.pcmPlayer=null,this.isInitialized=!1,this.sampleRate=8e3,this.channels=1,this.bitDepth=16}initialize(){if(!this.isInitialized)try{this.pcmPlayer=new(ie())({inputCodec:"Int16",channels:this.channels,sampleRate:this.sampleRate,flushTime:50,volume:1,onstatechange:e=>{console.log("\ud83d\udd0a PCMPlayer state:",e)}}),this.isInitialized=!0,console.log("\u2705 PCMPlayer initialized:",{sampleRate:this.sampleRate,channels:this.channels,bitDepth:this.bitDepth}),this.logVolumeInfo()}catch(e){throw console.error("\u274c Failed to initialize PCMPlayer:",e),e}}muLawToPcm16(e){const t=new Int16Array(e.length);for(let n=0;n<e.length;n++)t[n]=se[e[n]];return t}async processAndPlayAudio(e){try{console.log("\ud83c\udfb5 Processing audio - base64 length:",e.length),this.isInitialized||this.initialize();const t=atob(e),n=new Uint8Array(t.length);for(let e=0;e<t.length;e++)n[e]=t.charCodeAt(e);console.log("\ud83d\udce6 Decoded \xb5-law data:",{length:n.length,firstBytes:Array.from(n.slice(0,8)),duration:(n.length/this.sampleRate*1e3).toFixed(1)+"ms"});const r=this.muLawToPcm16(n),a=Math.max(...r),l=Math.min(...r),o=Math.sqrt(r.reduce((e,t)=>e+t*t,0)/r.length);console.log("\ud83d\udd0a PCM data converted:",{length:r.length,range:`${l} to ${a}`,rms:o.toFixed(1),sample:Array.from(r.slice(0,8))}),o<10&&console.warn("\u26a0\ufe0f Audio appears to be very quiet (RMS < 10)"),this.pcmPlayer.feed(r),console.log("\u2705 Audio fed to PCMPlayer")}catch(t){throw console.error("\u274c Error processing audio:",t),t}}enhanceAudio(e){const t=new Int16Array(e.length);let n=0;for(let l=0;l<e.length;l++)n+=e[l]*e[l];const r=Math.sqrt(n/e.length),a=r>0?Math.min(8192/r,4):1;for(let l=0;l<e.length;l++){let n=e[l]*a;n>16383?n=16383+.3*(n-16383):n<-16384&&(n=.3*(n+16384)-16384),t[l]=Math.max(-32768,Math.min(32767,Math.round(n)))}return console.log("\ud83c\udf9a\ufe0f Audio enhanced:",{originalRms:r.toFixed(1),targetRms:8192,gainApplied:a.toFixed(2)}),t}testAudio(){this.isInitialized||this.initialize(),console.log("\ud83c\udfb5 Testing audio with 440Hz tone...");const e=Math.floor(.5*this.sampleRate),t=new Int16Array(e);for(let n=0;n<e;n++){const e=16e3*Math.sin(2*Math.PI*440*n/this.sampleRate);t[n]=Math.round(e)}this.pcmPlayer.feed(t),console.log("\u2705 Test tone should be playing")}stop(){this.pcmPlayer&&(this.pcmPlayer.destroy(),this.pcmPlayer=null,this.isInitialized=!1,console.log("\ud83d\uded1 PCMPlayer stopped and destroyed"))}getStatus(){const e={isInitialized:this.isInitialized,sampleRate:this.sampleRate,channels:this.channels,bitDepth:this.bitDepth};return this.pcmPlayer&&(e.volume=1,e.playerState=this.pcmPlayer.state||"unknown"),e}logVolumeInfo(){console.log("\ud83d\udd0a Volume Configuration:",{pcmPlayerVolume:1,sampleRate:this.sampleRate,bitDepth:this.bitDepth,isInitialized:this.isInitialized,recommendation:"If volume is low, check browser/system volume settings"})}};"undefined"!==typeof window&&(window.simpleAudioProcessor=ue);const ce=132,de=new Array(256);!function(){for(let e=0;e<256;e++){const t=255&~e,n=(112&t)>>4,r=15&t;let a;a=0===n?(r<<4)+ce:((16|r)<<n+3)+ce,a-=ce,128&t&&(a=-a),de[e]=a/32768}console.log("\xb5-law decode table initialized with sample values:",{silence:de[255],positive:de[135],negative:de[7]})}();class fe{constructor(e){this.audioContext=e,this.queue=[],this.isPlaying=!1,this.nextStartTime=0,this.currentSource=null,this.scheduledBuffers=[]}enqueue(e){e&&0!==e.length?(console.log("\ud83c\udfb5 AudioQueue.enqueue - Adding buffer:",{duration:e.duration.toFixed(3)+"s",length:e.length,channels:e.numberOfChannels,sampleRate:e.sampleRate}),this.queue.push(e),console.log("\ud83d\udccb Queue length now:",this.queue.length,"isPlaying:",this.isPlaying),this.isPlaying||this.startPlayback()):console.warn("\ud83d\udeab AudioQueue.enqueue - Invalid or empty audio buffer")}startPlayback(){0===this.queue.length||this.isPlaying||(console.log("\ud83c\udfa7 AudioQueue.startPlayback - Starting continuous playback"),this.isPlaying=!0,"running"!==this.audioContext.state?(console.log("\ud83d\udd04 Resuming AudioContext..."),this.audioContext.resume().then(()=>{console.log("\u2705 AudioContext resumed"),this.scheduleNextBuffer()})):this.scheduleNextBuffer())}scheduleNextBuffer(){if(0===this.queue.length)return console.log("\ud83d\udccb AudioQueue - Queue empty, stopping playback"),this.isPlaying=!1,void(this.currentSource=null);const e=this.queue.shift(),t=this.audioContext.currentTime,n=Math.max(t+.01,this.nextStartTime);console.log("\ud83c\udfb6 AudioQueue.scheduleNextBuffer:",{currentTime:t.toFixed(3),startTime:n.toFixed(3),duration:e.duration.toFixed(3)});const r=this.audioContext.createBufferSource();r.buffer=e,r.connect(this.audioContext.destination);const a=e.getChannelData(0),l=Math.sqrt(a.reduce((e,t)=>e+t*t,0)/a.length);console.log("\ud83d\udcca Audio content RMS:",l.toFixed(4),"peak:",Math.max(...a.slice(0,100)).toFixed(4)),r.start(n),this.currentSource=r,this.nextStartTime=n+e.duration,r.onended=()=>{console.log("\u2705 AudioQueue - Buffer finished, scheduling next..."),this.scheduleNextBuffer()},r.onerror=e=>{console.error("\u274c AudioQueue - Playback error:",e),this.scheduleNextBuffer()}}clear(){if(console.log("\ud83e\uddf9 AudioQueue.clear - Clearing queue and stopping playback"),this.currentSource){try{this.currentSource.stop()}catch(r){}this.currentSource=null}this.queue=[],this.isPlaying=!1,this.nextStartTime=0,this.scheduledBuffers=[]}testAudioPlayback(){if(!this.audioContext)return void console.error("\u274c AudioContext not available for test");console.log("\ud83c\udfb5 Testing audio playback with 440Hz tone...");const e=this.audioContext.sampleRate,t=.5*e,n=this.audioContext.createBuffer(1,t,e),r=n.getChannelData(0);for(let a=0;a<t;a++)r[a]=.2*Math.sin(2*Math.PI*440*a/e);this.enqueue(n),console.log("\u2705 Test tone queued - should hear 440Hz for 0.5 seconds")}getStatus(){return{queueLength:this.queue.length,isPlaying:this.isPlaying,nextStartTime:this.nextStartTime,audioContextState:this.audioContext.state}}}const pe=new class{constructor(){this.sampleRate=8e3,this.chunkDuration=20,this.chunkSize=this.sampleRate*this.chunkDuration/1e3,this.audioBuffer=[]}pcmToMuLaw(e){const t=new Uint8Array(e.length);for(let n=0;n<e.length;n++){let r=Math.max(-1,Math.min(1,e[n]));r=Math.round(32767*r);const a=r<0?128:0;r=Math.abs(r),r>32635&&(r=32635),r+=ce;let l=7;for(let e=0;e<8;e++)if(r<=31<<e+3){l=e;break}const o=r>>l+3&15;t[n]=~(a|l<<4|o)}return t}muLawToPcm(e){const t=new Float32Array(e.length);for(let n=0;n<e.length;n++)t[n]=de[e[n]];return t}resample(e,t,n){if(t===n)return e;const r=t/n,a=Math.round(e.length/r),l=new Float32Array(a);for(let o=0;o<a;o++){const t=o*r,n=Math.floor(t),a=t-n;n+1<e.length?l[o]=e[n]*(1-a)+e[n+1]*a:n<e.length?l[o]=e[n]:l[o]=0}return console.log(`Resampled audio: ${e.length} samples @ ${t}Hz \u2192 ${l.length} samples @ ${n}Hz`),l}processInputAudio(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:44100,n=e;t!==this.sampleRate&&(n=this.resample(e,t,this.sampleRate)),this.audioBuffer.push(...n);const r=[];for(;this.audioBuffer.length>=this.chunkSize;){const e=this.audioBuffer.splice(0,this.chunkSize),t=new Float32Array(e),n=this.pcmToMuLaw(t);r.push(n)}return r}async processReceivedAudio(e,t){try{console.log("\ud83c\udfb5 Processing received audio - base64 length:",e.length);const r=atob(e);console.log("\ud83d\udce6 Decoded binary string length:",r.length);const a=new Uint8Array(r.length);for(let e=0;e<r.length;e++)a[e]=r.charCodeAt(e);console.log("\ud83d\udd22 \xb5-law data created, length:",a.length,"first 10 bytes:",Array.from(a.slice(0,10)));const l=this.muLawToPcm(a);console.log("\ud83d\udd0a PCM data converted, length:",l.length);const o=Math.max(...l),i=Math.min(...l),s=Math.sqrt(l.reduce((e,t)=>e+t*t,0)/l.length);if(console.log("\ud83d\udcca PCM analysis - max:",o.toFixed(4),"min:",i.toFixed(4),"RMS:",s.toFixed(4)),console.log("\ud83c\udf9a\ufe0f Sample preview:",l.slice(0,10).map(e=>e.toFixed(4))),o>1||i<-1){console.warn("\u26a0\ufe0f PCM values out of range, clamping to [-1.0, 1.0]");for(let e=0;e<l.length;e++)l[e]=Math.max(-1,Math.min(1,l[e]))}let u={type:"unknown",isBeep:!1,dominantFreq:0,details:"Analysis skipped"};try{u=this.analyzeAudioChunk(l),u.isBeep&&console.warn(`\u26a0\ufe0f BEEP DETECTED: ${u.details}`)}catch(n){console.warn("\u26a0\ufe0f Audio analysis failed, continuing with basic processing:",n.message)}const c={maxValue:o,minValue:i,rmsValue:s,originalDuration:l.length/this.sampleRate,audioType:u.type,dominantFreq:u.dominantFreq,isBeep:u.isBeep};he.recordChunk(e,a,l,c),s<.001?console.warn("\u26a0\ufe0f Audio appears to be mostly silence (RMS < 0.001)"):console.log("\u2705 Audio has content (RMS:",s.toFixed(4),")");const d=l.length/this.sampleRate;console.log("\u23f1\ufe0f Original duration at 8kHz:",d.toFixed(3),"seconds");const f=t.sampleRate;console.log("\ud83d\udd04 Resampling from",this.sampleRate,"Hz to",f,"Hz");const p=this.resample(l,this.sampleRate,f);if(0===p.length)return console.error("\u274c Resampling failed - no output data"),null;const h=p.length/f;console.log("\u23f1\ufe0f Final duration at",f+"Hz:",h.toFixed(3),"seconds"),console.log("\ud83c\udfb5 Creating AudioBuffer with",p.length,"samples at",f,"Hz");const m=t.createBuffer(1,p.length,f),g=m.getChannelData(0);g.set(p);const y=Math.sqrt(g.reduce((e,t)=>e+t*t,0)/g.length);return console.log("\ud83d\udcca AudioBuffer RMS:",y.toFixed(4)),console.log("\u2705 AudioBuffer created successfully:",{channels:m.numberOfChannels,length:m.length,duration:m.duration.toFixed(3)+"s",sampleRate:m.sampleRate,bufferRms:y.toFixed(4)}),m}catch(r){return console.error("\u274c Error processing received audio:",r),console.error("Stack trace:",r.stack),null}}encodeToBase64(e){let t="";for(let n=0;n<e.length;n++)t+=String.fromCharCode(e[n]);return btoa(t)}calculateAudioLevel(e){let t=0;for(let r=0;r<e.length;r++)t+=e[r]*e[r];const n=Math.sqrt(t/e.length);return Math.min(100,100*n)}applyNoiseGate(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.01;const n=new Float32Array(e.length);for(let r=0;r<e.length;r++){const a=Math.abs(e[r]);n[r]=a>t?e[r]:0}return n}calculateFrequencyMagnitude(e,t,n){try{let r=0,a=0;const l=2*Math.PI*t/n;for(let t=0;t<e.length;t++)r+=e[t]*Math.cos(l*t),a+=e[t]*Math.sin(l*t);return Math.sqrt(r*r+a*a)/e.length}catch(r){return console.warn("Error calculating frequency magnitude:",r),0}}analyzeAudioChunk(e){try{if(0===e.length)return{type:"empty",isBeep:!1,dominantFreq:0,details:"No data"};const t=Math.sqrt(e.reduce((e,t)=>e+t*t,0)/e.length);if(t<.001)return{type:"silence",isBeep:!1,dominantFreq:0,details:"Silence"};const n=[440,800,1e3,1500];let r=0,a=0;for(const s of n){const t=this.calculateFrequencyMagnitude(e,s,this.sampleRate);t>r&&(r=t,a=s)}const l=r>.2,o=this.detectShortPattern(e),i=l||o;return{type:i?"beep":"audio",isBeep:i,dominantFreq:a,magnitude:r,details:i?`Beep at ${a}Hz (magnitude: ${r.toFixed(3)})`:"Normal audio content",rms:t}}catch(t){return console.warn("Error in audio chunk analysis:",t),{type:"error",isBeep:!1,dominantFreq:0,details:"Analysis failed: "+t.message}}}detectShortPattern(e){if(e.length<32)return!1;const t=[8,16,20,32];for(const n of t){if(3*n>e.length)continue;let t=0,r=0;for(let a=0;a<e.length-2*n;a+=n)for(let l=0;l<n&&a+l+n<e.length;l++){const o=Math.abs(e[a+l]-e[a+l+n]);t+=1-Math.min(o,1),r++}if(t/r>.8)return!0}return!1}clearBuffer(){this.audioBuffer=[]}getBufferSize(){return this.audioBuffer.length}};const he=new class{constructor(){this.isRecording=!1,this.recordedChunks=[],this.rawMuLawData=[],this.pcmData=[],this.metadata={startTime:null,endTime:null,totalChunks:0,totalDuration:0,sampleRate:8e3,processingStats:[]}}startRecording(){console.log("\ud83c\udf99\ufe0f AudioRecorder: Starting recording session"),this.isRecording=!0,this.recordedChunks=[],this.rawMuLawData=[],this.pcmData=[],this.metadata={startTime:Date.now(),endTime:null,totalChunks:0,totalDuration:0,sampleRate:8e3,processingStats:[]}}recordChunk(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(!this.isRecording)return;const a=Date.now(),l={timestamp:a,base64Length:e.length,muLawLength:t.length,pcmLength:n.length,duration:n.length/this.metadata.sampleRate,...r};this.recordedChunks.push({timestamp:a,base64Data:e,muLawData:new Uint8Array(t),pcmData:new Float32Array(n)}),this.rawMuLawData.push(...t),this.pcmData.push(...n),this.metadata.totalChunks++,this.metadata.totalDuration+=l.duration,this.metadata.processingStats.push(l),console.log(`\ud83d\udcdd Recorded chunk ${this.metadata.totalChunks}: ${l.duration.toFixed(3)}s (total: ${this.metadata.totalDuration.toFixed(3)}s)`)}stopRecording(){this.isRecording&&(console.log("\ud83d\uded1 AudioRecorder: Stopping recording session"),this.isRecording=!1,this.metadata.endTime=Date.now(),console.log("\ud83d\udcca Recording complete:",{duration:this.metadata.totalDuration.toFixed(3)+"s",chunks:this.metadata.totalChunks,totalSamples:this.pcmData.length}))}createWavFile(){if(0===this.pcmData.length)return console.warn("\u26a0\ufe0f No PCM data to create WAV file"),null;const e=this.metadata.sampleRate,t=this.pcmData.length,n=new Int16Array(t);for(let s=0;s<t;s++){const e=Math.max(-1,Math.min(1,this.pcmData[s]));n[s]=Math.round(32767*e)}const r=2*t,a=44+r,l=new ArrayBuffer(a),o=new DataView(l);let i=0;o.setUint32(i,1380533830,!1),i+=4,o.setUint32(i,a-8,!0),i+=4,o.setUint32(i,1463899717,!1),i+=4,o.setUint32(i,1718449184,!1),i+=4,o.setUint32(i,16,!0),i+=4,o.setUint16(i,1,!0),i+=2,o.setUint16(i,1,!0),i+=2,o.setUint32(i,e,!0),i+=4,o.setUint32(i,1*e*2,!0),i+=4,o.setUint16(i,2,!0),i+=2,o.setUint16(i,16,!0),i+=2,o.setUint32(i,1684108385,!1),i+=4,o.setUint32(i,r,!0),i+=4;for(let s=0;s<t;s++)o.setInt16(i,n[s],!0),i+=2;return console.log("\ud83c\udfb5 Created WAV file:",{size:a+" bytes",duration:(t/e).toFixed(3)+"s",sampleRate:e+"Hz",samples:t}),new Blob([l],{type:"audio/wav"})}createRawMuLawFile(){if(0===this.rawMuLawData.length)return console.warn("\u26a0\ufe0f No \xb5-law data to create raw file"),null;const e=new Uint8Array(this.rawMuLawData);return console.log("\ud83d\udcc1 Created raw \xb5-law file:",{size:e.length+" bytes",estimatedDuration:(e.length/this.metadata.sampleRate).toFixed(3)+"s"}),new Blob([e],{type:"application/octet-stream"})}createMetadataFile(){const e=this.analyzeAudioContent(),t={...this.metadata,sessionDuration:this.metadata.endTime-this.metadata.startTime,averageChunkSize:this.rawMuLawData.length/this.metadata.totalChunks,summary:{totalAudioDuration:this.metadata.totalDuration,totalChunks:this.metadata.totalChunks,totalBytes:this.rawMuLawData.length,averageRMS:this.calculateAverageRMS()},audioAnalysis:{type:e.type,confidence:e.confidence,details:e.details,rms:e.rms,dynamicRange:e.dynamicRange,dominantFreq:e.dominantFreq,isRepetitive:e.isRepetitive,warning:"pure_tone"===e.type||"beep_pattern"===e.type?"DETECTED BEEP/TONE INSTEAD OF SPEECH - Backend may be sending placeholder audio":null}},n=JSON.stringify(t,null,2);return console.log("\ud83d\udccb Created metadata file:",t.summary),console.log("\ud83d\udd0d Audio Content Analysis:",e),"pure_tone"!==e.type&&"beep_pattern"!==e.type||(console.warn("\u26a0\ufe0f PROBLEM IDENTIFIED: Backend is sending beep/tone data instead of AI speech!"),console.warn("\ud83d\udd27 This indicates the TTS (Text-to-Speech) system is not working properly on the backend.")),new Blob([n],{type:"application/json"})}calculateAverageRMS(){if(0===this.pcmData.length)return 0;const e=this.pcmData.reduce((e,t)=>e+t*t,0);return Math.sqrt(e/this.pcmData.length)}analyzeAudioContent(){try{if(0===this.pcmData.length)return{type:"empty",confidence:1,details:"No audio data"};const e=this.pcmData,t=this.metadata.sampleRate,n=this.calculateAverageRMS(),r=Math.max(...e),a=r-Math.min(...e);if(n<.001)return{type:"silence",confidence:1,details:`RMS too low: ${n.toFixed(6)}`,rms:n,dynamicRange:a};const l=this.performSimpleFrequencyAnalysis(e,t),o=this.detectRepetitivePatterns(e);let i="unknown",s=.5,u="";return l.isPureTone?(i="pure_tone",s=.9,u=`Pure tone detected at ~${l.dominantFreq.toFixed(1)}Hz`):o.isRepetitive?(i="beep_pattern",s=.8,u=`Repetitive beep pattern detected, period: ${o.period}ms`):l.hasVariedSpectrum&&n>.01?(i="speech_like",s=.7,u="Varied frequency spectrum suggesting speech"):(i="noise_or_tone",s=.6,u="Consistent tone or noise pattern"),{type:i,confidence:s,details:u,rms:n,dynamicRange:a,dominantFreq:l.dominantFreq,spectralVariance:l.spectralVariance,isRepetitive:o.isRepetitive,duration:e.length/t}}catch(e){return console.warn("Error in audio content analysis:",e),{type:"error",confidence:0,details:"Analysis failed: "+e.message,rms:0,dynamicRange:0}}}performSimpleFrequencyAnalysis(e,t){try{const n=Math.min(1024,e.length),r=[440,800,1e3,1500,2e3,3e3];let a=0,l=0;for(const i of r){const r=this.calculateSimpleFrequencyMagnitude(e.slice(0,n),i,t);r>l&&(l=r,a=i)}const o=this.calculateSpectralVariance(e.slice(0,n),t);return{dominantFreq:a,maxMagnitude:l,spectralVariance:o,isPureTone:l>.3&&o<.1,hasVariedSpectrum:o>.2}}catch(n){return console.warn("Error in frequency analysis:",n),{dominantFreq:0,maxMagnitude:0,spectralVariance:0,isPureTone:!1,hasVariedSpectrum:!1}}}calculateSimpleFrequencyMagnitude(e,t,n){try{let r=0,a=0;const l=2*Math.PI*t/n;for(let t=0;t<e.length;t++)r+=e[t]*Math.cos(l*t),a+=e[t]*Math.sin(l*t);return Math.sqrt(r*r+a*a)/e.length}catch(r){return 0}}calculateFrequencyMagnitude(e,t,n){try{let r=0,a=0;const l=2*Math.PI*t/n;for(let t=0;t<e.length;t++)r+=e[t]*Math.cos(l*t),a+=e[t]*Math.sin(l*t);return Math.sqrt(r*r+a*a)/e.length}catch(r){return 0}}calculateSpectralVariance(e,t){try{const n=[200,400,800,1600,3200,6400].map(n=>this.calculateSimpleFrequencyMagnitude(e,n,t)),r=n.reduce((e,t)=>e+t,0)/n.length;return n.reduce((e,t)=>e+Math.pow(t-r,2),0)/n.length}catch(n){return 0}}detectRepetitivePatterns(e){if(e.length<160)return{isRepetitive:!1,period:0};const t=[80,160,320,480,640];for(const n of t){if(3*n>e.length)continue;let t=0,r=0;for(let l=0;l<e.length-2*n;l+=n)for(let a=0;a<Math.min(n,e.length-l-n);a++)t+=Math.abs(e[l+a]-e[l+a+n]),r++;const a=t/r;if(a<.1)return{isRepetitive:!0,period:n/8*1e3,similarity:1-a}}return{isRepetitive:!1,period:0}}async downloadRecording(){this.metadata.endTime||this.stopRecording(),console.log("\u2b07\ufe0f Starting audio download...");const e=this.createWavFile();e&&this.downloadBlob(e,"received_audio.wav");const t=this.createRawMuLawFile();t&&this.downloadBlob(t,"received_audio_raw.bin");const n=this.createMetadataFile();n&&this.downloadBlob(n,"audio_metadata.json"),console.log("\u2705 Audio download complete!")}downloadBlob(e,t){const n=URL.createObjectURL(e),r=document.createElement("a");r.href=n,r.download=t,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(n),console.log(`\ud83d\udcbe Downloaded: ${t}`)}clearRecording(){console.log("\ud83d\uddd1\ufe0f Clearing recorded audio data"),this.recordedChunks=[],this.rawMuLawData=[],this.pcmData=[],this.isRecording=!1}getStatus(){return{isRecording:this.isRecording,totalChunks:this.metadata.totalChunks,totalDuration:this.metadata.totalDuration,totalBytes:this.rawMuLawData.length}}};"undefined"!==typeof window&&(window.audioProcessor=pe,window.AudioQueue=fe,window.audioRecorder=he);const me="disconnected",ge="connecting",ye="connected",ve="error",be="idle",xe="connecting",we="in_call",ke="ending",Se="ended",Ce="error",Ee="connected",Ne="start",_e="media",Me="stop",Pe="mark",ze="clear",je="user_register",Re="call_start",Te="call_end",Fe="idle",De="requesting",Le="granted",Ae="denied",Ie="error",Oe=ne("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),Ue=ne("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),$e=ne("PhoneOff",[["path",{d:"M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91",key:"z86iuo"}],["line",{x1:"22",x2:"2",y1:"2",y2:"22",key:"11kh81"}]]),Be=ne("MicOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]),Ve=ne("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]),qe=ne("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]),We=ne("VolumeX",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);var He=n(579);const Qe=t=>{let{callState:n,isConnected:r,isMuted:a,onStartCall:l,onEndCall:o,onToggleMute:i,audioLevel:s=0}=t;const[u,c]=(0,e.useState)(!0),d=n===we,f=n===xe,p=n===ke,h=r&&n===be,m=d||f,g=d||f,y=()=>{if(!d||a)return null;const e=Math.ceil(s/100*5);return(0,He.jsx)("div",{className:"flex items-center space-x-1 ml-2",children:Array.from({length:5},(t,n)=>(0,He.jsx)("div",{className:"w-1 h-4 rounded-full transition-colors duration-150 "+(n<e?n<2?"bg-success-500":n<4?"bg-yellow-500":"bg-danger-500":"bg-gray-300")},n))})};return(0,He.jsxs)("div",{className:"card p-6",children:[(0,He.jsxs)("div",{className:"text-center mb-6",children:[(0,He.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Call Controls"}),(0,He.jsxs)("p",{className:"text-sm text-gray-600",children:[!r&&"Connect to server to start calling",r&&n===be&&"Ready to start call",f&&"Connecting to call...",d&&"Call in progress",p&&"Ending call...",n===Se&&"Call ended",n===Ce&&"Call error occurred"]})]}),(0,He.jsx)("div",{className:"flex justify-center mb-6",children:d||f||p?(0,He.jsxs)("button",{onClick:()=>{m&&o()},disabled:!m,className:`\n              relative w-16 h-16 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-4 focus:ring-offset-2 shadow-lg\n              ${m?"bg-danger-600 hover:bg-danger-700 focus:ring-danger-500 text-white":"bg-gray-300 text-gray-500 cursor-not-allowed"}\n            `,children:[p?(0,He.jsx)(Ue,{size:24,className:"animate-spin"}):(0,He.jsx)($e,{size:24}),d&&(0,He.jsx)("div",{className:"absolute inset-0 rounded-full bg-danger-600 animate-pulse opacity-30"})]}):(0,He.jsxs)("button",{onClick:()=>{h&&l()},disabled:!h,className:`\n              relative w-16 h-16 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-4 focus:ring-offset-2 shadow-lg\n              ${h?"bg-success-600 hover:bg-success-700 focus:ring-success-500 text-white":"bg-gray-300 text-gray-500 cursor-not-allowed"}\n            `,children:[(0,He.jsx)(Oe,{size:24}),h&&(0,He.jsx)("div",{className:"absolute inset-0 rounded-full bg-success-600 animate-ping opacity-20"})]})}),(0,He.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,He.jsxs)("div",{className:"flex flex-col items-center",children:[(0,He.jsx)("button",{onClick:()=>{g&&i()},disabled:!g,className:`\n              w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              ${g?a?"bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500":"bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500":"bg-gray-200 text-gray-400 cursor-not-allowed"}\n            `,children:a?(0,He.jsx)(Be,{size:20}):(0,He.jsx)(Ve,{size:20})}),(0,He.jsxs)("div",{className:"flex items-center mt-1",children:[(0,He.jsx)("span",{className:"text-xs text-gray-500",children:a?"Muted":"Mic"}),(0,He.jsx)(y,{})]})]}),(0,He.jsxs)("div",{className:"flex flex-col items-center",children:[(0,He.jsx)("button",{onClick:()=>{c(e=>!e)},disabled:!g,className:`\n              w-12 h-12 rounded-full flex items-center justify-center\n              transition-all duration-200 transform hover:scale-105 focus:outline-none\n              focus:ring-2 focus:ring-offset-2 shadow-md\n              ${g?u?"bg-gray-100 text-gray-600 hover:bg-gray-200 focus:ring-gray-500":"bg-danger-100 text-danger-600 hover:bg-danger-200 focus:ring-danger-500":"bg-gray-200 text-gray-400 cursor-not-allowed"}\n            `,children:u?(0,He.jsx)(qe,{size:20}):(0,He.jsx)(We,{size:20})}),(0,He.jsx)("span",{className:"text-xs text-gray-500 mt-1",children:u?"Speaker":"Muted"})]})]}),f&&(0,He.jsxs)("div",{className:"mt-6 flex items-center justify-center space-x-2",children:[(0,He.jsx)(Ue,{size:16,className:"animate-spin text-primary-600"}),(0,He.jsx)("span",{className:"text-sm text-primary-600",children:"Connecting to call..."})]}),d&&(0,He.jsx)("div",{className:"mt-6 text-center",children:(0,He.jsxs)("div",{className:"inline-flex items-center space-x-2 px-3 py-1 bg-success-100 text-success-800 rounded-full text-sm",children:[(0,He.jsx)("div",{className:"w-2 h-2 bg-success-500 rounded-full animate-pulse"}),(0,He.jsx)("span",{children:"Call Active"})]})}),n===Ce&&(0,He.jsx)("div",{className:"mt-6 text-center",children:(0,He.jsxs)("div",{className:"inline-flex items-center space-x-2 px-3 py-1 bg-danger-100 text-danger-800 rounded-full text-sm",children:[(0,He.jsx)("div",{className:"w-2 h-2 bg-danger-500 rounded-full"}),(0,He.jsx)("span",{children:"Call Error"})]})}),(0,He.jsx)("div",{className:"mt-6 text-center",children:(0,He.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[!r&&(0,He.jsx)("p",{children:"Please connect to the server first"}),r&&n===be&&(0,He.jsx)("p",{children:"Click the green phone button to start a call"}),d&&(0,He.jsxs)(He.Fragment,{children:[(0,He.jsx)("p",{children:"Use the microphone button to mute/unmute"}),(0,He.jsx)("p",{children:"Click the red phone button to end the call"})]})]})})]})},Ke=ne("WifiOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M8.5 16.5a5 5 0 0 1 7 0",key:"sej527"}],["path",{d:"M2 8.82a15 15 0 0 1 4.17-2.65",key:"11utq1"}],["path",{d:"M10.66 5c4.01-.36 8.14.9 11.34 3.76",key:"hxefdu"}],["path",{d:"M16.85 11.25a10 10 0 0 1 2.22 1.68",key:"q734kn"}],["path",{d:"M5 13a10 10 0 0 1 5.24-2.76",key:"piq4yl"}],["line",{x1:"12",x2:"12.01",y1:"20",y2:"20",key:"of4bc4"}]]),Ye=ne("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),Xe=ne("Signal",[["path",{d:"M2 20h.01",key:"4haj6o"}],["path",{d:"M7 20v-4",key:"j294jx"}],["path",{d:"M12 20v-8",key:"i3yub9"}],["path",{d:"M17 20V8",key:"1tkaf5"}],["path",{d:"M22 4v16",key:"sih9yq"}]]),Ge=t=>{let{connectionState:n,callState:r,micState:a,sessionId:l,streamId:o,audioLevel:i=0}=t;const[s,u]=(0,e.useState)(0),[c,d]=(0,e.useState)(null);(0,e.useEffect)(()=>{r!==we||c?r!==we&&(d(null),u(0)):d(Date.now())},[r,c]),(0,e.useEffect)(()=>{let e;return c&&r===we&&(e=setInterval(()=>{u(Math.floor((Date.now()-c)/1e3))},1e3)),()=>{e&&clearInterval(e)}},[c,r]);const f=e=>{const t=e%60;return`${Math.floor(e/60).toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`},p=(()=>{switch(n){case me:return{icon:Ke,text:"Disconnected",color:"text-gray-500",bgColor:"bg-gray-100",dotColor:"bg-gray-400"};case ge:return{icon:Ue,text:"Connecting...",color:"text-yellow-600",bgColor:"bg-yellow-100",dotColor:"bg-yellow-500",animate:"animate-spin"};case ye:return{icon:ae,text:"Connected",color:"text-success-600",bgColor:"bg-success-100",dotColor:"bg-success-500"};case ve:return{icon:Ye,text:"Connection Error",color:"text-danger-600",bgColor:"bg-danger-100",dotColor:"bg-danger-500"};default:return{icon:Ke,text:"Unknown",color:"text-gray-500",bgColor:"bg-gray-100",dotColor:"bg-gray-400"}}})(),h=(()=>{switch(r){case be:return{icon:$e,text:"Ready to Call",color:"text-gray-600",bgColor:"bg-gray-100",dotColor:"bg-gray-400"};case xe:return{icon:Ue,text:"Connecting Call...",color:"text-primary-600",bgColor:"bg-primary-100",dotColor:"bg-primary-500",animate:"animate-spin"};case we:return{icon:Oe,text:"In Call",color:"text-success-600",bgColor:"bg-success-100",dotColor:"bg-success-500",duration:f(s)};case ke:return{icon:Ue,text:"Ending Call...",color:"text-yellow-600",bgColor:"bg-yellow-100",dotColor:"bg-yellow-500",animate:"animate-spin"};case Se:return{icon:$e,text:"Call Ended",color:"text-gray-600",bgColor:"bg-gray-100",dotColor:"bg-gray-400"};case Ce:return{icon:Ye,text:"Call Error",color:"text-danger-600",bgColor:"bg-danger-100",dotColor:"bg-danger-500"};default:return{icon:$e,text:"Unknown",color:"text-gray-600",bgColor:"bg-gray-100",dotColor:"bg-gray-400"}}})(),m=(()=>{switch(a){case Fe:return{text:"Microphone Idle",color:"text-gray-500",dotColor:"bg-gray-400"};case De:return{text:"Requesting Access...",color:"text-yellow-600",dotColor:"bg-yellow-500"};case Le:return{text:"Microphone Ready",color:"text-success-600",dotColor:"bg-success-500"};case Ae:return{text:"Access Denied",color:"text-danger-600",dotColor:"bg-danger-500"};case Ie:return{text:"Microphone Error",color:"text-danger-600",dotColor:"bg-danger-500"};default:return{text:"Unknown",color:"text-gray-500",dotColor:"bg-gray-400"}}})();return(0,He.jsxs)("div",{className:"card p-6",children:[(0,He.jsx)("div",{className:"text-center mb-6",children:(0,He.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Status"})}),(0,He.jsxs)("div",{className:"space-y-4",children:[(0,He.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg bg-gray-50",children:[(0,He.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,He.jsx)("div",{className:`p-2 rounded-full ${p.bgColor}`,children:(0,He.jsx)(p.icon,{size:20,className:`${p.color} ${p.animate||""}`})}),(0,He.jsxs)("div",{children:[(0,He.jsx)("p",{className:"font-medium text-gray-900",children:"Connection"}),(0,He.jsx)("p",{className:`text-sm ${p.color}`,children:p.text})]})]}),(0,He.jsx)("div",{className:`w-3 h-3 rounded-full ${p.dotColor} ${n===ye?"animate-pulse":""}`})]}),(0,He.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg bg-gray-50",children:[(0,He.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,He.jsx)("div",{className:`p-2 rounded-full ${h.bgColor}`,children:(0,He.jsx)(h.icon,{size:20,className:`${h.color} ${h.animate||""}`})}),(0,He.jsxs)("div",{children:[(0,He.jsx)("p",{className:"font-medium text-gray-900",children:"Call Status"}),(0,He.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,He.jsx)("p",{className:`text-sm ${h.color}`,children:h.text}),h.duration&&(0,He.jsxs)("span",{className:"text-sm text-gray-500",children:["\u2022 ",h.duration]})]})]})]}),(0,He.jsx)("div",{className:`w-3 h-3 rounded-full ${h.dotColor} ${r===we?"animate-pulse":""}`})]}),(0,He.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg bg-gray-50",children:[(0,He.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,He.jsx)("div",{className:"p-2 rounded-full bg-gray-100",children:(0,He.jsx)(Xe,{size:20,className:"text-gray-600"})}),(0,He.jsxs)("div",{children:[(0,He.jsx)("p",{className:"font-medium text-gray-900",children:"Microphone"}),(0,He.jsx)("p",{className:`text-sm ${m.color}`,children:m.text})]})]}),(0,He.jsxs)("div",{className:"flex items-center space-x-2",children:[a===Le&&i>0&&(0,He.jsx)("div",{className:"flex items-center space-x-1",children:Array.from({length:3},(e,t)=>(0,He.jsx)("div",{className:"w-1 h-3 rounded-full transition-colors duration-150 "+(i>20*(t+1)?"bg-success-500":"bg-gray-300")},t))}),(0,He.jsx)("div",{className:`w-3 h-3 rounded-full ${m.dotColor}`})]})]})]}),(l||o)&&(0,He.jsxs)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:[(0,He.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-2",children:"Session Info"}),(0,He.jsxs)("div",{className:"space-y-1 text-xs text-gray-500",children:[l&&(0,He.jsxs)("p",{children:["Session: ",l.substring(0,20),"..."]}),o&&(0,He.jsxs)("p",{children:["Stream: ",o.substring(0,20),"..."]})]})]}),(0,He.jsx)("div",{className:"mt-6 pt-4 border-t border-gray-200",children:(0,He.jsxs)("div",{className:"flex justify-center space-x-2 text-xs text-gray-500",children:[(0,He.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,He.jsx)("div",{className:"w-2 h-2 bg-success-500 rounded-full"}),(0,He.jsx)("span",{children:"Active"})]}),(0,He.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,He.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,He.jsx)("span",{children:"Connecting"})]}),(0,He.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,He.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,He.jsx)("span",{children:"Inactive"})]}),(0,He.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,He.jsx)("div",{className:"w-2 h-2 bg-danger-500 rounded-full"}),(0,He.jsx)("span",{children:"Error"})]})]})})]})};const Je=function(){const[t,n]=(0,e.useState)("ws://localhost:1802"),[r,a]=(0,e.useState)(!1),[l,o]=(0,e.useState)(!0),i=(0,e.useRef)(!1),{connectionState:s,callState:u,streamId:c,sessionId:d,connect:f,disconnect:p,startCall:h,endCall:m,sendAudioData:g,isConnected:y,isInCall:v,isConnecting:b}=function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"ws://localhost:1802";const[n,r]=(0,e.useState)(me),[a,l]=(0,e.useState)(be),[o,i]=(0,e.useState)(null),[s,u]=(0,e.useState)(null),[c,d]=(0,e.useState)(0),f=(0,e.useRef)(null),p=(0,e.useRef)(null),h=(0,e.useRef)(0),m=(0,e.useRef)(!1),g=(0,e.useCallback)(()=>"session_"+Math.random().toString(36).substr(2,9)+"_"+Date.now(),[]),y=(0,e.useCallback)(async()=>{try{ue.initialize(),console.log("\u2705 Simple audio processor initialized")}catch(e){throw console.error("\u274c Failed to initialize audio processor:",e),ee.error("Failed to initialize audio system"),e}},[]),v=(0,e.useCallback)(async e=>{try{if(console.log("\ud83c\udfb5 playAudioFromBase64 called with data length:",e?e.length:"null"),!e||0===e.length)return void console.warn("\u26a0\ufe0f Empty or invalid base64 data received");ue.isInitialized||(console.log("\ud83d\udd04 Initializing audio processor..."),await y()),await ue.processAndPlayAudio(e),console.log("\u2705 Audio processed and playing")}catch(t){console.error("\u274c Error playing audio:",t),ee.error("Audio playback failed: "+t.message)}},[y]),b=(0,e.useCallback)(async e=>{try{const n=JSON.parse(e.data);if(n.event)switch(n.event){case Ee:console.log("WebSocket protocol connected"),r(ye);break;case Ne:console.log("Call started:",n),i(n.streamSid),l(we),he.startRecording(),console.log("\ud83c\udf99\ufe0f Started recording audio for debugging"),ee.success("Call connected!");break;case _e:if(n.media&&n.media.payload){console.log("\ud83d\udce8 MEDIA event received - payload length:",n.media.payload.length,"chunk:",n.media.chunk);try{await v(n.media.payload),console.log("\u2705 Audio playback completed successfully")}catch(t){console.error("\u274c Error playing MEDIA audio:",t),ee.error("Audio playback error")}}else console.warn("\u26a0\ufe0f MEDIA event received but no payload found:",n);break;case Me:console.log("Call stopped:",n),l(Se),i(null),he.getStatus().isRecording&&(console.log("\ud83d\uded1 Stopping audio recording..."),he.stopRecording()),ee.success("Call ended");break;case Pe:console.log("Mark event received:",n);break;case ze:console.log("\ud83e\uddf9 Clear event received:",n),ue.stop(),console.log("\u2705 Audio playback stopped");break;default:console.log("Unknown protocol event:",n.event)}else if(n.type)switch(n.type){case"user_registered":console.log("User registered successfully - setting connection state to CONNECTED"),r(ye),console.log("Connection state updated to CONNECTED"),ee.success("Connected to server");break;case"error":console.error("Server error:",n.message),ee.error(n.message||"Server error occurred"),r(ve),l(Ce);break;default:console.log("Unknown user event:",n.type)}}catch(t){console.error("Error handling WebSocket message:",t)}},[v]),x=(0,e.useCallback)(async()=>{var e,n;if((null===(e=f.current)||void 0===e?void 0:e.readyState)===WebSocket.OPEN||(null===(n=f.current)||void 0===n?void 0:n.readyState)===WebSocket.CONNECTING||m.current)console.log("WebSocket already connected or connecting, skipping connection attempt");else try{m.current=!0,r(ge),console.log(`Attempting to connect to ${t}`);const e=new WebSocket(t);f.current=e,e.onopen=async()=>{console.log("WebSocket connected"),h.current=0,m.current=!1;const t=g();u(t);const n={type:je,sessionId:t,userData:{name:"Web User",mobile:"+1234567890",userId:"web_user_"+Date.now(),sessionType:"call",target:"assistant"}};e.send(JSON.stringify(n))},e.onmessage=b,e.onclose=e=>{console.log("WebSocket closed:",e.code,e.reason),m.current=!1,r(me),l(be),1e3!==e.code&&h.current<5?(h.current++,console.log(`Attempting to reconnect (${h.current}/5)...`),p.current=setTimeout(()=>{x()},3e3)):h.current>=5&&(console.log("Max reconnection attempts reached"),ee.error("Unable to connect to server. Please check if the server is running."))},e.onerror=e=>{var t;if(console.error("WebSocket error:",e),m.current=!1,r(ve),"error"===e.type&&(null===(t=f.current)||void 0===t?void 0:t.readyState)===WebSocket.CLOSED){const e="1802";ee.error(`Failed to connect to server. Please check if the WebSocket server is running on port ${e}.`)}else ee.error("Connection error occurred")}}catch(a){console.error("Failed to connect:",a),m.current=!1,r(ve),ee.error("Failed to connect to server")}},[t,b,g]),w=(0,e.useCallback)(()=>{p.current&&(clearTimeout(p.current),p.current=null),m.current=!1,ue.stop(),he.getStatus().isRecording&&(console.log("\ud83e\uddf9 Cleaning up audio recorder on disconnect"),he.clearRecording()),f.current&&(f.current.close(1e3,"User disconnected"),f.current=null),r(me),l(be),i(null),u(null)},[]),k=(0,e.useCallback)(async()=>{if(f.current&&f.current.readyState===WebSocket.OPEN&&s)try{l(xe);try{await y(),console.log("\u2705 Audio processor initialized for call");const e=ue.getStatus();console.log("\ud83c\udfb5 Audio system status:",e)}catch(e){return console.error("\u274c Failed to initialize audio processor:",e),ee.error("Failed to initialize audio. Please check your speakers and try again."),void l(be)}const t={type:Re,sessionId:s};f.current.send(JSON.stringify(t))}catch(t){console.error("Error starting call:",t),l(be),ee.error("Failed to start call")}else ee.error("Not connected to server")},[s,y]),S=(0,e.useCallback)(()=>{if(!f.current||f.current.readyState!==WebSocket.OPEN||!s)return;l(ke),he.getStatus().isRecording&&(console.log("\ud83d\uded1 User ended call - stopping audio recording..."),he.stopRecording());const e={type:Te,sessionId:s,reason:"user_ended"};f.current.send(JSON.stringify(e))},[s]),C=(0,e.useCallback)(e=>{if(f.current&&f.current.readyState===WebSocket.OPEN&&o)try{const t=btoa(String.fromCharCode(...new Uint8Array(e))),n={event:_e,streamSid:o,sequenceNumber:(c+1).toString(),media:{chunk:Math.floor(Date.now()/20).toString(),timestamp:Date.now().toString(),payload:t}};f.current.send(JSON.stringify(n)),d(e=>e+1)}catch(t){console.error("Error sending audio data:",t)}},[o,c]);return(0,e.useEffect)(()=>()=>{he.getStatus().isRecording&&he.clearRecording(),ue.stop(),w()},[w]),{connectionState:n,callState:a,streamId:o,sessionId:s,connect:x,disconnect:w,startCall:k,endCall:S,sendAudioData:C,isConnected:n===ye,isInCall:a===we,isConnecting:a===xe}}(t),x=(0,e.useCallback)(e=>{v&&e&&g(e)},[v,g]),{micState:w,isMuted:k,audioLevel:S,isGranted:C,isDenied:E,isRequesting:N,hasError:_,requestMicrophone:M,stopMicrophone:P,toggleMute:z}=(t=>{let{onAudioData:n,enabled:r=!1}=t;const[a,l]=(0,e.useState)(Fe),[o,i]=(0,e.useState)(!1),[s,u]=(0,e.useState)(0),c=(0,e.useRef)(null),d=(0,e.useRef)(null),f=(0,e.useRef)(null),p=(0,e.useRef)(null),h=(0,e.useRef)(null),m=(0,e.useCallback)((e,t)=>{n&&!o&&pe.processInputAudio(e,t).forEach(e=>{n(e.buffer)})},[n,o]),g=(0,e.useCallback)(()=>{if(!f.current)return;const e=new Uint8Array(f.current.frequencyBinCount);f.current.getByteFrequencyData(e);let t=0;for(let a=0;a<e.length;a++)t+=e[a]*e[a];const n=Math.sqrt(t/e.length),r=Math.min(100,n/255*100);u(r),a!==Le||o||(h.current=requestAnimationFrame(g))},[a,o]),y=(0,e.useCallback)(async e=>{f.current=d.current.createAnalyser(),f.current.fftSize=256,f.current.smoothingTimeConstant=.8;const t=d.current.createMediaStreamSource(e);t.connect(f.current);const n=d.current.createScriptProcessor(4096,1,1);n.onaudioprocess=e=>{m(e.inputBuffer.getChannelData(0),d.current.sampleRate)},t.connect(n),n.connect(d.current.destination),g()},[m,g]),v=(0,e.useCallback)(async e=>{try{d.current=new(window.AudioContext||window.webkitAudioContext)({sampleRate:44100}),"suspended"===d.current.state&&await d.current.resume();try{await d.current.audioWorklet.addModule("/audio-processor-worklet.js"),f.current=d.current.createAnalyser(),f.current.fftSize=256,f.current.smoothingTimeConstant=.8;const t=d.current.createMediaStreamSource(e);t.connect(f.current),p.current=new AudioWorkletNode(d.current,"audio-processor-worklet"),p.current.port.onmessage=e=>{"audioData"===e.data.type&&m(e.data.buffer,e.data.sampleRate)},t.connect(p.current),p.current.connect(d.current.destination),g()}catch(t){console.warn("AudioWorklet not supported, falling back to ScriptProcessorNode:",t),await y(e)}}catch(n){throw console.error("Error initializing audio processing:",n),n}},[m,g,y]),b=(0,e.useCallback)(async()=>{if(a!==De&&a!==Le)try{l(De);const e={audio:{echoCancellation:!0,noiseSuppression:!0,autoGainControl:!0,sampleRate:44100,channelCount:1},video:!1},t=await navigator.mediaDevices.getUserMedia(e);c.current=t,await v(t),l(Le),ee.success("Microphone access granted")}catch(e){console.error("Error requesting microphone:",e),"NotAllowedError"===e.name?(l(Ae),ee.error("Microphone access denied. Please allow microphone access and try again.")):"NotFoundError"===e.name?(l(Ie),ee.error("No microphone found. Please connect a microphone and try again.")):(l(Ie),ee.error("Failed to access microphone: "+e.message))}},[a,v]),x=(0,e.useCallback)(()=>{h.current&&(cancelAnimationFrame(h.current),h.current=null),c.current&&(c.current.getTracks().forEach(e=>e.stop()),c.current=null),d.current&&(d.current.close(),d.current=null),f.current=null,p.current=null,pe.clearBuffer(),l(Fe),u(0),i(!1)},[]),w=(0,e.useCallback)(()=>{a===Le&&i(e=>{const t=!e;return c.current&&c.current.getAudioTracks().forEach(e=>{e.enabled=!t}),t?(u(0),ee.success("Microphone muted")):(ee.success("Microphone unmuted"),g()),t})},[a,g]);return(0,e.useEffect)(()=>{r&&a===Fe?b():r||a!==Le||x()},[r,a,b,x]),(0,e.useEffect)(()=>()=>{x()},[x]),{micState:a,isMuted:o,audioLevel:s,isGranted:a===Le,isDenied:a===Ae,isRequesting:a===De,hasError:a===Ie,requestMicrophone:b,stopMicrophone:x,toggleMute:w}})({onAudioData:x,enabled:v});(0,e.useEffect)(()=>{l&&!i.current&&(i.current=!0,f())},[l]);const j=()=>(0,He.jsxs)("div",{className:"card p-4 mb-6",children:[(0,He.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,He.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Settings"}),(0,He.jsx)("button",{onClick:()=>a(!1),className:"text-gray-500 hover:text-gray-700",children:"\xd7"})]}),(0,He.jsxs)("div",{className:"space-y-4",children:[(0,He.jsxs)("div",{children:[(0,He.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"WebSocket URL"}),(0,He.jsx)("input",{type:"text",value:t,onChange:e=>n(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",placeholder:"ws://localhost:1802"})]}),(0,He.jsxs)("div",{className:"flex items-center",children:[(0,He.jsx)("input",{type:"checkbox",id:"autoConnect",checked:l,onChange:e=>o(e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),(0,He.jsx)("label",{htmlFor:"autoConnect",className:"ml-2 block text-sm text-gray-900",children:"Auto-connect on page load"})]})]})]});return(0,He.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,He.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,He.jsx)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,He.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,He.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,He.jsx)("div",{className:"p-2 bg-primary-100 rounded-lg",children:(0,He.jsx)(re,{className:"h-6 w-6 text-primary-600"})}),(0,He.jsxs)("div",{children:[(0,He.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"One call"}),(0,He.jsx)("p",{className:"text-sm text-gray-500",children:"Real-time Voice Calling"})]})]}),(0,He.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,He.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,He.jsx)(ae,{size:16,className:y?"text-success-600":"text-gray-400"}),(0,He.jsx)("span",{className:"text-sm "+(y?"text-success-600":"text-gray-500"),children:y?"Connected":"Disconnected"})]}),(0,He.jsx)("button",{onClick:()=>a(!r),className:"p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",children:(0,He.jsx)(le,{size:20})})]})]})})}),(0,He.jsxs)("main",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[r&&(0,He.jsx)(j,{}),(0,He.jsx)("div",{className:"card p-4 mb-6",children:(0,He.jsxs)("div",{className:"flex items-center justify-between",children:[(0,He.jsxs)("div",{children:[(0,He.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Server Connection"}),(0,He.jsx)("p",{className:"text-sm text-gray-500",children:y?`Connected to ${t}`:`Disconnected from ${t}`})]}),(0,He.jsx)("button",{onClick:()=>{y?p():f()},disabled:b,className:`px-4 py-2 rounded-lg font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${y?"bg-danger-600 hover:bg-danger-700 text-white focus:ring-danger-500":"bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500"} ${b?"opacity-50 cursor-not-allowed":""}`,children:b?"Connecting...":y?"Disconnect":"Connect"})]})}),(0,He.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,He.jsx)("div",{className:"order-1 lg:order-1",children:(0,He.jsx)(Qe,{callState:u,isConnected:y,isMuted:k,onStartCall:async()=>{C||await M(),h()},onEndCall:()=>{m(),P()},onToggleMute:()=>{z()},audioLevel:S})}),(0,He.jsx)("div",{className:"order-2 lg:order-2",children:(0,He.jsx)(Ge,{connectionState:s,callState:u,micState:w,sessionId:d,streamId:c,audioLevel:S})})]}),E&&(0,He.jsx)("div",{className:"mt-6 card p-4 bg-yellow-50 border-yellow-200",children:(0,He.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,He.jsx)("div",{className:"flex-shrink-0",children:(0,He.jsx)("div",{className:"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center",children:(0,He.jsx)(le,{size:16,className:"text-yellow-600"})})}),(0,He.jsxs)("div",{children:[(0,He.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"Microphone Access Required"}),(0,He.jsx)("p",{className:"text-sm text-yellow-700 mt-1",children:"To make voice calls, please allow microphone access in your browser settings and refresh the page."}),(0,He.jsx)("button",{onClick:M,className:"mt-2 text-sm text-yellow-800 underline hover:text-yellow-900",children:"Try Again"})]})]})}),_&&(0,He.jsx)("div",{className:"mt-6 card p-4 bg-danger-50 border-danger-200",children:(0,He.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,He.jsx)("div",{className:"flex-shrink-0",children:(0,He.jsx)("div",{className:"w-8 h-8 bg-danger-100 rounded-full flex items-center justify-center",children:(0,He.jsx)(le,{size:16,className:"text-danger-600"})})}),(0,He.jsxs)("div",{children:[(0,He.jsx)("h3",{className:"text-sm font-medium text-danger-800",children:"Microphone Error"}),(0,He.jsx)("p",{className:"text-sm text-danger-700 mt-1",children:"There was an error accessing your microphone. Please check your device settings and try again."})]})]})}),(0,He.jsxs)("div",{className:"mt-8 card p-6",children:[(0,He.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"How to Use"}),(0,He.jsxs)("div",{className:"space-y-3 text-sm text-gray-600",children:[(0,He.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,He.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium",children:"1"}),(0,He.jsx)("p",{children:"Ensure you're connected to the WebSocket server (green status indicator)"})]}),(0,He.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,He.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium",children:"2"}),(0,He.jsx)("p",{children:"Allow microphone access when prompted by your browser"})]}),(0,He.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,He.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium",children:"3"}),(0,He.jsx)("p",{children:"Click the green phone button to start a voice call"})]}),(0,He.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,He.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium",children:"4"}),(0,He.jsx)("p",{children:"Use the microphone and speaker controls during the call"})]}),(0,He.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,He.jsx)("span",{className:"flex-shrink-0 w-6 h-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium",children:"5"}),(0,He.jsx)("p",{children:"Click the red phone button to end the call"})]})]})]})]}),(0,He.jsx)(Z,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#10b981",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})};t.createRoot(document.getElementById("root")).render((0,He.jsx)(e.StrictMode,{children:(0,He.jsx)(Je,{})}))})()})();
//# sourceMappingURL=main.3be4c063.js.map