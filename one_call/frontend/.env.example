# Port Configuration
REACT_APP_API_PORT=1801
REACT_APP_WEBSOCKET_PORT=1802
REACT_APP_FRONTEND_PORT=1800
REACT_APP_BACKEND_PROXY_PORT=1803

# WebSocket Server Configuration (will be dynamically constructed from WEBSOCKET_PORT)
REACT_APP_WS_URL=ws://localhost:1802

# Auto-connect to server on page load
REACT_APP_AUTO_CONNECT=true

# Audio Configuration
REACT_APP_SAMPLE_RATE=8000
REACT_APP_CHUNK_DURATION=20

# Development Settings
REACT_APP_DEBUG_AUDIO=false
REACT_APP_LOG_LEVEL=info
