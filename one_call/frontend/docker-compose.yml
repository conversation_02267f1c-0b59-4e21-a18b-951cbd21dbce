version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add the backend service if running together
  # backend:
  #   image: ai-voice-mate-backend:latest
  #   ports:
  #     - "5010:5010"
  #   environment:
  #     - NODE_ENV=production
  #   restart: unless-stopped
