# One call Frontend

A modern React.js frontend application for real-time voice calling that integrates with the One call WebSocket backend.

## Features

- 🎙️ **Real-time Voice Calling** - High-quality voice communication using WebSocket protocol
- 🔊 **Audio Controls** - Mute/unmute microphone and speaker controls
- 📊 **Visual Feedback** - Real-time audio level indicators and call status
- 📱 **Responsive Design** - Mobile-friendly interface built with TailwindCSS
- 🔄 **Auto-reconnection** - Automatic WebSocket reconnection with exponential backoff
- 🎵 **Advanced Audio Processing** - µ-law encoding/decoding for optimal audio quality
- 🚨 **Error Handling** - Comprehensive error handling with user-friendly notifications

## Technology Stack

- **React 18** - Modern React with hooks and functional components
- **TailwindCSS** - Utility-first CSS framework for responsive design
- **Lucide React** - Beautiful SVG icons
- **React Hot Toast** - Elegant toast notifications
- **Web Audio API** - Advanced audio processing and playback
- **WebSocket** - Real-time bidirectional communication

## Prerequisites

- Node.js 16+ and npm/yarn
- Modern web browser with WebSocket and Web Audio API support
- Microphone access for voice calling
- One call backend server running on port 5010

## Quick Start

### 1. Installation

```bash
# Clone the repository (if not already done)
cd frontend

# Install dependencies
npm install

# Or using yarn
yarn install
```

### 2. Development Server

```bash
# Start the development server
npm start

# Or using yarn
yarn start
```

The application will open at `http://localhost:3000`

### 3. Production Build

```bash
# Create production build
npm run build

# Or using yarn
yarn build
```

## Configuration

### WebSocket Server URL

The default WebSocket server URL is `ws://localhost:5010`. You can change this in the application settings or by modifying the default value in `src/App.js`:

```javascript
const [wsUrl, setWsUrl] = useState('ws://your-server.com:5010');
```

### Audio Settings

Audio processing settings can be modified in `src/utils/audioProcessor.js`:

```javascript
// Protocol specifications
SAMPLE_RATE = 8000;     // 8kHz sample rate
BIT_DEPTH = 8;          // 8-bit depth
ENCODING = "audio/x-mulaw"; // µ-law encoding
CHANNELS = 1;           // Mono audio
```

## Project Structure

```
frontend/
├── public/
│   ├── index.html          # Main HTML template
│   └── manifest.json       # PWA manifest
├── src/
│   ├── components/         # React components
│   │   ├── CallControls.jsx    # Call control buttons
│   │   └── CallStatus.jsx      # Status indicators
│   ├── hooks/              # Custom React hooks
│   │   ├── useVoiceSocket.js   # WebSocket management
│   │   └── useMicrophone.js    # Microphone handling
│   ├── utils/              # Utility functions
│   │   └── audioProcessor.js   # Audio processing utilities
│   ├── App.js              # Main application component
│   ├── index.js            # Application entry point
│   └── index.css           # Global styles and Tailwind
├── package.json            # Dependencies and scripts
├── tailwind.config.js      # TailwindCSS configuration
└── postcss.config.js       # PostCSS configuration
```

## Usage Guide

### 1. Connect to Server

1. Open the application in your browser
2. The app will automatically attempt to connect to the WebSocket server
3. Check the connection status indicator in the header
4. If connection fails, verify the server URL in settings

### 2. Start a Voice Call

1. Ensure you're connected to the server (green status indicator)
2. Click the green phone button to start a call
3. Allow microphone access when prompted by your browser
4. Wait for the call to connect (status will show "In Call")

### 3. During a Call

- **Mute/Unmute**: Click the microphone button to toggle mute
- **Speaker Control**: Click the speaker button to toggle audio output
- **Audio Levels**: Watch the real-time audio level indicators
- **End Call**: Click the red phone button to end the call

### 4. Troubleshooting

- **No Microphone Access**: Check browser permissions and allow microphone access
- **Connection Issues**: Verify the WebSocket server is running and accessible
- **Audio Problems**: Ensure your browser supports Web Audio API
- **Poor Audio Quality**: Check your internet connection and microphone quality

## Browser Compatibility

- **Chrome 66+** - Full support
- **Firefox 60+** - Full support
- **Safari 11.1+** - Full support
- **Edge 79+** - Full support

**Note**: HTTPS is required for microphone access in production environments.

## Development

### Available Scripts

```bash
npm start          # Start development server
npm run build      # Create production build
npm test           # Run tests
npm run eject      # Eject from Create React App (irreversible)
```

### Environment Variables

Create a `.env` file in the frontend directory:

```env
REACT_APP_WS_URL=ws://localhost:5010
REACT_APP_AUTO_CONNECT=true
```

### Custom Hooks

#### useVoiceSocket

Manages WebSocket connection and voice protocol:

```javascript
const {
  connectionState,
  callState,
  connect,
  disconnect,
  startCall,
  endCall,
  sendAudioData
} = useVoiceSocket('ws://localhost:5010');
```

#### useMicrophone

Handles microphone access and audio processing:

```javascript
const {
  micState,
  isMuted,
  audioLevel,
  requestMicrophone,
  toggleMute
} = useMicrophone({ onAudioData: handleAudioData });
```

## WebSocket Protocol

The frontend implements the following WebSocket message types:

### Outgoing Messages

```javascript
// User Registration
{
  "type": "user_register",
  "sessionId": "session_123",
  "userData": { ... }
}

// Start Call
{
  "type": "call_start",
  "sessionId": "session_123"
}

// Audio Data
{
  "event": "media",
  "streamSid": "MZ123...",
  "media": {
    "payload": "base64_audio_data"
  }
}
```

### Incoming Messages

```javascript
// Connected Event
{
  "event": "connected"
}

// Call Started
{
  "event": "start",
  "streamSid": "MZ123..."
}

// Audio Data
{
  "event": "media",
  "media": {
    "payload": "base64_audio_data"
  }
}
```

## Deployment

### Production Deployment

1. Build the application:
   ```bash
   npm run build
   ```

2. Serve the `build` folder using a web server:
   ```bash
   # Using serve
   npx serve -s build -l 3000
   
   # Using nginx, apache, or any static file server
   ```

3. Configure HTTPS for production (required for microphone access)

### Docker Deployment

```dockerfile
FROM node:16-alpine as build
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=build /app/build /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For issues and questions:
- Check the troubleshooting section above
- Review browser console for error messages
- Ensure WebSocket server is running and accessible
- Verify microphone permissions are granted
