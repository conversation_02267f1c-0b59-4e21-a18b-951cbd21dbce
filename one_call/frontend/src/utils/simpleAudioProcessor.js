/**
 * Simplified Audio Processor using pcm-player
 * Focuses only on µ-law decoding - pcm-player handles everything else
 */

import PCMPlayer from 'pcm-player';

// Standard µ-law decoding table (256 entries)
const muLawDecodeTable = new Int16Array(256);

// Initialize µ-law decoding table using Columbia University reference implementation
// Exact implementation from http://www.cs.columbia.edu/~hgs/research/projects/NetworkAudioLibrary/
function initializeMuLawDecodeTable() {
  // G.711 constants
  const QUANT_MASK = 0x0f; // quantization field mask
  const BIAS = 0x84;       // bias for linear code
  const SEG_MASK = 0x70;   // segment field mask
  const SEG_SHIFT = 4;     // left shift for segment number
  const SIGN_BIT = 0x80;   // sign bit
  
  for (let i = 0; i < 256; i++) {
    let u_val = i;
    
    // Complement to obtain normal u-law value
    u_val = (~u_val) & 0xFF;
    
    // Extract and bias the quantization bits. Then
    // shift up by the segment number and subtract out the bias.
    let t = ((u_val & QUANT_MASK) << 3) + BIAS;
    t = t << ((u_val & SEG_MASK) >> SEG_SHIFT);
    
    const sample = (u_val & SIGN_BIT) ? (BIAS - t) : (t - BIAS);
    
    // Store as 16-bit signed integer
    muLawDecodeTable[i] = sample;
  }
  
  console.log('✅ µ-law decode table initialized with', muLawDecodeTable.length, 'entries');
  console.log('📊 Key values: 0x00→', muLawDecodeTable[0x00], '0x80→', muLawDecodeTable[0x80], '0xFF→', muLawDecodeTable[0xFF], '0x7F→', muLawDecodeTable[0x7F]);
}

// Initialize the decode table
initializeMuLawDecodeTable();

/**
 * Simple Audio Processor with pcm-player
 */
export class SimpleAudioProcessor {
  constructor() {
    this.pcmPlayer = null;
    this.isInitialized = false;
    this.sampleRate = 8000; // Protocol sample rate
    this.channels = 1; // Mono
    this.bitDepth = 16; // 16-bit PCM
  }

  /**
   * Initialize pcm-player (call this after user interaction)
   */
  initialize() {
    if (this.isInitialized) return;

    try {
      this.pcmPlayer = new PCMPlayer({
        inputCodec: 'Int16', // We'll provide 16-bit integers
        channels: this.channels,
        sampleRate: this.sampleRate,
        flushTime: 50, // Reduced to 50ms for better responsiveness
        volume: 1.0, // Maximum safe volume (some libraries allow >1.0 but may cause distortion)
        onstatechange: (state) => {
          console.log('🔊 PCMPlayer state:', state);
        }
      });

      this.isInitialized = true;
      console.log('✅ PCMPlayer initialized:', {
        sampleRate: this.sampleRate,
        channels: this.channels,
        bitDepth: this.bitDepth
      });
      
      // Log volume configuration for debugging
      this.logVolumeInfo();
    } catch (error) {
      console.error('❌ Failed to initialize PCMPlayer:', error);
      throw error;
    }
  }

  /**
   * Convert µ-law bytes to 16-bit PCM integers
   * @param {Uint8Array} muLawData - µ-law encoded data
   * @returns {Int16Array} 16-bit PCM data
   */
  muLawToPcm16(muLawData) {
    const pcmData = new Int16Array(muLawData.length);
    
    for (let i = 0; i < muLawData.length; i++) {
      pcmData[i] = muLawDecodeTable[muLawData[i]];
    }
    
    return pcmData;
  }

  /**
   * Process and play received audio
   * @param {string} base64Data - Base64 encoded µ-law data
   */
  async processAndPlayAudio(base64Data) {
    try {
      console.log('🎵 Processing audio - base64 length:', base64Data.length);

      // Ensure pcm-player is initialized
      if (!this.isInitialized) {
        this.initialize();
      }

      // Decode base64 to µ-law data
      const binaryString = atob(base64Data);
      const muLawData = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        muLawData[i] = binaryString.charCodeAt(i);
      }

      console.log('📦 Decoded µ-law data:', {
        length: muLawData.length,
        firstBytes: Array.from(muLawData.slice(0, 8)),
        duration: (muLawData.length / this.sampleRate * 1000).toFixed(1) + 'ms'
      });

      // Convert µ-law to 16-bit PCM
      const pcmData = this.muLawToPcm16(muLawData);
      
      // Validate PCM data (using original, unfiltered audio)
      const maxValue = Math.max(...pcmData);
      const minValue = Math.min(...pcmData);
      const rms = Math.sqrt(pcmData.reduce((sum, sample) => sum + sample * sample, 0) / pcmData.length);
      
      console.log('🔊 PCM data converted:', {
        length: pcmData.length,
        range: `${minValue} to ${maxValue}`,
        rms: rms.toFixed(1),
        sample: Array.from(pcmData.slice(0, 8))
      });

      // Check for valid audio content
      if (rms < 10) {
        console.warn('⚠️ Audio appears to be very quiet (RMS < 10)');
      }

      // Feed original PCM data to pcm-player for immediate playback
      this.pcmPlayer.feed(pcmData);
      
      console.log('✅ Audio fed to PCMPlayer');

    } catch (error) {
      console.error('❌ Error processing audio:', error);
      throw error;
    }
  }

  /**
   * Enhance audio quality with normalization and noise reduction
   * @param {Int16Array} pcmData - Raw PCM data
   * @returns {Int16Array} Enhanced PCM data
   */
  enhanceAudio(pcmData) {
    const enhanced = new Int16Array(pcmData.length);
    
    // Calculate RMS for normalization
    let sumSquares = 0;
    for (let i = 0; i < pcmData.length; i++) {
      sumSquares += pcmData[i] * pcmData[i];
    }
    const rms = Math.sqrt(sumSquares / pcmData.length);
    
    // Target RMS for good audibility (about 25% of max range)
    const targetRms = 8192; // 16384 * 0.5
    const gain = rms > 0 ? Math.min(targetRms / rms, 4.0) : 1.0; // Limit max gain to 4x
    
    // Apply gain and light compression
    for (let i = 0; i < pcmData.length; i++) {
      let sample = pcmData[i] * gain;
      
      // Soft clipping to prevent harsh distortion
      if (sample > 16383) {
        sample = 16383 + (sample - 16383) * 0.3;
      } else if (sample < -16384) {
        sample = -16384 + (sample + 16384) * 0.3;
      }
      
      // Ensure we stay within 16-bit range
      enhanced[i] = Math.max(-32768, Math.min(32767, Math.round(sample)));
    }
    
    console.log('🎚️ Audio enhanced:', {
      originalRms: rms.toFixed(1),
      targetRms: targetRms,
      gainApplied: gain.toFixed(2)
    });
    
    return enhanced;
  }

  /**
   * Test audio playback with a simple tone
   */
  testAudio() {
    if (!this.isInitialized) {
      this.initialize();
    }

    console.log('🎵 Testing audio with 440Hz tone...');

    // Generate a 0.5 second 440Hz sine wave as 16-bit PCM
    const duration = 0.5; // seconds
    const frequency = 440; // Hz
    const samples = Math.floor(this.sampleRate * duration);
    const testPcm = new Int16Array(samples);

    for (let i = 0; i < samples; i++) {
      const sample = Math.sin(2 * Math.PI * frequency * i / this.sampleRate) * 16000;
      testPcm[i] = Math.round(sample);
    }

    this.pcmPlayer.feed(testPcm);
    console.log('✅ Test tone should be playing');
  }

  /**
   * Stop audio playback and cleanup
   */
  stop() {
    if (this.pcmPlayer) {
      this.pcmPlayer.destroy();
      this.pcmPlayer = null;
      this.isInitialized = false;
      console.log('🛑 PCMPlayer stopped and destroyed');
    }
  }

  /**
   * Get playback status including volume information
   */
  getStatus() {
    const status = {
      isInitialized: this.isInitialized,
      sampleRate: this.sampleRate,
      channels: this.channels,
      bitDepth: this.bitDepth
    };
    
    if (this.pcmPlayer) {
      // Note: PCMPlayer may not expose volume getter, but we track our setting
      status.volume = 1.0; // Our configured volume
      status.playerState = this.pcmPlayer.state || 'unknown';
    }
    
    return status;
  }

  /**
   * Log volume configuration for debugging
   */
  logVolumeInfo() {
    console.log('🔊 Volume Configuration:', {
      pcmPlayerVolume: 1.0,
      sampleRate: this.sampleRate,
      bitDepth: this.bitDepth,
      isInitialized: this.isInitialized,
      recommendation: 'If volume is low, check browser/system volume settings'
    });
  }
}

// Export singleton instance
export const simpleAudioProcessor = new SimpleAudioProcessor();

// Expose for debugging
if (typeof window !== 'undefined') {
  window.simpleAudioProcessor = simpleAudioProcessor;
}