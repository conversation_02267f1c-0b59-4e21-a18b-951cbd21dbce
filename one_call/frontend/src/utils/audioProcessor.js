/**
 * Audio Processing Utilities for WebSocket Voice Protocol
 * Handles µ-law encoding/decoding, resampling, and audio format conversions
 * 
 * KEY FIXES IMPLEMENTED:
 * - Fixed µ-law decoding algorithm with proper normalization to -1.0 to 1.0 range
 * - Improved resampling from 8kHz protocol audio to AudioContext sample rate
 * - Enhanced AudioQueue for seamless sequential buffer playback without gaps
 * - Added comprehensive debugging and audio content validation
 * - Fixed AudioBuffer creation with proper duration and sample rate handling
 * - Removed gain nodes as requested, using direct connection to destination
 */

// µ-law encoding/decoding constants
const MULAW_BIAS = 0x84;
const MULAW_CLIP = 32635;
const MULAW_MAX = 0x1FFF;

// Pre-computed µ-law decoding table for better performance
const muLawDecodeTable = new Array(256);

// Initialize µ-law decoding table using standard algorithm
function initializeMuLawTables() {
  // Standard µ-law decoding table generation
  for (let i = 0; i < 256; i++) {
    const mulaw = ~i & 0xFF; // Invert bits
    const sign = mulaw & 0x80;
    const exponent = (mulaw & 0x70) >> 4;
    const mantissa = mulaw & 0x0F;

    let sample;
    if (exponent === 0) {
      // Linear segment
      sample = (mantissa << 4) + MULAW_BIAS;
    } else {
      // Logarithmic segments
      sample = (((mantissa | 0x10) << (exponent + 3)) + MULAW_BIAS);
    }

    // Remove bias and apply sign
    sample -= MULAW_BIAS;
    if (sign) {
      sample = -sample;
    }

    // Store as normalized float (-1.0 to 1.0)
    muLawDecodeTable[i] = sample / 32768.0;
  }
  
  console.log('µ-law decode table initialized with sample values:', {
    silence: muLawDecodeTable[0xFF], // Should be close to 0
    positive: muLawDecodeTable[0x87], // Should be positive
    negative: muLawDecodeTable[0x07]  // Should be negative
  });
}

// Initialize tables on module load
initializeMuLawTables();

/**
 * Audio Processor Class
 */
export class AudioProcessor {
  constructor() {
    this.sampleRate = 8000; // Protocol requirement
    this.chunkDuration = 20; // 20ms chunks
    this.chunkSize = (this.sampleRate * this.chunkDuration) / 1000; // 160 samples
    this.audioBuffer = [];
  }

  /**
   * Convert PCM Float32Array to µ-law bytes
   * @param {Float32Array} pcmData - Input PCM data (-1.0 to 1.0)
   * @returns {Uint8Array} µ-law encoded data
   */
  pcmToMuLaw(pcmData) {
    const muLawData = new Uint8Array(pcmData.length);
    
    for (let i = 0; i < pcmData.length; i++) {
      // Clamp and scale to 16-bit range
      let sample = Math.max(-1, Math.min(1, pcmData[i]));
      sample = Math.round(sample * 32767);
      
      // Apply µ-law encoding
      const sign = sample < 0 ? 0x80 : 0x00;
      sample = Math.abs(sample);
      
      if (sample > MULAW_CLIP) sample = MULAW_CLIP;
      sample += MULAW_BIAS;
      
      let exponent = 7;
      for (let exp = 0; exp < 8; exp++) {
        if (sample <= (0x1F << (exp + 3))) {
          exponent = exp;
          break;
        }
      }
      
      const mantissa = (sample >> (exponent + 3)) & 0x0F;
      muLawData[i] = ~(sign | (exponent << 4) | mantissa);
    }
    
    return muLawData;
  }

  /**
   * Convert µ-law bytes to PCM Float32Array
   * @param {Uint8Array} muLawData - Input µ-law data
   * @returns {Float32Array} PCM data (-1.0 to 1.0)
   */
  muLawToPcm(muLawData) {
    const pcmData = new Float32Array(muLawData.length);

    for (let i = 0; i < muLawData.length; i++) {
      // Use pre-computed normalized values from decode table
      pcmData[i] = muLawDecodeTable[muLawData[i]];
    }

    return pcmData;
  }

  /**
   * Resample audio data with improved quality
   * @param {Float32Array} inputData - Input audio data
   * @param {number} inputSampleRate - Input sample rate
   * @param {number} outputSampleRate - Output sample rate
   * @returns {Float32Array} Resampled audio data
   */
  resample(inputData, inputSampleRate, outputSampleRate) {
    if (inputSampleRate === outputSampleRate) {
      return inputData;
    }

    const ratio = inputSampleRate / outputSampleRate;
    const outputLength = Math.round(inputData.length / ratio);
    const outputData = new Float32Array(outputLength);

    for (let i = 0; i < outputLength; i++) {
      const sourceIndex = i * ratio;
      const index = Math.floor(sourceIndex);
      const fraction = sourceIndex - index;

      if (index + 1 < inputData.length) {
        // Linear interpolation for smooth transitions
        outputData[i] = inputData[index] * (1 - fraction) + inputData[index + 1] * fraction;
      } else if (index < inputData.length) {
        outputData[i] = inputData[index];
      } else {
        outputData[i] = 0; // Pad with silence if needed
      }
    }

    console.log(`Resampled audio: ${inputData.length} samples @ ${inputSampleRate}Hz → ${outputData.length} samples @ ${outputSampleRate}Hz`);
    return outputData;
  }

  /**
   * Process audio input for transmission
   * @param {Float32Array} inputData - Raw audio input
   * @param {number} inputSampleRate - Input sample rate
   * @returns {Array<Uint8Array>} Array of µ-law encoded chunks
   */
  processInputAudio(inputData, inputSampleRate = 44100) {
    // Resample to 8kHz if needed
    let processedData = inputData;
    if (inputSampleRate !== this.sampleRate) {
      processedData = this.resample(inputData, inputSampleRate, this.sampleRate);
    }

    // Add to buffer
    this.audioBuffer.push(...processedData);

    // Extract chunks
    const chunks = [];
    while (this.audioBuffer.length >= this.chunkSize) {
      const chunk = this.audioBuffer.splice(0, this.chunkSize);
      const chunkArray = new Float32Array(chunk);
      const muLawChunk = this.pcmToMuLaw(chunkArray);
      chunks.push(muLawChunk);
    }

    return chunks;
  }

  /**
   * Process received audio for playback
   * @param {string} base64Data - Base64 encoded µ-law data
   * @returns {AudioBuffer} Web Audio API AudioBuffer
   */
  async processReceivedAudio(base64Data, audioContext) {
    try {
      console.log('🎵 Processing received audio - base64 length:', base64Data.length);
      
      // Decode base64 to µ-law data
      const binaryString = atob(base64Data);
      console.log('📦 Decoded binary string length:', binaryString.length);
      
      const muLawData = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        muLawData[i] = binaryString.charCodeAt(i);
      }
      console.log('🔢 µ-law data created, length:', muLawData.length, 'first 10 bytes:', Array.from(muLawData.slice(0, 10)));

      // Convert µ-law to PCM
      const pcmData = this.muLawToPcm(muLawData);
      console.log('🔊 PCM data converted, length:', pcmData.length);
      
      // Validate PCM data content
      const maxValue = Math.max(...pcmData);
      const minValue = Math.min(...pcmData);
      const rmsValue = Math.sqrt(pcmData.reduce((sum, sample) => sum + sample * sample, 0) / pcmData.length);
      console.log('📊 PCM analysis - max:', maxValue.toFixed(4), 'min:', minValue.toFixed(4), 'RMS:', rmsValue.toFixed(4));
      console.log('🎚️ Sample preview:', pcmData.slice(0, 10).map(v => v.toFixed(4)));

      // Validate PCM data ranges
      if (maxValue > 1.0 || minValue < -1.0) {
        console.warn('⚠️ PCM values out of range, clamping to [-1.0, 1.0]');
        for (let i = 0; i < pcmData.length; i++) {
          pcmData[i] = Math.max(-1.0, Math.min(1.0, pcmData[i]));
        }
      }

      // Analyze this chunk for beep/tone detection (with error handling)
      let chunkAnalysis = { type: 'unknown', isBeep: false, dominantFreq: 0, details: 'Analysis skipped' };
      try {
        chunkAnalysis = this.analyzeAudioChunk(pcmData);
        // Warn if beep detected
        if (chunkAnalysis.isBeep) {
          console.warn(`⚠️ BEEP DETECTED: ${chunkAnalysis.details}`);
        }
      } catch (analysisError) {
        console.warn('⚠️ Audio analysis failed, continuing with basic processing:', analysisError.message);
      }
      
      // Record this chunk for debugging (using singleton audioRecorder)
      const stats = {
        maxValue,
        minValue,
        rmsValue,
        originalDuration: pcmData.length / this.sampleRate,
        audioType: chunkAnalysis.type,
        dominantFreq: chunkAnalysis.dominantFreq,
        isBeep: chunkAnalysis.isBeep
      };
      audioRecorder.recordChunk(base64Data, muLawData, pcmData, stats);

      // Check if audio has actual content (not just silence)
      if (rmsValue < 0.001) {
        console.warn('⚠️ Audio appears to be mostly silence (RMS < 0.001)');
      } else {
        console.log('✅ Audio has content (RMS:', rmsValue.toFixed(4), ')');
      }

      // Calculate expected duration at original sample rate
      const originalDuration = pcmData.length / this.sampleRate;
      console.log('⏱️ Original duration at 8kHz:', originalDuration.toFixed(3), 'seconds');
      
      // Resample PCM data from 8kHz to AudioContext sample rate
      const targetSampleRate = audioContext.sampleRate;
      console.log('🔄 Resampling from', this.sampleRate, 'Hz to', targetSampleRate, 'Hz');
      
      const resampledData = this.resample(pcmData, this.sampleRate, targetSampleRate);
      
      // Validate resampled data
      if (resampledData.length === 0) {
        console.error('❌ Resampling failed - no output data');
        return null;
      }
      
      // Calculate final duration
      const finalDuration = resampledData.length / targetSampleRate;
      console.log('⏱️ Final duration at', targetSampleRate + 'Hz:', finalDuration.toFixed(3), 'seconds');
      
      // Create AudioBuffer with proper parameters
      console.log('🎵 Creating AudioBuffer with', resampledData.length, 'samples at', targetSampleRate, 'Hz');
      const audioBuffer = audioContext.createBuffer(1, resampledData.length, targetSampleRate);
      const channelData = audioBuffer.getChannelData(0);
      channelData.set(resampledData);
      
      // Verify AudioBuffer contents
      const bufferRms = Math.sqrt(channelData.reduce((sum, sample) => sum + sample * sample, 0) / channelData.length);
      console.log('📊 AudioBuffer RMS:', bufferRms.toFixed(4));
      
      console.log('✅ AudioBuffer created successfully:', {
        channels: audioBuffer.numberOfChannels,
        length: audioBuffer.length,
        duration: audioBuffer.duration.toFixed(3) + 's',
        sampleRate: audioBuffer.sampleRate,
        bufferRms: bufferRms.toFixed(4)
      });

      return audioBuffer;
    } catch (error) {
      console.error('❌ Error processing received audio:', error);
      console.error('Stack trace:', error.stack);
      return null;
    }
  }

  /**
   * Convert audio data to base64 for transmission
   * @param {Uint8Array} muLawData - µ-law encoded data
   * @returns {string} Base64 encoded string
   */
  encodeToBase64(muLawData) {
    let binaryString = '';
    for (let i = 0; i < muLawData.length; i++) {
      binaryString += String.fromCharCode(muLawData[i]);
    }
    return btoa(binaryString);
  }

  /**
   * Calculate audio level (RMS) for visualization
   * @param {Float32Array} audioData - Audio data
   * @returns {number} Audio level (0-100)
   */
  calculateAudioLevel(audioData) {
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i] * audioData[i];
    }
    const rms = Math.sqrt(sum / audioData.length);
    return Math.min(100, rms * 100);
  }

  /**
   * Apply noise gate to reduce background noise
   * @param {Float32Array} audioData - Input audio data
   * @param {number} threshold - Noise gate threshold (0-1)
   * @returns {Float32Array} Processed audio data
   */
  applyNoiseGate(audioData, threshold = 0.01) {
    const processedData = new Float32Array(audioData.length);
    
    for (let i = 0; i < audioData.length; i++) {
      const sample = Math.abs(audioData[i]);
      if (sample > threshold) {
        processedData[i] = audioData[i];
      } else {
        processedData[i] = 0;
      }
    }
    
    return processedData;
  }

  /**
   * Calculate magnitude at a specific frequency (simple DFT)
   * @param {Float32Array} samples - Audio samples
   * @param {number} freq - Target frequency
   * @param {number} sampleRate - Sample rate
   * @returns {number} Magnitude at frequency
   */
  calculateFrequencyMagnitude(samples, freq, sampleRate) {
    try {
      let real = 0, imag = 0;
      const omega = 2 * Math.PI * freq / sampleRate;
      
      for (let i = 0; i < samples.length; i++) {
        real += samples[i] * Math.cos(omega * i);
        imag += samples[i] * Math.sin(omega * i);
      }
      
      return Math.sqrt(real * real + imag * imag) / samples.length;
    } catch (error) {
      console.warn('Error calculating frequency magnitude:', error);
      return 0;
    }
  }

  /**
   * Analyze a single audio chunk for beep/tone detection
   * @param {Float32Array} pcmData - PCM audio data
   * @returns {Object} Analysis results
   */
  analyzeAudioChunk(pcmData) {
    try {
      if (pcmData.length === 0) {
        return { type: 'empty', isBeep: false, dominantFreq: 0, details: 'No data' };
      }
      
      // Calculate RMS
      const rms = Math.sqrt(pcmData.reduce((sum, sample) => sum + sample * sample, 0) / pcmData.length);
      
      if (rms < 0.001) {
        return { type: 'silence', isBeep: false, dominantFreq: 0, details: 'Silence' };
      }
      
      // Test for common beep frequencies
      const beepFreqs = [440, 800, 1000, 1500]; // Common beep frequencies
      let maxMagnitude = 0;
      let dominantFreq = 0;
      
      for (const freq of beepFreqs) {
        const magnitude = this.calculateFrequencyMagnitude(pcmData, freq, this.sampleRate);
        if (magnitude > maxMagnitude) {
          maxMagnitude = magnitude;
          dominantFreq = freq;
        }
      }
      
      // Check if it's a pure tone (beep characteristic)
      const isPureTone = maxMagnitude > 0.2; // Threshold for pure tone detection
      
      // Check for repetitive pattern within chunk
      const hasPattern = this.detectShortPattern(pcmData);
      
      const isBeep = isPureTone || hasPattern;
      
      return {
        type: isBeep ? 'beep' : 'audio',
        isBeep,
        dominantFreq,
        magnitude: maxMagnitude,
        details: isBeep ? 
          `Beep at ${dominantFreq}Hz (magnitude: ${maxMagnitude.toFixed(3)})` : 
          'Normal audio content',
        rms
      };
    } catch (error) {
      console.warn('Error in audio chunk analysis:', error);
      return { type: 'error', isBeep: false, dominantFreq: 0, details: 'Analysis failed: ' + error.message };
    }
  }

  /**
   * Detect short repetitive patterns within a chunk
   * @param {Float32Array} samples - Audio samples
   * @returns {boolean} True if repetitive pattern detected
   */
  detectShortPattern(samples) {
    if (samples.length < 32) return false;
    
    // Test for very short repeating patterns
    const testLengths = [8, 16, 20, 32];
    
    for (const len of testLengths) {
      if (len * 3 > samples.length) continue;
      
      let similarity = 0;
      let comparisons = 0;
      
      // Compare pattern repetitions
      for (let i = 0; i < samples.length - len * 2; i += len) {
        for (let j = 0; j < len && i + j + len < samples.length; j++) {
          const diff = Math.abs(samples[i + j] - samples[i + j + len]);
          similarity += (1 - Math.min(diff, 1));
          comparisons++;
        }
      }
      
      const avgSimilarity = similarity / comparisons;
      if (avgSimilarity > 0.8) { // High similarity indicates repetition
        return true;
      }
    }
    
    return false;
  }

  /**
   * Clear internal audio buffer
   */
  clearBuffer() {
    this.audioBuffer = [];
  }

  /**
   * Get buffer size
   * @returns {number} Current buffer size
   */
  getBufferSize() {
    return this.audioBuffer.length;
  }
}

/**
 * Audio Queue for managing playback timing with seamless sequential playback
 */
export class AudioQueue {
  constructor(audioContext) {
    this.audioContext = audioContext;
    this.queue = [];
    this.isPlaying = false;
    this.nextStartTime = 0;
    this.currentSource = null;
    this.scheduledBuffers = [];
  }

  /**
   * Add audio buffer to queue
   * @param {AudioBuffer} audioBuffer - Audio buffer to queue
   */
  enqueue(audioBuffer) {
    if (!audioBuffer || audioBuffer.length === 0) {
      console.warn('🚫 AudioQueue.enqueue - Invalid or empty audio buffer');
      return;
    }

    console.log('🎵 AudioQueue.enqueue - Adding buffer:', {
      duration: audioBuffer.duration.toFixed(3) + 's',
      length: audioBuffer.length,
      channels: audioBuffer.numberOfChannels,
      sampleRate: audioBuffer.sampleRate
    });
    
    this.queue.push(audioBuffer);
    console.log('📋 Queue length now:', this.queue.length, 'isPlaying:', this.isPlaying);
    
    if (!this.isPlaying) {
      this.startPlayback();
    }
  }

  /**
   * Start playback and schedule all queued buffers
   */
  startPlayback() {
    if (this.queue.length === 0 || this.isPlaying) {
      return;
    }

    console.log('🎧 AudioQueue.startPlayback - Starting continuous playback');
    this.isPlaying = true;
    
    // Ensure AudioContext is running
    if (this.audioContext.state !== 'running') {
      console.log('🔄 Resuming AudioContext...');
      this.audioContext.resume().then(() => {
        console.log('✅ AudioContext resumed');
        this.scheduleNextBuffer();
      });
    } else {
      this.scheduleNextBuffer();
    }
  }

  /**
   * Schedule the next buffer for seamless playback
   */
  scheduleNextBuffer() {
    if (this.queue.length === 0) {
      console.log('📋 AudioQueue - Queue empty, stopping playback');
      this.isPlaying = false;
      this.currentSource = null;
      return;
    }

    const audioBuffer = this.queue.shift();
    const currentTime = this.audioContext.currentTime;
    
    // Calculate start time for seamless playback
    const startTime = Math.max(currentTime + 0.01, this.nextStartTime); // Small buffer to prevent gaps
    
    console.log('🎶 AudioQueue.scheduleNextBuffer:', {
      currentTime: currentTime.toFixed(3),
      startTime: startTime.toFixed(3),
      duration: audioBuffer.duration.toFixed(3)
    });
    
    // Create and configure source
    const source = this.audioContext.createBufferSource();
    source.buffer = audioBuffer;
    
    // Direct connection to destination (no gain node as requested)
    source.connect(this.audioContext.destination);
    
    // Validate audio content
    const channelData = audioBuffer.getChannelData(0);
    const rms = Math.sqrt(channelData.reduce((sum, sample) => sum + sample * sample, 0) / channelData.length);
    console.log('📊 Audio content RMS:', rms.toFixed(4), 'peak:', Math.max(...channelData.slice(0, 100)).toFixed(4));
    
    // Schedule playback
    source.start(startTime);
    this.currentSource = source;
    this.nextStartTime = startTime + audioBuffer.duration;
    
    // Schedule next buffer and handle completion
    source.onended = () => {
      console.log('✅ AudioQueue - Buffer finished, scheduling next...');
      this.scheduleNextBuffer();
    };
    
    // Handle errors
    source.onerror = (error) => {
      console.error('❌ AudioQueue - Playback error:', error);
      this.scheduleNextBuffer(); // Try to continue with next buffer
    };
  }

  /**
   * Clear the audio queue and stop current playback
   */
  clear() {
    console.log('🧹 AudioQueue.clear - Clearing queue and stopping playback');
    
    // Stop current source if playing
    if (this.currentSource) {
      try {
        this.currentSource.stop();
      } catch (e) {
        // Source might already be stopped
      }
      this.currentSource = null;
    }
    
    // Clear queue and reset state
    this.queue = [];
    this.isPlaying = false;
    this.nextStartTime = 0;
    this.scheduledBuffers = [];
  }

  /**
   * Test audio playback with a simple tone
   */
  testAudioPlayback() {
    if (!this.audioContext) {
      console.error('❌ AudioContext not available for test');
      return;
    }

    console.log('🎵 Testing audio playback with 440Hz tone...');

    // Create a 0.5-second 440Hz sine wave
    const sampleRate = this.audioContext.sampleRate;
    const duration = 0.5; // 0.5 second
    const frequency = 440; // A4 note
    const length = sampleRate * duration;

    const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate);
    const channelData = audioBuffer.getChannelData(0);

    for (let i = 0; i < length; i++) {
      channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.2;
    }

    // Use the queue system to test
    this.enqueue(audioBuffer);
    console.log('✅ Test tone queued - should hear 440Hz for 0.5 seconds');
  }

  /**
   * Get current queue status
   */
  getStatus() {
    return {
      queueLength: this.queue.length,
      isPlaying: this.isPlaying,
      nextStartTime: this.nextStartTime,
      audioContextState: this.audioContext.state
    };
  }
}

// Export singleton instance
export const audioProcessor = new AudioProcessor();

/**
 * AudioRecorder class for debugging - records received audio for download
 */
export class AudioRecorder {
  constructor() {
    this.isRecording = false;
    this.recordedChunks = [];
    this.rawMuLawData = [];
    this.pcmData = [];
    this.metadata = {
      startTime: null,
      endTime: null,
      totalChunks: 0,
      totalDuration: 0,
      sampleRate: 8000,
      processingStats: []
    };
  }

  /**
   * Start recording audio
   */
  startRecording() {
    console.log('🎙️ AudioRecorder: Starting recording session');
    this.isRecording = true;
    this.recordedChunks = [];
    this.rawMuLawData = [];
    this.pcmData = [];
    this.metadata = {
      startTime: Date.now(),
      endTime: null,
      totalChunks: 0,
      totalDuration: 0,
      sampleRate: 8000,
      processingStats: []
    };
  }

  /**
   * Record a chunk of audio data
   * @param {string} base64Data - Base64 encoded µ-law data
   * @param {Uint8Array} muLawData - Raw µ-law data
   * @param {Float32Array} pcmData - Converted PCM data
   * @param {Object} stats - Processing statistics
   */
  recordChunk(base64Data, muLawData, pcmData, stats = {}) {
    if (!this.isRecording) return;

    const timestamp = Date.now();
    const chunkInfo = {
      timestamp,
      base64Length: base64Data.length,
      muLawLength: muLawData.length,
      pcmLength: pcmData.length,
      duration: pcmData.length / this.metadata.sampleRate,
      ...stats
    };

    // Store the data
    this.recordedChunks.push({
      timestamp,
      base64Data,
      muLawData: new Uint8Array(muLawData),
      pcmData: new Float32Array(pcmData)
    });

    // Accumulate raw data for final file creation
    this.rawMuLawData.push(...muLawData);
    this.pcmData.push(...pcmData);

    // Update metadata
    this.metadata.totalChunks++;
    this.metadata.totalDuration += chunkInfo.duration;
    this.metadata.processingStats.push(chunkInfo);

    console.log(`📝 Recorded chunk ${this.metadata.totalChunks}: ${chunkInfo.duration.toFixed(3)}s (total: ${this.metadata.totalDuration.toFixed(3)}s)`);
  }

  /**
   * Stop recording and prepare data for download
   */
  stopRecording() {
    if (!this.isRecording) return;

    console.log('🛑 AudioRecorder: Stopping recording session');
    this.isRecording = false;
    this.metadata.endTime = Date.now();
    
    console.log('📊 Recording complete:', {
      duration: this.metadata.totalDuration.toFixed(3) + 's',
      chunks: this.metadata.totalChunks,
      totalSamples: this.pcmData.length
    });
  }

  /**
   * Create a WAV file from recorded PCM data
   * @returns {Blob} WAV file blob
   */
  createWavFile() {
    if (this.pcmData.length === 0) {
      console.warn('⚠️ No PCM data to create WAV file');
      return null;
    }

    const sampleRate = this.metadata.sampleRate;
    const numChannels = 1;
    const bytesPerSample = 2; // 16-bit
    const numSamples = this.pcmData.length;
    
    // Convert Float32 PCM to 16-bit integers
    const int16Data = new Int16Array(numSamples);
    for (let i = 0; i < numSamples; i++) {
      // Clamp to [-1, 1] range and convert to 16-bit
      const sample = Math.max(-1, Math.min(1, this.pcmData[i]));
      int16Data[i] = Math.round(sample * 32767);
    }

    // Calculate sizes
    const dataSize = numSamples * bytesPerSample;
    const fileSize = 44 + dataSize; // 44 bytes for WAV header

    // Create WAV file buffer
    const buffer = new ArrayBuffer(fileSize);
    const view = new DataView(buffer);
    
    let offset = 0;
    
    // WAV Header
    // "RIFF" chunk descriptor
    view.setUint32(offset, 0x52494646, false); offset += 4; // "RIFF"
    view.setUint32(offset, fileSize - 8, true); offset += 4; // File size - 8
    view.setUint32(offset, 0x57415645, false); offset += 4; // "WAVE"
    
    // "fmt " sub-chunk
    view.setUint32(offset, 0x666d7420, false); offset += 4; // "fmt "
    view.setUint32(offset, 16, true); offset += 4; // Sub-chunk size
    view.setUint16(offset, 1, true); offset += 2; // Audio format (PCM)
    view.setUint16(offset, numChannels, true); offset += 2; // Number of channels
    view.setUint32(offset, sampleRate, true); offset += 4; // Sample rate
    view.setUint32(offset, sampleRate * numChannels * bytesPerSample, true); offset += 4; // Byte rate
    view.setUint16(offset, numChannels * bytesPerSample, true); offset += 2; // Block align
    view.setUint16(offset, bytesPerSample * 8, true); offset += 2; // Bits per sample
    
    // "data" sub-chunk
    view.setUint32(offset, 0x64617461, false); offset += 4; // "data"
    view.setUint32(offset, dataSize, true); offset += 4; // Data size
    
    // Write audio data
    for (let i = 0; i < numSamples; i++) {
      view.setInt16(offset, int16Data[i], true);
      offset += 2;
    }

    console.log('🎵 Created WAV file:', {
      size: fileSize + ' bytes',
      duration: (numSamples / sampleRate).toFixed(3) + 's',
      sampleRate: sampleRate + 'Hz',
      samples: numSamples
    });

    return new Blob([buffer], { type: 'audio/wav' });
  }

  /**
   * Create raw µ-law data file
   * @returns {Blob} Raw µ-law data blob
   */
  createRawMuLawFile() {
    if (this.rawMuLawData.length === 0) {
      console.warn('⚠️ No µ-law data to create raw file');
      return null;
    }

    const uint8Array = new Uint8Array(this.rawMuLawData);
    console.log('📁 Created raw µ-law file:', {
      size: uint8Array.length + ' bytes',
      estimatedDuration: (uint8Array.length / this.metadata.sampleRate).toFixed(3) + 's'
    });

    return new Blob([uint8Array], { type: 'application/octet-stream' });
  }

  /**
   * Create metadata JSON file
   * @returns {Blob} JSON metadata blob
   */
  createMetadataFile() {
    const audioAnalysis = this.analyzeAudioContent();
    
    const metadata = {
      ...this.metadata,
      sessionDuration: this.metadata.endTime - this.metadata.startTime,
      averageChunkSize: this.rawMuLawData.length / this.metadata.totalChunks,
      summary: {
        totalAudioDuration: this.metadata.totalDuration,
        totalChunks: this.metadata.totalChunks,
        totalBytes: this.rawMuLawData.length,
        averageRMS: this.calculateAverageRMS()
      },
      audioAnalysis: {
        type: audioAnalysis.type,
        confidence: audioAnalysis.confidence,
        details: audioAnalysis.details,
        rms: audioAnalysis.rms,
        dynamicRange: audioAnalysis.dynamicRange,
        dominantFreq: audioAnalysis.dominantFreq,
        isRepetitive: audioAnalysis.isRepetitive,
        warning: audioAnalysis.type === 'pure_tone' || audioAnalysis.type === 'beep_pattern' ? 
          'DETECTED BEEP/TONE INSTEAD OF SPEECH - Backend may be sending placeholder audio' : null
      }
    };

    const jsonString = JSON.stringify(metadata, null, 2);
    console.log('📋 Created metadata file:', metadata.summary);
    
    // Log analysis results
    console.log('🔍 Audio Content Analysis:', audioAnalysis);
    if (audioAnalysis.type === 'pure_tone' || audioAnalysis.type === 'beep_pattern') {
      console.warn('⚠️ PROBLEM IDENTIFIED: Backend is sending beep/tone data instead of AI speech!');
      console.warn('🔧 This indicates the TTS (Text-to-Speech) system is not working properly on the backend.');
    }

    return new Blob([jsonString], { type: 'application/json' });
  }

  /**
   * Calculate average RMS of recorded audio
   * @returns {number} Average RMS value
   */
  calculateAverageRMS() {
    if (this.pcmData.length === 0) return 0;
    
    const sumSquares = this.pcmData.reduce((sum, sample) => sum + sample * sample, 0);
    return Math.sqrt(sumSquares / this.pcmData.length);
  }

  /**
   * Analyze audio content to detect beeps, tones, or speech patterns
   * @returns {Object} Audio analysis results
   */
  analyzeAudioContent() {
    try {
      if (this.pcmData.length === 0) {
        return { type: 'empty', confidence: 1.0, details: 'No audio data' };
      }

      const samples = this.pcmData;
      const sampleRate = this.metadata.sampleRate;
      
      // Calculate basic statistics
      const rms = this.calculateAverageRMS();
      const maxValue = Math.max(...samples);
      const minValue = Math.min(...samples);
      const dynamicRange = maxValue - minValue;
      
      // Detect if audio is mostly silence
      if (rms < 0.001) {
        return {
          type: 'silence',
          confidence: 1.0,
          details: `RMS too low: ${rms.toFixed(6)}`,
          rms,
          dynamicRange
        };
      }
      
      // Simple analysis without complex FFT
      const analysis = this.performSimpleFrequencyAnalysis(samples, sampleRate);
      
      // Detect repetitive patterns (characteristic of beeps/tones)
      const repetitionAnalysis = this.detectRepetitivePatterns(samples);
      
      // Classify audio type
      let audioType = 'unknown';
      let confidence = 0.5;
      let details = '';
      
      if (analysis.isPureTone) {
        audioType = 'pure_tone';
        confidence = 0.9;
        details = `Pure tone detected at ~${analysis.dominantFreq.toFixed(1)}Hz`;
      } else if (repetitionAnalysis.isRepetitive) {
        audioType = 'beep_pattern';
        confidence = 0.8;
        details = `Repetitive beep pattern detected, period: ${repetitionAnalysis.period}ms`;
      } else if (analysis.hasVariedSpectrum && rms > 0.01) {
        audioType = 'speech_like';
        confidence = 0.7;
        details = 'Varied frequency spectrum suggesting speech';
      } else {
        audioType = 'noise_or_tone';
        confidence = 0.6;
        details = 'Consistent tone or noise pattern';
      }
      
      return {
        type: audioType,
        confidence,
        details,
        rms,
        dynamicRange,
        dominantFreq: analysis.dominantFreq,
        spectralVariance: analysis.spectralVariance,
        isRepetitive: repetitionAnalysis.isRepetitive,
        duration: samples.length / sampleRate
      };
    } catch (error) {
      console.warn('Error in audio content analysis:', error);
      return {
        type: 'error',
        confidence: 0.0,
        details: 'Analysis failed: ' + error.message,
        rms: 0,
        dynamicRange: 0
      };
    }
  }

  /**
   * Simple frequency analysis to detect frequency characteristics
   * @param {Array} samples - Audio samples
   * @param {number} sampleRate - Sample rate
   * @returns {Object} Frequency analysis results
   */
  performSimpleFrequencyAnalysis(samples, sampleRate) {
    try {
      // Simple frequency analysis without full FFT
      const chunkSize = Math.min(1024, samples.length);
      
      // Test for dominant frequencies (simplified approach)
      const testFreqs = [440, 800, 1000, 1500, 2000, 3000]; // Common beep frequencies
      let dominantFreq = 0;
      let maxMagnitude = 0;
      
      for (const freq of testFreqs) {
        const magnitude = this.calculateSimpleFrequencyMagnitude(samples.slice(0, chunkSize), freq, sampleRate);
        if (magnitude > maxMagnitude) {
          maxMagnitude = magnitude;
          dominantFreq = freq;
        }
      }
      
      // Calculate spectral variance (speech has more varied spectrum)
      const spectralVariance = this.calculateSpectralVariance(samples.slice(0, chunkSize), sampleRate);
      
      return {
        dominantFreq,
        maxMagnitude,
        spectralVariance,
        isPureTone: maxMagnitude > 0.3 && spectralVariance < 0.1,
        hasVariedSpectrum: spectralVariance > 0.2
      };
    } catch (error) {
      console.warn('Error in frequency analysis:', error);
      return {
        dominantFreq: 0,
        maxMagnitude: 0,
        spectralVariance: 0,
        isPureTone: false,
        hasVariedSpectrum: false
      };
    }
  }

  /**
   * Calculate magnitude at a specific frequency (simplified)
   * @param {Array} samples - Audio samples
   * @param {number} freq - Target frequency
   * @param {number} sampleRate - Sample rate
   * @returns {number} Magnitude at frequency
   */
  calculateSimpleFrequencyMagnitude(samples, freq, sampleRate) {
    try {
      let real = 0, imag = 0;
      const omega = 2 * Math.PI * freq / sampleRate;
      
      for (let i = 0; i < samples.length; i++) {
        real += samples[i] * Math.cos(omega * i);
        imag += samples[i] * Math.sin(omega * i);
      }
      
      return Math.sqrt(real * real + imag * imag) / samples.length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Calculate magnitude at a specific frequency (for AudioRecorder)
   * @param {Array} samples - Audio samples
   * @param {number} freq - Target frequency
   * @param {number} sampleRate - Sample rate
   * @returns {number} Magnitude at frequency
   */
  calculateFrequencyMagnitude(samples, freq, sampleRate) {
    try {
      let real = 0, imag = 0;
      const omega = 2 * Math.PI * freq / sampleRate;
      
      for (let i = 0; i < samples.length; i++) {
        real += samples[i] * Math.cos(omega * i);
        imag += samples[i] * Math.sin(omega * i);
      }
      
      return Math.sqrt(real * real + imag * imag) / samples.length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Calculate spectral variance
   * @param {Array} samples - Audio samples
   * @param {number} sampleRate - Sample rate
   * @returns {number} Spectral variance
   */
  calculateSpectralVariance(samples, sampleRate) {
    try {
      const freqs = [200, 400, 800, 1600, 3200, 6400];
      const magnitudes = freqs.map(f => this.calculateSimpleFrequencyMagnitude(samples, f, sampleRate));
      
      const mean = magnitudes.reduce((sum, mag) => sum + mag, 0) / magnitudes.length;
      const variance = magnitudes.reduce((sum, mag) => sum + Math.pow(mag - mean, 2), 0) / magnitudes.length;
      
      return variance;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Detect repetitive patterns in audio
   * @param {Array} samples - Audio samples
   * @returns {Object} Repetition analysis
   */
  detectRepetitivePatterns(samples) {
    if (samples.length < 160) return { isRepetitive: false, period: 0 };
    
    // Test for common beep periods (in samples)
    const testPeriods = [80, 160, 320, 480, 640]; // Various beep lengths at 8kHz
    
    for (const period of testPeriods) {
      if (period * 3 > samples.length) continue;
      
      let correlation = 0;
      let validSamples = 0;
      
      // Check correlation between repeating segments
      for (let i = 0; i < samples.length - period * 2; i += period) {
        for (let j = 0; j < Math.min(period, samples.length - i - period); j++) {
          correlation += Math.abs(samples[i + j] - samples[i + j + period]);
          validSamples++;
        }
      }
      
      const avgDifference = correlation / validSamples;
      
      // If segments are very similar, it's repetitive
      if (avgDifference < 0.1) {
        return {
          isRepetitive: true,
          period: (period / 8) * 1000, // Convert to milliseconds
          similarity: 1 - avgDifference
        };
      }
    }
    
    return { isRepetitive: false, period: 0 };
  }

  /**
   * Download all recorded audio files
   */
  async downloadRecording() {
    if (!this.metadata.endTime) {
      this.stopRecording();
    }

    console.log('⬇️ Starting audio download...');

    // Download WAV file
    const wavBlob = this.createWavFile();
    if (wavBlob) {
      this.downloadBlob(wavBlob, 'received_audio.wav');
    }

    // Download raw µ-law file
    const rawBlob = this.createRawMuLawFile();
    if (rawBlob) {
      this.downloadBlob(rawBlob, 'received_audio_raw.bin');
    }

    // Download metadata
    const metadataBlob = this.createMetadataFile();
    if (metadataBlob) {
      this.downloadBlob(metadataBlob, 'audio_metadata.json');
    }

    console.log('✅ Audio download complete!');
  }

  /**
   * Trigger browser download of blob
   * @param {Blob} blob - File blob to download
   * @param {string} filename - Download filename
   */
  downloadBlob(blob, filename) {
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    console.log(`💾 Downloaded: ${filename}`);
  }

  /**
   * Clear all recorded data
   */
  clearRecording() {
    console.log('🗑️ Clearing recorded audio data');
    this.recordedChunks = [];
    this.rawMuLawData = [];
    this.pcmData = [];
    this.isRecording = false;
  }

  /**
   * Get recording status
   * @returns {Object} Current recording status
   */
  getStatus() {
    return {
      isRecording: this.isRecording,
      totalChunks: this.metadata.totalChunks,
      totalDuration: this.metadata.totalDuration,
      totalBytes: this.rawMuLawData.length
    };
  }
}

// Create singleton audio recorder instance
export const audioRecorder = new AudioRecorder();

// Expose for debugging in browser console
if (typeof window !== 'undefined') {
  window.audioProcessor = audioProcessor;
  window.AudioQueue = AudioQueue;
  window.audioRecorder = audioRecorder;
}
