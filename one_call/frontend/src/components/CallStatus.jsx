import React, { useState, useEffect } from 'react';
import { 
  Wifi, 
  WifiOff, 
  Phone, 
  PhoneOff, 
  Clock, 
  AlertCircle,
  CheckCircle,
  Loader2,
  Signal
} from 'lucide-react';
import { CONNECTION_STATES, CALL_STATES } from '../hooks/useVoiceSocket';
import { MIC_STATES } from '../hooks/useMicrophone';

const CallStatus = ({ 
  connectionState, 
  callState, 
  micState, 
  sessionId, 
  streamId,
  audioLevel = 0 
}) => {
  const [callDuration, setCallDuration] = useState(0);
  const [callStartTime, setCallStartTime] = useState(null);

  // Track call duration
  useEffect(() => {
    if (callState === CALL_STATES.IN_CALL && !callStartTime) {
      setCallStartTime(Date.now());
    } else if (callState !== CALL_STATES.IN_CALL) {
      setCallStartTime(null);
      setCallDuration(0);
    }
  }, [callState, callStartTime]);

  useEffect(() => {
    let interval;
    if (callStartTime && callState === CALL_STATES.IN_CALL) {
      interval = setInterval(() => {
        setCallDuration(Math.floor((Date.now() - callStartTime) / 1000));
      }, 1000);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [callStartTime, callState]);

  // Format duration as MM:SS
  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Get connection status info
  const getConnectionStatus = () => {
    switch (connectionState) {
      case CONNECTION_STATES.DISCONNECTED:
        return {
          icon: WifiOff,
          text: 'Disconnected',
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          dotColor: 'bg-gray-400'
        };
      case CONNECTION_STATES.CONNECTING:
        return {
          icon: Loader2,
          text: 'Connecting...',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          dotColor: 'bg-yellow-500',
          animate: 'animate-spin'
        };
      case CONNECTION_STATES.CONNECTED:
        return {
          icon: Wifi,
          text: 'Connected',
          color: 'text-success-600',
          bgColor: 'bg-success-100',
          dotColor: 'bg-success-500'
        };
      case CONNECTION_STATES.ERROR:
        return {
          icon: AlertCircle,
          text: 'Connection Error',
          color: 'text-danger-600',
          bgColor: 'bg-danger-100',
          dotColor: 'bg-danger-500'
        };
      default:
        return {
          icon: WifiOff,
          text: 'Unknown',
          color: 'text-gray-500',
          bgColor: 'bg-gray-100',
          dotColor: 'bg-gray-400'
        };
    }
  };

  // Get call status info
  const getCallStatus = () => {
    switch (callState) {
      case CALL_STATES.IDLE:
        return {
          icon: PhoneOff,
          text: 'Ready to Call',
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          dotColor: 'bg-gray-400'
        };
      case CALL_STATES.CONNECTING:
        return {
          icon: Loader2,
          text: 'Connecting Call...',
          color: 'text-primary-600',
          bgColor: 'bg-primary-100',
          dotColor: 'bg-primary-500',
          animate: 'animate-spin'
        };
      case CALL_STATES.IN_CALL:
        return {
          icon: Phone,
          text: 'In Call',
          color: 'text-success-600',
          bgColor: 'bg-success-100',
          dotColor: 'bg-success-500',
          duration: formatDuration(callDuration)
        };
      case CALL_STATES.ENDING:
        return {
          icon: Loader2,
          text: 'Ending Call...',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          dotColor: 'bg-yellow-500',
          animate: 'animate-spin'
        };
      case CALL_STATES.ENDED:
        return {
          icon: PhoneOff,
          text: 'Call Ended',
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          dotColor: 'bg-gray-400'
        };
      case CALL_STATES.ERROR:
        return {
          icon: AlertCircle,
          text: 'Call Error',
          color: 'text-danger-600',
          bgColor: 'bg-danger-100',
          dotColor: 'bg-danger-500'
        };
      default:
        return {
          icon: PhoneOff,
          text: 'Unknown',
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          dotColor: 'bg-gray-400'
        };
    }
  };

  // Get microphone status info
  const getMicStatus = () => {
    switch (micState) {
      case MIC_STATES.IDLE:
        return {
          text: 'Microphone Idle',
          color: 'text-gray-500',
          dotColor: 'bg-gray-400'
        };
      case MIC_STATES.REQUESTING:
        return {
          text: 'Requesting Access...',
          color: 'text-yellow-600',
          dotColor: 'bg-yellow-500'
        };
      case MIC_STATES.GRANTED:
        return {
          text: 'Microphone Ready',
          color: 'text-success-600',
          dotColor: 'bg-success-500'
        };
      case MIC_STATES.DENIED:
        return {
          text: 'Access Denied',
          color: 'text-danger-600',
          dotColor: 'bg-danger-500'
        };
      case MIC_STATES.ERROR:
        return {
          text: 'Microphone Error',
          color: 'text-danger-600',
          dotColor: 'bg-danger-500'
        };
      default:
        return {
          text: 'Unknown',
          color: 'text-gray-500',
          dotColor: 'bg-gray-400'
        };
    }
  };

  const connectionStatus = getConnectionStatus();
  const callStatus = getCallStatus();
  const micStatus = getMicStatus();

  return (
    <div className="card p-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Status</h2>
      </div>

      {/* Main Status Display */}
      <div className="space-y-4">
        {/* Connection Status */}
        <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${connectionStatus.bgColor}`}>
              <connectionStatus.icon 
                size={20} 
                className={`${connectionStatus.color} ${connectionStatus.animate || ''}`} 
              />
            </div>
            <div>
              <p className="font-medium text-gray-900">Connection</p>
              <p className={`text-sm ${connectionStatus.color}`}>
                {connectionStatus.text}
              </p>
            </div>
          </div>
          <div className={`w-3 h-3 rounded-full ${connectionStatus.dotColor} ${
            connectionState === CONNECTION_STATES.CONNECTED ? 'animate-pulse' : ''
          }`}></div>
        </div>

        {/* Call Status */}
        <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${callStatus.bgColor}`}>
              <callStatus.icon 
                size={20} 
                className={`${callStatus.color} ${callStatus.animate || ''}`} 
              />
            </div>
            <div>
              <p className="font-medium text-gray-900">Call Status</p>
              <div className="flex items-center space-x-2">
                <p className={`text-sm ${callStatus.color}`}>
                  {callStatus.text}
                </p>
                {callStatus.duration && (
                  <span className="text-sm text-gray-500">
                    • {callStatus.duration}
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className={`w-3 h-3 rounded-full ${callStatus.dotColor} ${
            callState === CALL_STATES.IN_CALL ? 'animate-pulse' : ''
          }`}></div>
        </div>

        {/* Microphone Status */}
        <div className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-full bg-gray-100">
              <Signal size={20} className="text-gray-600" />
            </div>
            <div>
              <p className="font-medium text-gray-900">Microphone</p>
              <p className={`text-sm ${micStatus.color}`}>
                {micStatus.text}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {/* Audio level indicator */}
            {micState === MIC_STATES.GRANTED && audioLevel > 0 && (
              <div className="flex items-center space-x-1">
                {Array.from({ length: 3 }, (_, i) => (
                  <div
                    key={i}
                    className={`w-1 h-3 rounded-full transition-colors duration-150 ${
                      audioLevel > (i + 1) * 20 
                        ? 'bg-success-500' 
                        : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            )}
            <div className={`w-3 h-3 rounded-full ${micStatus.dotColor}`}></div>
          </div>
        </div>
      </div>

      {/* Session Information */}
      {(sessionId || streamId) && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-900 mb-2">Session Info</h3>
          <div className="space-y-1 text-xs text-gray-500">
            {sessionId && (
              <p>Session: {sessionId.substring(0, 20)}...</p>
            )}
            {streamId && (
              <p>Stream: {streamId.substring(0, 20)}...</p>
            )}
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex justify-center space-x-2 text-xs text-gray-500">
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-success-500 rounded-full"></div>
            <span>Active</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
            <span>Connecting</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
            <span>Inactive</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-2 h-2 bg-danger-500 rounded-full"></div>
            <span>Error</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CallStatus;
