#!/bin/bash

# One call Frontend Deployment Script
# This script builds and deploys the React frontend application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
BUILD_DIR="build"
DEPLOY_DIR="/var/www/ai-voice-mate"
BACKUP_DIR="/var/backups/ai-voice-mate"
NGINX_CONFIG="/etc/nginx/sites-available/ai-voice-mate"

echo -e "${GREEN}🚀 Starting One call Frontend Deployment${NC}"

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: package.json not found. Please run this script from the frontend directory.${NC}"
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Error: Node.js is not installed.${NC}"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ Error: npm is not installed.${NC}"
    exit 1
fi

echo -e "${YELLOW}📦 Installing dependencies...${NC}"
npm ci

echo -e "${YELLOW}🔧 Building application...${NC}"
npm run build

if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}❌ Error: Build failed. Build directory not found.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Build completed successfully${NC}"

# Create backup if deployment directory exists
if [ -d "$DEPLOY_DIR" ]; then
    echo -e "${YELLOW}💾 Creating backup...${NC}"
    sudo mkdir -p "$BACKUP_DIR"
    sudo cp -r "$DEPLOY_DIR" "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)"
    echo -e "${GREEN}✅ Backup created${NC}"
fi

# Deploy the application
echo -e "${YELLOW}🚀 Deploying application...${NC}"
sudo mkdir -p "$DEPLOY_DIR"
sudo cp -r "$BUILD_DIR"/* "$DEPLOY_DIR/"
sudo chown -R www-data:www-data "$DEPLOY_DIR"
sudo chmod -R 755 "$DEPLOY_DIR"

echo -e "${GREEN}✅ Application deployed to $DEPLOY_DIR${NC}"

# Create nginx configuration if it doesn't exist
if [ ! -f "$NGINX_CONFIG" ]; then
    echo -e "${YELLOW}⚙️ Creating nginx configuration...${NC}"
    sudo tee "$NGINX_CONFIG" > /dev/null <<EOF
server {
    listen 80;
    server_name your-domain.com;  # Change this to your domain
    root $DEPLOY_DIR;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Handle React Router
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # WebSocket proxy (if backend is on same server)
    location /ws {
        proxy_pass http://localhost:5010;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

    # Enable the site
    sudo ln -sf "$NGINX_CONFIG" /etc/nginx/sites-enabled/
    echo -e "${GREEN}✅ Nginx configuration created${NC}"
fi

# Test nginx configuration
echo -e "${YELLOW}🔍 Testing nginx configuration...${NC}"
if sudo nginx -t; then
    echo -e "${GREEN}✅ Nginx configuration is valid${NC}"
    
    # Reload nginx
    echo -e "${YELLOW}🔄 Reloading nginx...${NC}"
    sudo systemctl reload nginx
    echo -e "${GREEN}✅ Nginx reloaded${NC}"
else
    echo -e "${RED}❌ Error: Nginx configuration is invalid${NC}"
    exit 1
fi

# Check if nginx is running
if sudo systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✅ Nginx is running${NC}"
else
    echo -e "${YELLOW}⚠️ Starting nginx...${NC}"
    sudo systemctl start nginx
fi

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo -e "${YELLOW}📝 Next steps:${NC}"
echo -e "   1. Update the server_name in $NGINX_CONFIG with your domain"
echo -e "   2. Configure SSL/TLS certificate for HTTPS (required for microphone access)"
echo -e "   3. Ensure the WebSocket backend is running on port 5010"
echo -e "   4. Test the application in your browser"

# Optional: Run a quick health check
if command -v curl &> /dev/null; then
    echo -e "${YELLOW}🏥 Running health check...${NC}"
    if curl -f -s http://localhost/ > /dev/null; then
        echo -e "${GREEN}✅ Application is responding${NC}"
    else
        echo -e "${YELLOW}⚠️ Application may not be responding correctly${NC}"
    fi
fi
