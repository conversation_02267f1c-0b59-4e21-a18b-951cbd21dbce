#!/usr/bin/env python3
"""
Demo script showing AWS Transcribe integration usage
"""

import sys
import os
import time
import threading
from unittest.mock import Mock

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

def demo_aws_transcribe():
    """Demonstrate AWS Transcribe integration"""
    print("AWS Transcribe Integration Demo")
    print("=" * 40)
    
    try:
        # Import required modules
        from tutor.modules.audio.transcription_factory import TranscriptionFactory
        from tutor.modules.models import models
        
        # Create mock user and logger
        mock_user = Mock()
        mock_user.session = "demo_session"
        mock_user.mobile = "1234567890"
        mock_user.loop = None
        mock_user.stream_id = None
        mock_user.bot_manager = Mock()
        mock_user.bot_manager.start_new_call.return_value = "Hello! I'm ready to transcribe your audio."
        mock_user.sync_send = Mock()
        mock_user.end_ai_call = Mock()
        
        mock_logger = Mock()
        
        print("1. Checking available transcription services...")
        available_services = TranscriptionFactory.get_available_services()
        print(f"   Available: {available_services}")
        
        print("\n2. Validating AWS Transcribe configuration...")
        is_valid, error_msg = TranscriptionFactory.validate_service_config("aws_transcribe")
        if is_valid:
            print("   ✓ AWS Transcribe is properly configured")
        else:
            print(f"   ✗ Configuration issue: {error_msg}")
            print("   Note: This is expected if AWS credentials are not set")
        
        print("\n3. Creating transcription service with fallback...")
        try:
            # Try to create AWS Transcribe service with fallback
            transcription_service = TranscriptionFactory.create_transcription_service(
                user=mock_user,
                logger_instance=mock_logger,
                service_type="aws_transcribe",
                enable_fallback=True
            )
            
            service_type = type(transcription_service).__module__
            if "aws_transcribe" in service_type:
                print("   ✓ AWS Transcribe service created successfully")
            elif "assembly_ai" in service_type:
                print("   ✓ Fallback to AssemblyAI service (AWS not configured)")
            else:
                print(f"   ✓ Service created: {service_type}")
                
        except Exception as e:
            print(f"   ✗ Error creating service: {e}")
            return False
        
        print("\n4. Testing service connection...")
        try:
            if transcription_service.connect():
                print("   ✓ Service connected successfully")
                
                print("\n5. Simulating audio processing...")
                # Add some test audio chunks
                for i in range(3):
                    test_audio = f"test audio chunk {i}".encode() * 50
                    transcription_service.add_audio_chunk(test_audio)
                    print(f"   ✓ Added audio chunk {i+1}")
                    time.sleep(0.5)
                
                print("\n6. Closing service...")
                transcription_service.close()
                print("   ✓ Service closed successfully")
                
            else:
                print("   ✗ Service connection failed")
                
        except Exception as e:
            print(f"   ✗ Error during service operations: {e}")
        
        print("\n7. Configuration Summary:")
        print(f"   Primary service: {models.env.transcription_service}")
        print(f"   Fallback enabled: {models.env.transcription_fallback_enabled}")
        print(f"   AWS region: {models.env.aws_transcribe_region}")
        print(f"   AWS language: {models.env.aws_transcribe_language_code}")
        print(f"   Sample rate: {models.env.aws_transcribe_sample_rate}")
        
        print("\n✓ Demo completed successfully!")
        return True
        
    except Exception as e:
        print(f"✗ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_setup_instructions():
    """Show setup instructions for AWS Transcribe"""
    print("\nAWS Transcribe Setup Instructions:")
    print("-" * 40)
    print("1. Get AWS credentials with Transcribe permissions")
    print("2. Set environment variables in .env file:")
    print("   AWS_TRANSCRIBE_ACCESS_KEY_ID=your_key")
    print("   AWS_TRANSCRIBE_SECRET_ACCESS_KEY=your_secret")
    print("   TRANSCRIPTION_SERVICE=aws_transcribe")
    print("3. Install dependencies: pip install boto3 amazon-transcribe")
    print("4. Run this demo again to test the integration")
    print("\nSee AWS_TRANSCRIBE_SETUP.md for detailed instructions.")

if __name__ == "__main__":
    success = demo_aws_transcribe()
    
    if not success:
        show_setup_instructions()
        sys.exit(1)
    else:
        print("\n🎉 AWS Transcribe integration is working!")
        sys.exit(0)
