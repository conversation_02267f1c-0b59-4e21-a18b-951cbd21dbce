#!/usr/bin/env python3
"""
Create an audio sample file with "Hello world" for testing AWS Transcribe
"""

import os
import sys
import wave
import struct
import math

def create_hello_world_audio(filename="hello_world_sample.wav", duration=2.0, sample_rate=16000):
    """
    Create a simple audio file with synthesized "Hello world" speech
    This creates a basic tone pattern that represents speech-like audio
    """
    
    # Calculate number of samples
    num_samples = int(duration * sample_rate)
    
    # Create audio data - simulate speech with varying frequencies
    audio_data = []
    
    # "Hello" - higher frequency pattern (0-1 seconds)
    hello_samples = int(sample_rate * 1.0)
    for i in range(hello_samples):
        t = i / sample_rate
        # Simulate "Hello" with frequency modulation
        freq = 200 + 100 * math.sin(2 * math.pi * 5 * t)  # Varying frequency
        amplitude = 0.3 * math.exp(-t * 2)  # Decaying amplitude
        sample = amplitude * math.sin(2 * math.pi * freq * t)
        audio_data.append(sample)
    
    # "World" - lower frequency pattern (1-2 seconds)
    world_samples = num_samples - hello_samples
    for i in range(world_samples):
        t = (i + hello_samples) / sample_rate
        # Simulate "World" with different frequency pattern
        freq = 150 + 80 * math.sin(2 * math.pi * 3 * (t - 1))
        amplitude = 0.3 * math.exp(-(t - 1) * 3)
        sample = amplitude * math.sin(2 * math.pi * freq * t)
        audio_data.append(sample)
    
    # Convert to 16-bit PCM
    pcm_data = []
    for sample in audio_data:
        # Convert to 16-bit signed integer
        pcm_sample = int(sample * 32767)
        pcm_sample = max(-32768, min(32767, pcm_sample))  # Clamp to 16-bit range
        pcm_data.append(pcm_sample)
    
    # Write WAV file
    with wave.open(filename, 'wb') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 2 bytes per sample (16-bit)
        wav_file.setframerate(sample_rate)
        
        # Pack PCM data as bytes
        for sample in pcm_data:
            wav_file.writeframes(struct.pack('<h', sample))
    
    return filename

def create_text_to_speech_audio(text="Hello world", filename="hello_world_tts.wav"):
    """
    Create audio using text-to-speech if available
    """
    try:
        import pyttsx3
        
        # Initialize TTS engine
        engine = pyttsx3.init()
        
        # Set properties
        engine.setProperty('rate', 150)  # Speed of speech
        engine.setProperty('volume', 0.8)  # Volume level
        
        # Get available voices
        voices = engine.getProperty('voices')
        if voices:
            # Try to find an English voice
            for voice in voices:
                if 'english' in voice.name.lower() or 'en' in voice.id.lower():
                    engine.setProperty('voice', voice.id)
                    break
        
        # Save to file
        engine.save_to_file(text, filename)
        engine.runAndWait()
        
        print(f"✓ Created TTS audio file: {filename}")
        return filename
        
    except ImportError:
        print("pyttsx3 not available, using synthesized audio instead")
        return None
    except Exception as e:
        print(f"TTS failed: {e}, using synthesized audio instead")
        return None

def create_simple_pcm_audio(text="Hello world", filename="hello_world_pcm.wav"):
    """
    Create a simple PCM audio file for testing
    """
    sample_rate = 16000
    duration = len(text) * 0.2  # 0.2 seconds per character
    
    # Generate simple tone sequence
    audio_data = []
    num_samples = int(duration * sample_rate)
    
    for i in range(num_samples):
        t = i / sample_rate
        # Create a simple tone that varies with the text
        char_index = int(t / 0.2) % len(text)
        base_freq = 200 + ord(text[char_index]) * 2  # Frequency based on character
        
        # Add some variation
        freq = base_freq + 50 * math.sin(2 * math.pi * 10 * t)
        amplitude = 0.3 * (1 - t / duration)  # Fade out
        
        sample = amplitude * math.sin(2 * math.pi * freq * t)
        audio_data.append(sample)
    
    # Convert to 16-bit PCM and save
    with wave.open(filename, 'wb') as wav_file:
        wav_file.setnchannels(1)  # Mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        
        for sample in audio_data:
            pcm_sample = int(sample * 32767)
            pcm_sample = max(-32768, min(32767, pcm_sample))
            wav_file.writeframes(struct.pack('<h', pcm_sample))
    
    return filename

def main():
    print("Creating audio sample files for AWS Transcribe testing...")
    
    # Create different types of audio files
    files_created = []
    
    # 1. Try TTS first
    tts_file = create_text_to_speech_audio("Hello world", "hello_world_tts.wav")
    if tts_file and os.path.exists(tts_file):
        files_created.append(tts_file)
    
    # 2. Create synthesized audio
    synth_file = create_hello_world_audio("hello_world_synth.wav")
    files_created.append(synth_file)
    
    # 3. Create simple PCM audio
    pcm_file = create_simple_pcm_audio("Hello world", "hello_world_pcm.wav")
    files_created.append(pcm_file)
    
    print(f"\n✓ Created {len(files_created)} audio sample files:")
    for file in files_created:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  - {file} ({size} bytes)")
    
    print(f"\nYou can use these files to test AWS Transcribe integration.")
    print(f"The files are in WAV format with 16kHz sample rate, suitable for transcription.")
    
    return files_created

if __name__ == "__main__":
    main()
