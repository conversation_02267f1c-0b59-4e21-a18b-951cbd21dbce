"""
Base integration class for platform adapters
Provides common functionality for all platform integrations
"""

import asyncio
import json
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, Callable, List
from enum import Enum

from tutor.modules.websocket import (
    EventType, ConnectedEvent, StartEvent, MediaEvent, StopEvent,
    MarkEvent, ClearEvent, DTMFEvent, StreamManager, AudioCodec
)


class IntegrationStatus(Enum):
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ACTIVE = "active"
    ERROR = "error"


@dataclass
class IntegrationConfig:
    """Configuration for platform integration"""
    platform_name: str
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    endpoint_url: Optional[str] = None
    webhook_url: Optional[str] = None
    custom_parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_parameters is None:
            self.custom_parameters = {}


@dataclass
class CallMetadata:
    """Metadata for a call session"""
    call_id: str
    from_number: str
    to_number: str
    platform: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    status: str = "active"
    custom_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.custom_data is None:
            self.custom_data = {}


class BaseIntegration(ABC):
    """Base class for all platform integrations"""
    
    def __init__(self, config: IntegrationConfig, logger=None):
        self.config = config
        self.logger = logger
        self.status = IntegrationStatus.DISCONNECTED
        self.stream_manager = StreamManager()
        self.audio_codec = AudioCodec()
        
        # Callbacks
        self.on_call_started: Optional[Callable[[CallMetadata], None]] = None
        self.on_call_ended: Optional[Callable[[CallMetadata], None]] = None
        self.on_audio_received: Optional[Callable[[bytes, str], None]] = None
        self.on_error: Optional[Callable[[Exception], None]] = None
        
        # Active calls tracking
        self.active_calls: Dict[str, CallMetadata] = {}
        self.call_streams: Dict[str, str] = {}  # call_id -> stream_id
        
    def set_callbacks(self, 
                     on_call_started: Optional[Callable[[CallMetadata], None]] = None,
                     on_call_ended: Optional[Callable[[CallMetadata], None]] = None,
                     on_audio_received: Optional[Callable[[bytes, str], None]] = None,
                     on_error: Optional[Callable[[Exception], None]] = None):
        """Set callback functions for integration events"""
        self.on_call_started = on_call_started
        self.on_call_ended = on_call_ended
        self.on_audio_received = on_audio_received
        self.on_error = on_error
    
    @abstractmethod
    async def connect(self) -> bool:
        """Connect to the platform"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """Disconnect from the platform"""
        pass
    
    @abstractmethod
    async def start_call(self, to_number: str, from_number: str = None, **kwargs) -> str:
        """Start a new call and return call ID"""
        pass
    
    @abstractmethod
    async def end_call(self, call_id: str) -> None:
        """End an active call"""
        pass
    
    @abstractmethod
    async def send_audio(self, call_id: str, audio_data: bytes) -> None:
        """Send audio data for a call"""
        pass
    
    @abstractmethod
    async def send_dtmf(self, call_id: str, digits: str) -> None:
        """Send DTMF tones for a call"""
        pass
    
    def get_status(self) -> IntegrationStatus:
        """Get current integration status"""
        return self.status
    
    def get_active_calls(self) -> List[CallMetadata]:
        """Get list of active calls"""
        return list(self.active_calls.values())
    
    def get_call_metadata(self, call_id: str) -> Optional[CallMetadata]:
        """Get metadata for a specific call"""
        return self.active_calls.get(call_id)
    
    def create_protocol_stream(self, call_metadata: CallMetadata) -> str:
        """Create a protocol-compliant stream for a call"""
        stream_id = self.stream_manager.create_stream(
            from_number=call_metadata.from_number,
            to_number=call_metadata.to_number,
            custom_parameters={
                "platform": self.config.platform_name,
                "call_id": call_metadata.call_id,
                "integration_config": asdict(self.config)
            }
        )
        
        self.call_streams[call_metadata.call_id] = stream_id
        return stream_id
    
    def get_stream_id(self, call_id: str) -> Optional[str]:
        """Get stream ID for a call"""
        return self.call_streams.get(call_id)
    
    async def handle_incoming_call(self, call_data: Dict[str, Any]) -> str:
        """Handle an incoming call from the platform"""
        try:
            # Extract call information
            call_id = call_data.get("call_id") or call_data.get("CallSid") or str(time.time())
            from_number = call_data.get("from") or call_data.get("From") or "Unknown"
            to_number = call_data.get("to") or call_data.get("To") or "System"
            
            # Create call metadata
            call_metadata = CallMetadata(
                call_id=call_id,
                from_number=from_number,
                to_number=to_number,
                platform=self.config.platform_name,
                start_time=time.time(),
                custom_data=call_data
            )
            
            # Track the call
            self.active_calls[call_id] = call_metadata
            
            # Create protocol stream
            stream_id = self.create_protocol_stream(call_metadata)
            
            # Notify callback
            if self.on_call_started:
                self.on_call_started(call_metadata)
            
            if self.logger:
                self.logger.info(f"Incoming call started: {call_id} from {from_number}")
            
            return call_id
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling incoming call: {e}")
            if self.on_error:
                self.on_error(e)
            raise
    
    async def handle_call_ended(self, call_id: str, reason: str = "completed") -> None:
        """Handle a call that has ended"""
        try:
            call_metadata = self.active_calls.get(call_id)
            if not call_metadata:
                if self.logger:
                    self.logger.warning(f"Call {call_id} not found when trying to end")
                return
            
            # Update call metadata
            call_metadata.end_time = time.time()
            call_metadata.duration = call_metadata.end_time - call_metadata.start_time
            call_metadata.status = "ended"
            
            # Clean up stream
            stream_id = self.call_streams.get(call_id)
            if stream_id:
                self.stream_manager.end_stream(stream_id, reason)
                self.stream_manager.cleanup_stream(stream_id)
                del self.call_streams[call_id]
            
            # Remove from active calls
            del self.active_calls[call_id]
            
            # Notify callback
            if self.on_call_ended:
                self.on_call_ended(call_metadata)
            
            if self.logger:
                self.logger.info(f"Call ended: {call_id}, duration: {call_metadata.duration:.2f}s")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling call end: {e}")
            if self.on_error:
                self.on_error(e)
    
    async def handle_incoming_audio(self, call_id: str, audio_data: bytes, 
                                  audio_format: str = "mulaw") -> None:
        """Handle incoming audio data from the platform"""
        try:
            # Convert audio to protocol format if needed
            if audio_format.lower() != "mulaw":
                # Convert from other formats to mulaw
                if audio_format.lower() in ["pcm", "wav"]:
                    protocol_audio = self.audio_codec.convert_audio_to_protocol_format(audio_data)
                else:
                    if self.logger:
                        self.logger.warning(f"Unsupported audio format: {audio_format}")
                    return
            else:
                protocol_audio = audio_data
            
            # Notify callback
            if self.on_audio_received:
                self.on_audio_received(protocol_audio, call_id)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error handling incoming audio: {e}")
            if self.on_error:
                self.on_error(e)
    
    async def send_protocol_audio(self, call_id: str, audio_data: bytes) -> None:
        """Send protocol-compliant audio to the platform"""
        try:
            # This method should be overridden by specific integrations
            # to convert protocol audio to platform-specific format
            await self.send_audio(call_id, audio_data)
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Error sending protocol audio: {e}")
            if self.on_error:
                self.on_error(e)
    
    def get_platform_capabilities(self) -> Dict[str, bool]:
        """Get platform capabilities"""
        return {
            "audio_streaming": True,
            "dtmf_support": True,
            "call_recording": False,
            "video_support": False,
            "conference_calls": False,
            "call_transfer": False,
            "call_hold": False
        }
    
    def validate_config(self) -> List[str]:
        """Validate integration configuration"""
        errors = []
        
        if not self.config.platform_name:
            errors.append("Platform name is required")
        
        # Subclasses should override this method to add specific validation
        return errors
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on the integration"""
        return {
            "status": self.status.value,
            "platform": self.config.platform_name,
            "active_calls": len(self.active_calls),
            "timestamp": time.time(),
            "config_valid": len(self.validate_config()) == 0
        }
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}({self.config.platform_name})"
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(platform='{self.config.platform_name}', status={self.status.value})"