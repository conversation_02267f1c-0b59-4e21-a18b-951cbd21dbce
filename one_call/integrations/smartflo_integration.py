"""
Smartflo integration for One call
Supports Smartflo platform API and real-time communication
"""

import asyncio
import json
import time
import hmac
import hashlib
from typing import Dict, Any, Optional
import aiohttp
import websockets

from .base_integration import BaseIntegration, IntegrationConfig, IntegrationStatus, CallMetadata


class SmartfloIntegration(BaseIntegration):
    """Integration with Smartflo platform"""
    
    def __init__(self, config: IntegrationConfig, logger=None):
        super().__init__(config, logger)
        
        # Smartflo-specific configuration
        self.api_key = config.api_key
        self.api_secret = config.api_secret
        self.base_url = config.endpoint_url or "https://api.smartflo.com/v1"
        self.websocket_url = None
        self.websocket_connection = None
        
        # Session management
        self.session_token = None
        self.session_expires = 0
        
    def validate_config(self) -> list:
        """Validate Smartflo-specific configuration"""
        errors = super().validate_config()
        
        if not self.api_key:
            errors.append("Smartflo API Key is required")
        
        if not self.api_secret:
            errors.append("Smartflo API Secret is required")
        
        return errors
    
    def _generate_signature(self, data: str, timestamp: str) -> str:
        """Generate HMAC signature for Smartflo API"""
        message = f"{timestamp}{data}"
        signature = hmac.new(
            self.api_secret.encode('utf-8'),
            message.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        return signature
    
    async def _authenticate(self) -> bool:
        """Authenticate with Smartflo API"""
        try:\n            timestamp = str(int(time.time()))\n            auth_data = {\n                \"api_key\": self.api_key,\n                \"timestamp\": timestamp\n            }\n            \n            data_str = json.dumps(auth_data, sort_keys=True)\n            signature = self._generate_signature(data_str, timestamp)\n            \n            headers = {\n                \"Content-Type\": \"application/json\",\n                \"X-Signature\": signature,\n                \"X-Timestamp\": timestamp\n            }\n            \n            async with aiohttp.ClientSession() as session:\n                url = f\"{self.base_url}/auth/token\"\n                async with session.post(url, headers=headers, json=auth_data) as response:\n                    if response.status == 200:\n                        auth_response = await response.json()\n                        self.session_token = auth_response.get(\"token\")\n                        self.session_expires = time.time() + auth_response.get(\"expires_in\", 3600)\n                        return True\n                    else:\n                        error_data = await response.text()\n                        if self.logger:\n                            self.logger.error(f\"Smartflo authentication failed: {error_data}\")\n                        return False\n                        \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error authenticating with Smartflo: {e}\")\n            return False\n    \n    async def connect(self) -> bool:\n        \"\"\"Connect to Smartflo services\"\"\"\n        try:\n            self.status = IntegrationStatus.CONNECTING\n            \n            # Authenticate\n            if not await self._authenticate():\n                self.status = IntegrationStatus.ERROR\n                return False\n            \n            # Test API connection\n            headers = {\n                \"Authorization\": f\"Bearer {self.session_token}\",\n                \"Content-Type\": \"application/json\"\n            }\n            \n            async with aiohttp.ClientSession() as session:\n                url = f\"{self.base_url}/account/info\"\n                async with session.get(url, headers=headers) as response:\n                    if response.status == 200:\n                        account_info = await response.json()\n                        if self.logger:\n                            self.logger.info(f\"Connected to Smartflo account: {account_info.get('name')}\")\n                        \n                        self.status = IntegrationStatus.CONNECTED\n                        \n                        # Set up WebSocket connection\n                        await self._setup_websocket()\n                        \n                        return True\n                    else:\n                        error_msg = f\"Failed to connect to Smartflo: {response.status}\"\n                        if self.logger:\n                            self.logger.error(error_msg)\n                        self.status = IntegrationStatus.ERROR\n                        return False\n                        \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error connecting to Smartflo: {e}\")\n            if self.on_error:\n                self.on_error(e)\n            self.status = IntegrationStatus.ERROR\n            return False\n    \n    async def _setup_websocket(self) -> None:\n        \"\"\"Set up WebSocket connection for real-time events\"\"\"\n        try:\n            ws_url = self.base_url.replace(\"https://\", \"wss://\").replace(\"http://\", \"ws://\")\n            self.websocket_url = f\"{ws_url}/websocket\"\n            \n            headers = {\n                \"Authorization\": f\"Bearer {self.session_token}\"\n            }\n            \n            self.websocket_connection = await websockets.connect(\n                self.websocket_url,\n                extra_headers=headers\n            )\n            \n            # Start listening for events\n            asyncio.create_task(self._handle_websocket_events())\n            \n            if self.logger:\n                self.logger.info(\"Smartflo WebSocket connection established\")\n                \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error setting up Smartflo WebSocket: {e}\")\n    \n    async def _handle_websocket_events(self) -> None:\n        \"\"\"Handle incoming WebSocket events\"\"\"\n        try:\n            async for message in self.websocket_connection:\n                try:\n                    data = json.loads(message)\n                    event_type = data.get(\"type\")\n                    \n                    if event_type == \"call_incoming\":\n                        await self._handle_incoming_call_event(data)\n                    elif event_type == \"call_ended\":\n                        await self._handle_call_ended_event(data)\n                    elif event_type == \"audio_data\":\n                        await self._handle_audio_data_event(data)\n                    elif event_type == \"dtmf\":\n                        await self._handle_dtmf_event(data)\n                    \n                except json.JSONDecodeError as e:\n                    if self.logger:\n                        self.logger.error(f\"Invalid JSON in WebSocket message: {e}\")\n                        \n        except websockets.exceptions.ConnectionClosed:\n            if self.logger:\n                self.logger.info(\"Smartflo WebSocket connection closed\")\n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error handling WebSocket events: {e}\")\n    \n    async def _handle_incoming_call_event(self, data: Dict[str, Any]) -> None:\n        \"\"\"Handle incoming call event from Smartflo\"\"\"\n        try:\n            call_data = data.get(\"call\", {})\n            await self.handle_incoming_call(call_data)\n            \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error handling incoming call event: {e}\")\n    \n    async def _handle_call_ended_event(self, data: Dict[str, Any]) -> None:\n        \"\"\"Handle call ended event from Smartflo\"\"\"\n        try:\n            call_id = data.get(\"call_id\")\n            reason = data.get(\"reason\", \"completed\")\n            \n            if call_id:\n                await self.handle_call_ended(call_id, reason)\n                \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error handling call ended event: {e}\")\n    \n    async def _handle_audio_data_event(self, data: Dict[str, Any]) -> None:\n        \"\"\"Handle audio data event from Smartflo\"\"\"\n        try:\n            call_id = data.get(\"call_id\")\n            audio_format = data.get(\"format\", \"pcm\")\n            audio_data = data.get(\"data\", \"\")\n            \n            if call_id and audio_data:\n                # Decode audio data (assuming base64)\n                import base64\n                audio_bytes = base64.b64decode(audio_data)\n                await self.handle_incoming_audio(call_id, audio_bytes, audio_format)\n                \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error handling audio data event: {e}\")\n    \n    async def _handle_dtmf_event(self, data: Dict[str, Any]) -> None:\n        \"\"\"Handle DTMF event from Smartflo\"\"\"\n        try:\n            call_id = data.get(\"call_id\")\n            digit = data.get(\"digit\")\n            \n            if self.logger:\n                self.logger.info(f\"DTMF received for call {call_id}: {digit}\")\n            \n            # Process DTMF through protocol if needed\n            \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error handling DTMF event: {e}\")\n    \n    async def disconnect(self) -> None:\n        \"\"\"Disconnect from Smartflo services\"\"\"\n        try:\n            # Close WebSocket connection\n            if self.websocket_connection:\n                await self.websocket_connection.close()\n                self.websocket_connection = None\n            \n            # End all active calls\n            for call_id in list(self.active_calls.keys()):\n                await self.end_call(call_id)\n            \n            self.session_token = None\n            self.session_expires = 0\n            self.status = IntegrationStatus.DISCONNECTED\n            \n            if self.logger:\n                self.logger.info(\"Disconnected from Smartflo\")\n                \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error disconnecting from Smartflo: {e}\")\n    \n    async def start_call(self, to_number: str, from_number: str = None, **kwargs) -> str:\n        \"\"\"Start a new call using Smartflo API\"\"\"\n        try:\n            if not self.session_token or time.time() > self.session_expires:\n                await self._authenticate()\n            \n            headers = {\n                \"Authorization\": f\"Bearer {self.session_token}\",\n                \"Content-Type\": \"application/json\"\n            }\n            \n            call_data = {\n                \"to\": to_number,\n                \"from\": from_number or self.config.custom_parameters.get(\"default_from_number\"),\n                \"type\": \"voice\",\n                \"webhook_url\": self.config.webhook_url\n            }\n            \n            # Add custom parameters\n            if kwargs:\n                call_data.update(kwargs)\n            \n            async with aiohttp.ClientSession() as session:\n                url = f\"{self.base_url}/calls\"\n                async with session.post(url, headers=headers, json=call_data) as response:\n                    if response.status == 201:\n                        response_data = await response.json()\n                        call_id = response_data.get(\"call_id\")\n                        \n                        # Create call metadata\n                        call_metadata = CallMetadata(\n                            call_id=call_id,\n                            from_number=call_data[\"from\"],\n                            to_number=to_number,\n                            platform=\"smartflo\",\n                            start_time=time.time(),\n                            custom_data=response_data\n                        )\n                        \n                        self.active_calls[call_id] = call_metadata\n                        \n                        if self.logger:\n                            self.logger.info(f\"Started Smartflo call: {call_id}\")\n                        \n                        return call_id\n                    else:\n                        error_data = await response.json()\n                        error_msg = f\"Failed to start call: {error_data.get('message', 'Unknown error')}\"\n                        raise Exception(error_msg)\n                        \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error starting Smartflo call: {e}\")\n            if self.on_error:\n                self.on_error(e)\n            raise\n    \n    async def end_call(self, call_id: str) -> None:\n        \"\"\"End a Smartflo call\"\"\"\n        try:\n            if not self.session_token or time.time() > self.session_expires:\n                await self._authenticate()\n            \n            headers = {\n                \"Authorization\": f\"Bearer {self.session_token}\",\n                \"Content-Type\": \"application/json\"\n            }\n            \n            async with aiohttp.ClientSession() as session:\n                url = f\"{self.base_url}/calls/{call_id}/end\"\n                async with session.post(url, headers=headers) as response:\n                    if response.status == 200:\n                        await self.handle_call_ended(call_id, \"completed\")\n                        \n                        if self.logger:\n                            self.logger.info(f\"Ended Smartflo call: {call_id}\")\n                    else:\n                        error_msg = f\"Failed to end call {call_id}: {response.status}\"\n                        if self.logger:\n                            self.logger.warning(error_msg)\n                        \n                        # Still try to clean up locally\n                        await self.handle_call_ended(call_id, \"error\")\n                        \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error ending Smartflo call: {e}\")\n            # Clean up locally even if API call fails\n            await self.handle_call_ended(call_id, \"error\")\n    \n    async def send_audio(self, call_id: str, audio_data: bytes) -> None:\n        \"\"\"Send audio data to Smartflo call\"\"\"\n        try:\n            if not self.websocket_connection:\n                if self.logger:\n                    self.logger.warning(f\"No WebSocket connection for call {call_id}\")\n                return\n            \n            # Convert audio to base64 for transmission\n            import base64\n            audio_b64 = base64.b64encode(audio_data).decode('utf-8')\n            \n            message = {\n                \"type\": \"audio_data\",\n                \"call_id\": call_id,\n                \"format\": \"mulaw\",\n                \"data\": audio_b64\n            }\n            \n            await self.websocket_connection.send(json.dumps(message))\n            \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error sending audio to Smartflo: {e}\")\n            if self.on_error:\n                self.on_error(e)\n    \n    async def send_dtmf(self, call_id: str, digits: str) -> None:\n        \"\"\"Send DTMF digits to Smartflo call\"\"\"\n        try:\n            if not self.session_token or time.time() > self.session_expires:\n                await self._authenticate()\n            \n            headers = {\n                \"Authorization\": f\"Bearer {self.session_token}\",\n                \"Content-Type\": \"application/json\"\n            }\n            \n            dtmf_data = {\"digits\": digits}\n            \n            async with aiohttp.ClientSession() as session:\n                url = f\"{self.base_url}/calls/{call_id}/dtmf\"\n                async with session.post(url, headers=headers, json=dtmf_data) as response:\n                    if response.status == 200:\n                        if self.logger:\n                            self.logger.info(f\"Sent DTMF digits '{digits}' to call {call_id}\")\n                    else:\n                        error_msg = f\"Failed to send DTMF: {response.status}\"\n                        if self.logger:\n                            self.logger.error(error_msg)\n                        \n        except Exception as e:\n            if self.logger:\n                self.logger.error(f\"Error sending DTMF to Smartflo: {e}\")\n            if self.on_error:\n                self.on_error(e)\n    \n    def get_platform_capabilities(self) -> Dict[str, bool]:\n        \"\"\"Get Smartflo platform capabilities\"\"\"\n        return {\n            \"audio_streaming\": True,\n            \"dtmf_support\": True,\n            \"call_recording\": True,\n            \"video_support\": False,\n            \"conference_calls\": True,\n            \"call_transfer\": True,\n            \"call_hold\": True,\n            \"sms_support\": True,\n            \"webhook_support\": True,\n            \"real_time_events\": True\n        }\n    \n    async def health_check(self) -> Dict[str, Any]:\n        \"\"\"Perform Smartflo-specific health check\"\"\"\n        base_health = await super().health_check()\n        \n        # Add Smartflo-specific health info\n        base_health.update({\n            \"api_key\": self.api_key,\n            \"authenticated\": self.session_token is not None,\n            \"session_expires\": self.session_expires,\n            \"websocket_connected\": self.websocket_connection is not None,\n            \"api_accessible\": self.status in [IntegrationStatus.CONNECTED, IntegrationStatus.ACTIVE]\n        })\n        \n        return base_health