# main.py

import string
import struct
import time
import traceback
from datetime import datetime
from tutor.executors import (process_map, processor)
from tutor.modules.audio import listener
from tutor.modules.exceptions import StopSignal
from tutor.modules.logger import custom_handler, logger
from tutor.modules.models import models
from tutor.modules.utils import shared

class Activator:
    def start(self) -> None:
        try:
            while True:
                time.sleep(1)  # Simulating indefinite listening or waiting for activation
        except StopSignal:
            logger.info("StopSignal received, stopping Activator.")
        except Exception as e:
            logger.error(f"Unexpected error in Activator: {e}")
            logger.error(traceback.format_exc())
     

def start() -> None:
    """Starts main process to activate after checking internet connection and initiating background processes."""
    logger.info("Current Process ID: %d", models.settings.pid)
    
    try:
        shared.processes = processor.start_processes()
        if not shared.processes:
            logger.error("No processes started successfully.")
            return
    except Exception as e:
        logger.error(f"Failed to start processes: {e}")
        logger.error(traceback.format_exc())
        return
    
    logger.info("Background processes started successfully.")


if __name__ == '__main__':
    start()
    # Instantiate the Activator class and call the start method
    activator = Activator()
    activator.start()