#!/bin/bash

# ============================================================================
# One call - Complete Setup Script
# ============================================================================
# This script combines port checking, configuration updating, and service starting
# into a single, easy-to-use command.
#
# Usage:
#   ./setup.sh                 # Full setup and start
#   ./setup.sh --check-only    # Just check ports
#   ./setup.sh --force         # Start even if ports are in use
#   ./setup.sh --help          # Show help
# ============================================================================

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m'

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PORTS_CONFIG="$SCRIPT_DIR/ports.conf"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
PID_DIR="$SCRIPT_DIR/.pids"
LOG_DIR="$SCRIPT_DIR/logs"

# Default port values
API_PORT=1801
WEBSOCKET_PORT=1802
FRONTEND_PORT=1800
BACKEND_PROXY_PORT=1803

# Command line options
CHECK_ONLY=false
FORCE_START=false
SHOW_HELP=false

# ============================================================================
# Print Functions
# ============================================================================

print_header() {
    echo -e "\n${BLUE}${BOLD}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"
    echo -e "${BLUE}${BOLD}  🎯 One call Setup${NC}"
    echo -e "${BLUE}${BOLD}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}\n"
}

print_step() {
    echo -e "${CYAN}${BOLD}▶ $1${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

# ============================================================================
# Configuration Loading
# ============================================================================

load_port_config() {
    print_step "Loading port configuration..."
    
    if [[ -f "$PORTS_CONFIG" ]]; then
        while IFS='=' read -r key value || [[ -n "$key" ]]; do
            [[ $key =~ ^[[:space:]]*# ]] && continue
            [[ -z "$key" ]] && continue
            
            key=$(echo "$key" | xargs)
            value=$(echo "$value" | xargs)
            
            if [[ -n "$key" && -n "$value" ]]; then
                case "$key" in
                    "API_PORT") API_PORT="$value" ;;
                    "WEBSOCKET_PORT") WEBSOCKET_PORT="$value" ;;
                    "FRONTEND_PORT") FRONTEND_PORT="$value" ;;
                    "BACKEND_PROXY_PORT") BACKEND_PROXY_PORT="$value" ;;
                esac
            fi
        done < "$PORTS_CONFIG"
        
        print_success "Configuration loaded: Frontend=$FRONTEND_PORT, API=$API_PORT, WebSocket=$WEBSOCKET_PORT, Proxy=$BACKEND_PROXY_PORT"
    else
        print_warning "Configuration file not found, using defaults"
    fi
    
    # Environment variables override config file
    API_PORT=${API_PORT_ENV:-$API_PORT}
    WEBSOCKET_PORT=${WEBSOCKET_PORT_ENV:-$WEBSOCKET_PORT}
    FRONTEND_PORT=${FRONTEND_PORT_ENV:-$FRONTEND_PORT}
    BACKEND_PROXY_PORT=${BACKEND_PROXY_PORT_ENV:-$BACKEND_PROXY_PORT}
}

# ============================================================================
# Port Checking
# ============================================================================

check_port() {
    local port=$1
    local service_name=$2
    
    if command -v lsof &> /dev/null; then
        local pid=$(lsof -t -i:$port 2>/dev/null || true)
        if [[ -n "$pid" ]]; then
            local process_name=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
            echo -e "  ${RED}✗${NC} $service_name (port $port): ${RED}IN USE${NC} by PID $pid ($process_name)"
            return 1
        else
            echo -e "  ${GREEN}✓${NC} $service_name (port $port): ${GREEN}AVAILABLE${NC}"
            return 0
        fi
    else
        echo -e "  ${YELLOW}?${NC} $service_name (port $port): ${YELLOW}CANNOT CHECK${NC} (lsof not available)"
        return 0
    fi
}

check_all_ports() {
    print_step "Checking port availability..."
    
    local all_available=true
    
    if ! check_port $FRONTEND_PORT "Frontend Server"; then all_available=false; fi
    if ! check_port $API_PORT "Backend API"; then all_available=false; fi
    if ! check_port $WEBSOCKET_PORT "WebSocket Server"; then all_available=false; fi
    if ! check_port $BACKEND_PROXY_PORT "Backend Proxy"; then all_available=false; fi
    
    if $all_available; then
        print_success "All ports are available!"
        return 0
    else
        if [[ "$FORCE_START" == "true" ]]; then
            print_warning "Some ports are in use, but continuing due to --force flag"
            return 0
        else
            print_error "Some ports are already in use!"
            echo -e "\n${YELLOW}Solutions:${NC}"
            echo -e "  • Run with --force to start anyway"
            echo -e "  • Stop conflicting processes: ${CYAN}./stop.sh${NC}"
            echo -e "  • Change ports in: ${CYAN}$PORTS_CONFIG${NC}"
            return 1
        fi
    fi
}

# ============================================================================
# Host Detection for Production Deployment
# ============================================================================

detect_host() {
    local detected_host="localhost"
    
    # Check for explicitly set environment variables (highest priority)
    if [[ -n "$REACT_APP_HOST" ]]; then
        detected_host="$REACT_APP_HOST"
    elif [[ -n "$SERVER_HOST" ]]; then
        detected_host="$SERVER_HOST"
    elif [[ -n "$HOST" ]]; then
        detected_host="$HOST"
    # Check for production environment indicators
    elif [[ -n "$PRODUCTION" ]] || [[ -n "$NODE_ENV" && "$NODE_ENV" == "production" ]]; then
        # Try to detect actual hostname for production
        if command -v hostname &> /dev/null; then
            local system_hostname=$(hostname -f 2>/dev/null || hostname 2>/dev/null)
            if [[ -n "$system_hostname" && "$system_hostname" != "localhost" ]]; then
                detected_host="$system_hostname"
            fi
        fi
    fi
    
    echo "$detected_host"
}

# ============================================================================
# Frontend Configuration Update
# ============================================================================

update_frontend_config() {
    print_step "Updating frontend configuration..."
    
    local package_json="$FRONTEND_DIR/package.json"
    local frontend_env="$FRONTEND_DIR/.env"
    
    # Update package.json proxy setting
    if [[ -f "$package_json" ]]; then
        cp "$package_json" "$package_json.backup"
        sed -i.tmp "s|\"proxy\": \"http://localhost:[0-9]*\"|\"proxy\": \"http://localhost:$BACKEND_PROXY_PORT\"|g" "$package_json"
        rm -f "$package_json.tmp"
        print_success "Updated package.json proxy to http://localhost:$BACKEND_PROXY_PORT"
    else
        print_warning "package.json not found"
    fi
    
    # Create/update frontend .env file
    # Detect host for WebSocket URL - production vs development
    WS_HOST=$(detect_host)
    REACT_APP_WS_URL=ws://$WS_HOST:$WEBSOCKET_PORT
    
    # Display host detection result
    if [[ "$WS_HOST" != "localhost" ]]; then
        print_info "Using custom host: $WS_HOST"
    else
        print_info "Using localhost (development mode)"
    fi
    
    print_success "Generated WebSocket URL: $REACT_APP_WS_URL"
    cat > "$frontend_env" << EOF
# Port Configuration (Auto-generated by setup.sh)
REACT_APP_API_PORT=$API_PORT
REACT_APP_WEBSOCKET_PORT=$WEBSOCKET_PORT
REACT_APP_FRONTEND_PORT=$FRONTEND_PORT
REACT_APP_BACKEND_PROXY_PORT=$BACKEND_PROXY_PORT

# WebSocket Server Configuration
# Generated URL: $REACT_APP_WS_URL
# 
# For production deployment, set one of these environment variables:
# export REACT_APP_HOST=your-server-hostname.com
# export SERVER_HOST=your-server-hostname.com
# export HOST=your-server-hostname.com
REACT_APP_WS_URL=$REACT_APP_WS_URL

# Auto-connect to server on page load
REACT_APP_AUTO_CONNECT=true

# Audio Configuration
REACT_APP_SAMPLE_RATE=8000
REACT_APP_CHUNK_DURATION=20

# Development Settings
REACT_APP_DEBUG_AUDIO=false
REACT_APP_LOG_LEVEL=info
EOF
    
    print_success "Updated frontend .env file"
}

# ============================================================================
# Service Management
# ============================================================================

setup_directories() {
    mkdir -p "$PID_DIR" "$LOG_DIR"
}

check_dependencies() {
    print_step "Checking dependencies..."
    
    local missing_deps=()
    
    if ! command -v python3 &> /dev/null; then missing_deps+=(python3); fi
    if ! command -v node &> /dev/null; then missing_deps+=(node); fi
    if ! command -v npm &> /dev/null; then missing_deps+=(npm); fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        print_error "Missing dependencies: ${missing_deps[*]}"
        return 1
    fi
    
    if [[ ! -f "$SCRIPT_DIR/main.py" ]]; then
        print_error "Backend script not found: $SCRIPT_DIR/main.py"
        return 1
    fi
    
    if [[ ! -d "$FRONTEND_DIR" ]]; then
        print_error "Frontend directory not found: $FRONTEND_DIR"
        return 1
    fi
    
    if [[ ! -f "$SCRIPT_DIR/requirements.txt" ]]; then
        print_error "Requirements file not found: $SCRIPT_DIR/requirements.txt"
        return 1
    fi
    
    print_success "All dependencies found"
}

setup_python_venv() {
    print_step "Setting up Python virtual environment..."
    
    cd "$SCRIPT_DIR"
    
    # Create virtual environment if it doesn't exist
    if [[ ! -d ".venv" ]]; then
        print_info "Creating virtual environment..."
        if python3 -m venv .venv; then
            print_success "Virtual environment created"
        else
            print_error "Failed to create virtual environment"
            return 1
        fi
    else
        print_success "Virtual environment already exists"
    fi
    
    # Activate virtual environment and install requirements
    print_info "Installing Python requirements..."
    if source .venv/bin/activate && pip install -r requirements.txt > /dev/null 2>&1; then
        print_success "Python requirements installed successfully"
    else
        print_error "Failed to install Python requirements"
        return 1
    fi
    
    # Check if torch installation needs special handling
    if ! source .venv/bin/activate && python -c "import torch" &> /dev/null; then
        print_info "Installing PyTorch with specific version..."
        if source .venv/bin/activate && pip install torch~=2.3.1 --no-cache-dir > /dev/null 2>&1; then
            print_success "PyTorch installed successfully"
        else
            print_warning "PyTorch installation failed, continuing with existing setup"
        fi
    fi
    
    print_success "Python virtual environment setup complete"
}

install_frontend_deps() {
    print_step "Checking frontend dependencies..."
    
    cd "$FRONTEND_DIR"
    
    # Check for CSS loader cache corruption or missing dependencies
    local need_clean_install=false
    local cache_corrupted=false
    
    # Check if webpack cache exists and might be corrupted
    if [[ -d "node_modules/.cache" ]]; then
        # Try to detect cache corruption by checking for common error patterns
        if [[ -f "$LOG_DIR/frontend.log" ]]; then
            if grep -q "ENOENT.*\.cache.*\.pack" "$LOG_DIR/frontend.log" 2>/dev/null; then
                print_warning "Detected corrupted webpack cache in previous run"
                cache_corrupted=true
                need_clean_install=true
            fi
        fi
    fi
    
    # Standard dependency checks
    if [[ ! -d "node_modules" ]] || [[ ! -f "node_modules/.install-timestamp" ]] || [[ "package.json" -nt "node_modules/.install-timestamp" ]]; then
        need_clean_install=true
    fi
    
    if [[ "$need_clean_install" == "true" ]]; then
        if [[ "$cache_corrupted" == "true" ]]; then
            print_info "Performing clean reinstall due to cache corruption..."
            rm -rf node_modules package-lock.json
        else
            print_info "Installing frontend dependencies..."
        fi
        
        # Install dependencies with better error handling
        print_info "Running npm install..."
        if npm install 2>&1 | tee /tmp/npm-install.log; then
            # Validate installation by checking if critical dependencies exist
            if [[ -d "node_modules/react-scripts" ]] && [[ -d "node_modules/tailwindcss" ]]; then
                # Quick build test to ensure CSS processing works
                print_info "Validating CSS processing..."
                if timeout 30s npm run build > /tmp/npm-build-test.log 2>&1; then
                    touch "node_modules/.install-timestamp"
                    print_success "Frontend dependencies installed and validated"
                    rm -f /tmp/npm-install.log /tmp/npm-build-test.log
                else
                    print_warning "Build test failed, but continuing (check logs if issues persist)"
                    touch "node_modules/.install-timestamp"
                fi
            else
                print_error "Critical dependencies missing after installation"
                cat /tmp/npm-install.log
                cd "$SCRIPT_DIR"
                return 1
            fi
        else
            print_error "Failed to install frontend dependencies"
            cat /tmp/npm-install.log
            cd "$SCRIPT_DIR"
            return 1
        fi
    else
        print_success "Frontend dependencies are up to date"
    fi
    
    cd "$SCRIPT_DIR"
}

start_backend() {
    print_step "Starting backend services..."
    
    local backend_pid_file="$PID_DIR/backend.pid"
    local backend_log="$LOG_DIR/backend.log"
    
    # Check if already running
    if [[ -f "$backend_pid_file" ]]; then
        local pid=$(cat "$backend_pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            print_warning "Backend already running (PID: $pid)"
            return 0
        else
            rm -f "$backend_pid_file"
        fi
    fi
    
    # Start backend
    cd "$SCRIPT_DIR"
    
    if [[ -f ".venv/bin/activate" ]]; then
        nohup bash -c "source .venv/bin/activate && python3 main.py" > "$backend_log" 2>&1 &
    else
        nohup python3 main.py > "$backend_log" 2>&1 &
    fi
    
    local backend_pid=$!
    echo "$backend_pid" > "$backend_pid_file"
    
    # Wait and verify
    sleep 3
    if kill -0 "$backend_pid" 2>/dev/null; then
        print_success "Backend started (PID: $backend_pid)"
    else
        print_error "Backend failed to start. Check logs: $backend_log"
        rm -f "$backend_pid_file"
        return 1
    fi
}

start_frontend() {
    print_step "Starting frontend server..."
    
    local frontend_pid_file="$PID_DIR/frontend.pid"
    local frontend_log="$LOG_DIR/frontend.log"
    
    # Check if already running
    if [[ -f "$frontend_pid_file" ]]; then
        local pid=$(cat "$frontend_pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            print_warning "Frontend already running (PID: $pid)"
            return 0
        else
            rm -f "$frontend_pid_file"
        fi
    fi
    
    # Start frontend
    cd "$FRONTEND_DIR"
    
    # Set PORT environment variable for React
    export PORT=$FRONTEND_PORT
    
    if npm run start > "$frontend_log" 2>&1 & then
        local frontend_pid=$!
        echo "$frontend_pid" > "$frontend_pid_file"
        
        # Wait and verify
        sleep 4
        if kill -0 "$frontend_pid" 2>/dev/null; then
            print_success "Frontend started (PID: $frontend_pid)"
        else
            print_error "Frontend failed to start. Check logs: $frontend_log"
            rm -f "$frontend_pid_file"
            cd "$SCRIPT_DIR"
            return 1
        fi
    else
        print_error "Failed to start frontend"
        cd "$SCRIPT_DIR"
        return 1
    fi
    
    cd "$SCRIPT_DIR"
}

wait_for_services() {
    print_step "Waiting for services to be ready..."
    
    local max_attempts=15
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        local backend_ready=false
        local frontend_ready=false
        
        if command -v lsof &> /dev/null; then
            if lsof -i:$API_PORT &> /dev/null && lsof -i:$WEBSOCKET_PORT &> /dev/null; then
                backend_ready=true
            fi
            
            if lsof -i:$FRONTEND_PORT &> /dev/null; then
                frontend_ready=true
            fi
        else
            backend_ready=true
            frontend_ready=true
        fi
        
        if [[ "$backend_ready" == true ]] && [[ "$frontend_ready" == true ]]; then
            print_success "All services are ready!"
            return 0
        fi
        
        ((attempt++))
        echo -n "."
        sleep 2
    done
    
    echo ""
    print_warning "Services may still be starting up..."
}

# ============================================================================
# Status Display
# ============================================================================

show_final_status() {
    echo -e "\n${GREEN}${BOLD}🎉 One call Setup Complete!${NC}\n"
    
    echo -e "${BOLD}Access URLs:${NC}"
    echo -e "  🌐 Frontend:  ${CYAN}http://localhost:$FRONTEND_PORT${NC}"
    echo -e "  🔧 Backend:   ${CYAN}http://localhost:$API_PORT${NC}"
    echo -e "  🔌 WebSocket: ${CYAN}ws://localhost:$WEBSOCKET_PORT${NC}"
    
    echo -e "\n${BOLD}Management Commands:${NC}"
    echo -e "  🛑 Stop services:     ${CYAN}./stop.sh${NC}"
    echo -e "  📊 Check ports:       ${CYAN}./check-ports.sh${NC}"
    echo -e "  📝 View backend logs: ${CYAN}tail -f $LOG_DIR/backend.log${NC}"
    echo -e "  📝 View frontend logs:${CYAN}tail -f $LOG_DIR/frontend.log${NC}"
    
    echo -e "\n${GREEN}${BOLD}Ready to use! 🚀${NC}\n"
}

# ============================================================================
# Help Display
# ============================================================================

show_help() {
    echo -e "${BOLD}One call Setup Script${NC}"
    echo -e "Complete setup and start script for the One call application.\n"
    
    echo -e "${BOLD}Usage:${NC}"
    echo -e "  ./setup.sh [OPTIONS]\n"
    
    echo -e "${BOLD}Options:${NC}"
    echo -e "  ${CYAN}--check-only${NC}    Only check port availability, don't start services"
    echo -e "  ${CYAN}--force${NC}         Start services even if some ports are in use"
    echo -e "  ${CYAN}--help${NC}          Show this help message\n"
    
    echo -e "${BOLD}Examples:${NC}"
    echo -e "  ./setup.sh                    # Full setup and start"
    echo -e "  ./setup.sh --check-only      # Just check if ports are available"
    echo -e "  ./setup.sh --force           # Start even if ports are in use\n"
    
    echo -e "${BOLD}Configuration:${NC}"
    echo -e "  Edit ${CYAN}ports.conf${NC} to change default ports"
    echo -e "  Use environment variables (API_PORT, WEBSOCKET_PORT, etc.) to override\n"
}

# ============================================================================
# Main Execution
# ============================================================================

parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --check-only)
                CHECK_ONLY=true
                shift
                ;;
            --force)
                FORCE_START=true
                shift
                ;;
            --help|-h)
                SHOW_HELP=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                echo "Use --help for usage information"
                exit 1
                ;;
        esac
    done
}

main() {
    parse_args "$@"
    
    if [[ "$SHOW_HELP" == "true" ]]; then
        show_help
        exit 0
    fi
    
    print_header
    
    # Step 1: Load configuration
    load_port_config
    
    # Step 2: Check ports
    if ! check_all_ports; then
        exit 1
    fi
    
    if [[ "$CHECK_ONLY" == "true" ]]; then
        print_success "Port check complete - all ports are available!"
        exit 0
    fi
    
    # Step 3: Setup and dependencies
    setup_directories
    if ! check_dependencies; then exit 1; fi
    if ! setup_python_venv; then exit 1; fi
    if ! install_frontend_deps; then exit 1; fi
    
    # Step 4: Update frontend configuration
    update_frontend_config
    
    # Step 5: Start services
    if ! start_backend; then exit 1; fi
    if ! start_frontend; then exit 1; fi
    
    # Step 6: Wait for services
    wait_for_services
    
    # Step 7: Show final status
    show_final_status
}

# Trap for cleanup
trap 'print_error "Setup interrupted. You may need to run ./stop.sh to clean up."; exit 1' INT TERM

# Run main function
main "$@"