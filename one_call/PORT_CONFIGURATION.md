# Port Configuration Guide

This document explains how to configure the ports used by the One call application to avoid conflicts with other applications on your system.

## Overview

The One call application uses the following ports by default:

- **1800**: Frontend React development server
- **1801**: Backend API server (FastAPI/uvicorn)
- **1802**: WebSocket server for real-time communication
- **1803**: Backend proxy port (referenced in frontend configuration)

## Configuration Methods

### Method 1: Using ports.conf (Recommended)

Edit the `ports.conf` file in the root directory:

```bash
# One call Port Configuration
API_PORT=1801
WEBSOCKET_PORT=1802
FRONTEND_PORT=1800
BACKEND_PROXY_PORT=1803
```

### Method 2: Using Environment Variables

Set environment variables before starting the application:

```bash
export API_PORT=8801
export WEBSOCKET_PORT=8802
export FRONTEND_PORT=8800
export BACKEND_PROXY_PORT=8803
```

### Method 3: Using .env Files

#### Backend (.env in tutor/ directory):
```bash
API_PORT=1801
WEBSOCKET_PORT=1802
FRONTEND_PORT=1800
BACKEND_PROXY_PORT=1803
```

#### Frontend (.env in frontend/ directory):
```bash
REACT_APP_API_PORT=1801
REACT_APP_WEBSOCKET_PORT=1802
REACT_APP_FRONTEND_PORT=1800
REACT_APP_BACKEND_PROXY_PORT=1803
REACT_APP_WS_URL=ws://localhost:1802
```

## Updating Configuration

### Super Simple Setup (Recommended)

The easiest way is to use the complete setup script:

```bash
./setup.sh
```

This automatically:
- Loads your port configuration
- Checks port availability 
- Updates all configuration files
- Starts the services

### Manual Update

If you prefer manual control, run the update script:

```bash
./update-frontend-config.sh
```

### Manual Steps

1. Update `ports.conf` with your desired ports
2. Update backend `.env` file
3. Update frontend `.env` file
4. Run the update script or manually update `frontend/package.json` proxy setting
5. Restart both backend and frontend servers

## Port Conflict Resolution

### Checking for Port Conflicts

The start script automatically checks for port conflicts and will warn you if ports are already in use:

```bash
./start.sh
```

### Resolving Conflicts

If you encounter port conflicts:

1. **Check what's using the port:**
   ```bash
   lsof -i :PORT_NUMBER
   ```

2. **Choose alternative ports:**
   - Update `ports.conf` with available ports
   - Run `./update-frontend-config.sh`
   - Restart the application

3. **Kill conflicting processes (use with caution):**
   ```bash
   ./stop.sh  # This will clean up One call processes
   ```

## Development Server Ports

### Frontend Development Server

The React development server runs on the port specified by `FRONTEND_PORT`. To change it:

1. Update `FRONTEND_PORT` in `ports.conf`
2. Set `PORT` environment variable when starting React:
   ```bash
   cd frontend
   PORT=8800 npm start
   ```

### Backend Development

The backend services will automatically use the configured ports from the environment variables or configuration files.

## Production Deployment

For production deployment:

1. Set production-appropriate ports in your environment
2. Update your reverse proxy configuration (nginx, Apache, etc.)
3. Ensure firewall rules allow the configured ports
4. Update any load balancer or container orchestration configurations

## Troubleshooting

### Common Issues

1. **"Port already in use" errors:**
   - Check for existing processes: `lsof -i :PORT`
   - Choose different ports or stop conflicting services

2. **Frontend can't connect to backend:**
   - Verify WebSocket URL in frontend settings
   - Check CORS configuration if running on different hosts
   - Ensure backend is running on expected ports

3. **Configuration not taking effect:**
   - Restart all services after configuration changes
   - Clear browser cache for frontend changes
   - Verify environment variables are set correctly

### Validation Commands

Check if services are running on configured ports:

```bash
# Check API server
curl http://localhost:$API_PORT/health

# Check WebSocket server (if health endpoint exists)
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" -H "Sec-WebSocket-Version: 13" http://localhost:$WEBSOCKET_PORT/

# Check frontend
curl http://localhost:$FRONTEND_PORT
```

## Security Considerations

1. **Development vs. Production:**
   - Use different ports for development and production
   - Don't expose development ports to the internet

2. **Firewall Configuration:**
   - Only allow necessary ports through firewalls
   - Consider using non-standard ports for additional security

3. **Service Isolation:**
   - Run services with minimal privileges
   - Use containers or virtual environments when possible

## Integration with CI/CD

For automated deployments, you can:

1. Set environment variables in your CI/CD pipeline
2. Use configuration management tools (Ansible, Chef, etc.)
3. Generate configuration files from templates during deployment

Example GitHub Actions configuration:

```yaml
env:
  API_PORT: 4483
  WEBSOCKET_PORT: 5010
  FRONTEND_PORT: 3000
  BACKEND_PROXY_PORT: 5000
```

## Support

If you encounter issues with port configuration:

1. Check the application logs for port-related errors
2. Verify all configuration files are updated consistently
3. Test with default ports first, then apply custom configuration
4. Consider using port ranges that are less likely to conflict (e.g., 8000-8999)