#!/usr/bin/env python3
"""
Simple test for AWS Transcribe transcription functionality
"""

import sys
import os
import time
import threading
from unittest.mock import Mock

# Add the project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tutor'))

def test_direct_audio_processing():
    """Test audio processing directly"""
    print("Testing direct audio processing...")
    
    try:
        from tutor.modules.audio.transcription_factory import TranscriptionFactory
        
        # Create mock user and logger
        mock_user = <PERSON><PERSON>()
        mock_user.session = "direct_test"
        mock_user.mobile = "+1234567890"
        mock_user.loop = None
        mock_user.stream_id = None
        mock_user.bot_manager = Mock()
        mock_user.bot_manager.start_new_call.return_value = "Direct test"
        mock_user.sync_send = Mock()
        mock_user.end_ai_call = Mock()
        
        mock_logger = Mock()
        
        print("1. Creating AWS Transcribe service...")
        transcription_service = TranscriptionFactory.create_transcription_service(
            user=mock_user,
            logger_instance=mock_logger,
            service_type="aws_transcribe"
        )
        
        print("2. Connecting...")
        transcription_service.connect()
        
        # Capture transcripts
        transcripts = []
        def capture_transcript(transcript):
            transcripts.append(transcript)
            print(f"   📝 Transcript: '{transcript.text}' (Final: {getattr(transcript, 'is_final', False)})")
        
        # Override callback
        transcription_service.on_data_callback = capture_transcript
        
        print("3. Testing direct audio processing...")
        
        # Add audio chunks and process them directly
        for i in range(10):
            audio_chunk = f"test_audio_chunk_{i}".encode() * 50
            transcription_service.add_audio_chunk(audio_chunk)
            
            # Process the chunk directly
            transcription_service._process_audio_chunk(audio_chunk)
            
            print(f"   Processed chunk {i+1}/10")
            time.sleep(0.1)
        
        print(f"\n📊 Results:")
        print(f"   Total transcripts: {len(transcripts)}")
        
        if len(transcripts) > 0:
            print("✓ Direct audio processing working")
            return True
        else:
            print("✗ No transcripts received")
            return False
            
    except Exception as e:
        print(f"✗ Direct audio processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streaming_with_thread():
    """Test streaming with proper threading"""
    print("\nTesting streaming with threading...")
    
    try:
        from tutor.modules.audio.transcription_factory import TranscriptionFactory
        
        mock_user = Mock()
        mock_user.session = "thread_test"
        mock_user.mobile = "+1234567890"
        mock_user.loop = None
        mock_user.stream_id = None
        mock_user.bot_manager = Mock()
        mock_user.bot_manager.start_new_call.return_value = "Thread test"
        mock_user.sync_send = Mock()
        mock_user.end_ai_call = Mock()
        
        mock_logger = Mock()
        
        transcription_service = TranscriptionFactory.create_transcription_service(
            user=mock_user,
            logger_instance=mock_logger,
            service_type="aws_transcribe"
        )
        
        transcription_service.connect()
        
        # Capture transcripts
        transcripts = []
        def capture_transcript(transcript):
            transcripts.append(transcript)
            print(f"   📝 Transcript: '{transcript.text}' (Final: {getattr(transcript, 'is_final', False)})")
        
        transcription_service.on_data_callback = capture_transcript
        
        print("1. Starting transcription service...")
        
        # Start the service in a thread
        def run_service():
            try:
                transcription_service.start()
            except Exception as e:
                print(f"Service error: {e}")
        
        service_thread = threading.Thread(target=run_service, daemon=True)
        service_thread.start()
        
        # Give it a moment to start
        time.sleep(0.5)
        
        print("2. Adding audio chunks...")
        # Add audio chunks
        for i in range(15):
            audio_chunk = f"streaming_audio_{i}".encode() * 100
            transcription_service.add_audio_chunk(audio_chunk)
            time.sleep(0.2)
            
            if i % 5 == 0:
                print(f"   Added {i+1}/15 chunks...")
        
        print("3. Waiting for processing...")
        time.sleep(3)
        
        print("4. Stopping service...")
        transcription_service.close()
        
        print(f"\n📊 Results:")
        print(f"   Total transcripts: {len(transcripts)}")
        
        if len(transcripts) > 0:
            print("✓ Streaming with threading working")
            return True
        else:
            print("✗ No transcripts received")
            return False
            
    except Exception as e:
        print(f"✗ Streaming test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("Simple AWS Transcribe Test")
    print("=" * 40)
    
    test1 = test_direct_audio_processing()
    test2 = test_streaming_with_thread()
    
    print("\n" + "=" * 40)
    print("Results:")
    print(f"Direct processing: {'PASS' if test1 else 'FAIL'}")
    print(f"Streaming: {'PASS' if test2 else 'FAIL'}")
    
    if test1 and test2:
        print("\n🎉 AWS Transcribe is working correctly!")
        return True
    else:
        print("\n❌ Some issues remain")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
