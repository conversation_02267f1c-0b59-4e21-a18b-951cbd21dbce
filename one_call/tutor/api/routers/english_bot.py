from datetime import datetime
import glob
import os
import secrets
import time
from http import HTT<PERSON><PERSON>us
from threading import Thread
from typing import Union, List, Dict, Optional
from json.decoder import <PERSON><PERSON><PERSON>ecodeError

from fastapi import File, Form, UploadFile
from fastapi.exceptions import HTTPException
from fastapi.requests import Request
from fastapi.routing import APIRouter
from pydub import AudioSegment

from tutor.api.logger import logger
# from tutor.modules.englishbot.bot import get_response_from_english_ai
from tutor.modules.models import models
from tutor.modules.englishbot import bot
from fastapi.responses import FileResponse, JSONResponse
# from tutor.modules.audio import tts_stt
from tutor.modules.exceptions import APIResponse, InvalidArgument
from tutor.modules.models import models
from tutor.modules.utils import support
from tutor.modules.audio import text_speech
from tutor.modules.database import database
from tutor.modules.transcription.hinglish_transcriber import HinglishTranscriber
import os
import time
import asyncio
from threading import Thread
from fastapi import APIRouter, File, Form, UploadFile
from fastapi.responses import J<PERSON>NResponse
from pathlib import Path

router = APIRouter()

# dev- Mayur

async def process_speech_response(text: str, media_type: str) -> Union[bytes, FileResponse]:
    try:
        if models.env.speech_synthesis_timeout:
            # Attempt speech synthesis with error handling and logging
            filename = models.fileio.speech_synthesis_wav.replace("speech_synthesis.wav", f"{int(time.time())}.wav")
            if await text_speech.speech_synthesizer(text=text, filename=filename):
                if os.path.isfile(filename):
                    logger.info("Storing response as %s in native audio.", filename)
                    Thread(target=support.remove_file, kwargs={'delay': 15, 'filepath': filename},
                           daemon=True).start()
                    return FileResponse(path=filename, media_type=media_type,
                                        filename="synthesized.wav", status_code=HTTPStatus.OK.real)
            else:
                native_audio_wav = None # tts_stt.text_to_audio(text=text)
                if native_audio_wav:
                    logger.info("Storing response as %s in native audio.", native_audio_wav)
                    Thread(target=support.remove_file, kwargs={'delay': 15, 'filepath': native_audio_wav},
                           daemon=True).start()
                    return FileResponse(path=native_audio_wav, media_type=media_type,
                                        filename="synthesized.wav", status_code=HTTPStatus.OK.real)
                else:
                    logger.error("Failed to generate audio file in native voice.")
                    raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.real,
                                        detail="Failed to generate audio file")

    except Exception as e:
        logger.error("Error occurred during audio generation: %s", str(e))
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.real,
                            detail="Error occurred during audio generation")


async def process_ok_response(response: str) -> Union[bytes, FileResponse]:
    try:
        native_audio_wav = None # tts_stt.text_to_audio(text=response)
        if native_audio_wav:
            logger.info("Storing response as %s in native audio.", native_audio_wav)
            Thread(target=support.remove_file, kwargs={'delay': 15, 'filepath': native_audio_wav}, daemon=True).start()
            return FileResponse(path=native_audio_wav, media_type='application/octet-stream',
                                filename="synthesized.wav", status_code=HTTPStatus.OK.real)
        else:
            logger.error("Failed to generate audio file in native voice.")
            raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.real,
                                detail="Failed to generate audio file")
    except Exception as e:
        logger.error("Error occurred during audio generation: %s", str(e))
        raise HTTPException(status_code=HTTPStatus.INTERNAL_SERVER_ERROR.real,
                            detail="Error occurred during audio generation")


def two_factor(request: Request) -> bool:
    """Two factor verification for messages coming via webhook.

    Args:
        request: Request object from FastAPI.

    Returns:
        bool:
        Flag to indicate the calling function if the auth was successful.
    """
    if models.env.bot_secret:
        # if secrets.compare_digest(request.headers.get('X-Telegram-Bot-Api-Secret-Token', ''), models.env.bot_secret):
        return True
    else:
        logger.warning("Use the env var bot_secret to secure the webhook interaction")
        return True


@router.post(path=models.env.english_bot_endpoint)
async def english_bot_webhook(request: Request):
    """Invoked when a new message is received from Telegram API.

    Args:
        request: Request instance.

    Raises:

        HTTPException:
            - 406: If the request payload is not JSON format-able.
    """
    logger.debug("Connection received from %s via %s", request.client.host, request.headers.get('host'))
    try:
        response = await request.json()
    except JSONDecodeError as error:
        logger.error(error)
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST.real, detail=HTTPStatus.BAD_REQUEST.phrase)
    # Ensure only the owner who set the webhook can interact with the Bot
    if not two_factor(request):
        logger.error("Request received from a non-webhook source")
        logger.error(response)
        raise HTTPException(status_code=HTTPStatus.FORBIDDEN.real, detail=HTTPStatus.FORBIDDEN.phrase)
    if payload := response.get('message'):
        logger.debug(response)
        bot.process_request(payload)
    else:
        raise HTTPException(status_code=HTTPStatus.UNPROCESSABLE_ENTITY.real,
                            detail=HTTPStatus.UNPROCESSABLE_ENTITY.phrase)


@router.post(path="/get-text-audio-file")
async def get_text_audio_file(request: Request):
    try:
        response = await request.json()
    except JSONDecodeError as error:
        logger.error(error)
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST.real, detail=HTTPStatus.BAD_REQUEST.phrase)

    if message := response.get('message'):
        logger.debug(response)
        return await process_speech_response(text=message, media_type="application/octet-stream")
    else:
        raise HTTPException(status_code=HTTPStatus.UNPROCESSABLE_ENTITY.real,
                            detail=HTTPStatus.UNPROCESSABLE_ENTITY.phrase)

@router.post(path="/api/tts")
async def tts(request: Request):
    try:
        response = await request.json()
    except JSONDecodeError as error:
        logger.error(error)
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST.real, detail=HTTPStatus.BAD_REQUEST.phrase)

    if message := response.get('message'):
        logger.debug(response)
        return await process_speech_response(text=message, media_type="audio/wav")
    else:
        raise HTTPException(status_code=HTTPStatus.UNPROCESSABLE_ENTITY.real,
                            detail=HTTPStatus.UNPROCESSABLE_ENTITY.phrase)
    
@router.post(path="/get-text-audio-ai-response-file")
async def get_text_audio_ai_response_file(request: Request):
    try:
        response = await request.json()
    except JSONDecodeError as error:
        logger.error(error)
        raise HTTPException(status_code=HTTPStatus.BAD_REQUEST.real, detail=HTTPStatus.BAD_REQUEST.phrase)

    if message := response.get('message'):
        logger.debug(response)
        response =None # get_response_from_english_ai(message)
        #TODO update audio file docker is stop or not running
        return await process_speech_response(text=response)
    else:
        raise HTTPException(status_code=HTTPStatus.UNPROCESSABLE_ENTITY.real,
                            detail=HTTPStatus.UNPROCESSABLE_ENTITY.phrase)

@router.post("/api/qa/update-audio-file")
async def qa_update_audio_file(
    call_id: str = Form(...),
    audio_file: UploadFile = File(...),
    overwrite: bool = Form(False),
):
    """
    Update the audio file for a specific call and process it for transcription.
    
    Args:
        call_id: Unique identifier for the call
        audio_file: The audio file to upload
        overwrite: Whether to overwrite existing file if it exists
    
    Returns:
        JSONResponse with status of the operation
    """
    try:
        # Validate file type
        if not audio_file.content_type.startswith('audio/'):
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "Uploaded file must be an audio file"}
            )
        
        # Create directory structure that matches the expected format in transcription.py
        # Expected path format: sessions/{mobile}/{session_id}/call_recording.wav
        mobile = "api_upload"  # Default mobile number for API uploads
        sessions_dir = os.path.join(models.fileio.audio_storage, mobile, call_id)
        os.makedirs(sessions_dir, exist_ok=True)
        
        # Save file with the expected name
        filename = os.path.join(sessions_dir, "call_recording.wav")
        
        # Check if file already exists
        if os.path.exists(filename) and not overwrite:
            return JSONResponse(
                status_code=409,
                content={
                    "status": "error", 
                    "message": "Audio file already exists for this call_id. Use overwrite=true to replace it.",
                    "file_path": filename
                }
            )
        
        # Save the uploaded file
        contents = await audio_file.read()
        with open(filename, "wb") as f:
            f.write(contents)
        
        logger.info(f"Saved audio file for call_id: {call_id} to {filename}")
        
        # The transcription service will automatically pick up this file
        # since it scans the sessions directory for new audio files
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "success", 
                "message": "Audio file uploaded successfully. It will be processed by the transcription service.", 
                "file_path": filename,
                "call_id": call_id
            }
        )
        
    except Exception as e:
        logger.error(f"Error updating audio file: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"Failed to update audio file: {str(e)}"}
        )

@router.get("/api/qa-results")
async def get_qa_results(
    call_id: Optional[str] = None,
    report_id: Optional[str] = None,
    limit: int = 10,
    offset: int = 0,
    include_parameter_details: bool = False,
    include_transcript: bool = False,
    include_audio: bool = False
):
    """
    Retrieve QA results from the database with detailed parameter information.
    
    Args:
        call_id: Optional filter by call_id
        report_id: Optional filter by specific QA report ID
        limit: Maximum number of results to return
        offset: Number of results to skip (for pagination)
        include_parameter_details: Whether to include full parameter definitions
        include_transcript: Whether to include the full transcript text and utterances
        include_audio: Whether to include audio file URLs for playback
    
    Returns:
        List of QA reports with their parameter scores, definitions, and optionally transcript and audio data
    """
    try:
        db = database.Database(database=models.fileio.base_db)
        
        # Import QA parameter definitions
        from tutor.modules.assistants.qa_assistant import QAAssistant
        
        # Build the query based on provided filters
        query_conditions = []
        query_params = []
        
        base_query = """
            SELECT r.report_id, r.call_summary, r.total_score, r.call_category, 
                   r.timestamp, r.agent_strengths, r.agent_areas_for_improvement,
                   t.call_id, t.full_transcript, t.utterances, t.audio_file_path
            FROM QAReport r
            LEFT JOIN Transcripts t ON r.report_id = t.qa_report_id
            WHERE 1=1
        """
        
        if call_id:
            query_conditions.append("t.call_id = ?")
            query_params.append(call_id)
            
        if report_id:
            query_conditions.append("r.report_id = ?")
            query_params.append(report_id)
            
        if query_conditions:
            condition_str = " AND " + " AND ".join(query_conditions)
            base_query += condition_str
            
        # Add pagination
        base_query += " ORDER BY r.timestamp DESC LIMIT ? OFFSET ?"
        query_params.extend([limit, offset])
        
        # Execute the query
        with db.connection:
            cursor = db.connection.cursor()
            cursor.execute(base_query, query_params)
            reports = cursor.fetchall()
            
            # Convert to list of dictionaries
            result = []
            for report in reports:
                report_dict = {
                    "report_id": report[0],
                    "call_summary": report[1],
                    "total_score": report[2],
                    "call_category": report[3],
                    "timestamp": report[4],
                    "agent_strengths": report[5].split(", ") if report[5] else [],
                    "agent_areas_for_improvement": report[6].split(", ") if report[6] else [],
                    "call_id": report[7]
                }
                
                # Add transcript data if requested
                if include_transcript and report[8]:  # full_transcript
                    report_dict["transcript"] = {
                        "full_text": report[8]
                    }
                    
                    # Parse utterances if available
                    if report[9]:  # utterances
                        try:
                            import json
                            utterances = json.loads(report[9])
                            report_dict["transcript"]["utterances"] = utterances
                        except (json.JSONDecodeError, TypeError) as e:
                            logger.warning(f"Failed to parse utterances for report {report[0]}: {str(e)}")
                            report_dict["transcript"]["utterances"] = []
                
                # Add audio file information if requested
                if include_audio and report[7]:  # call_id
                    audio_file_path = report[10] if len(report) > 10 and report[10] else None
                    
                    # If audio_file_path is stored in the database, use it
                    if audio_file_path and os.path.exists(audio_file_path):
                        # Generate a URL for the audio file
                        audio_filename = os.path.basename(audio_file_path)
                        audio_url = f"/api/audio/{report[7]}/{audio_filename}"
                    else:
                        # Try to find the audio file based on the call_id
                        mobile = "api_upload"  # Default mobile number for API uploads
                        possible_paths = [
                            os.path.join(models.fileio.audio_storage, mobile, report[7], "call_recording.wav"),
                            os.path.join(models.fileio.audio_storage, "*", report[7], "call_recording.wav"),
                            os.path.join(models.fileio.sessions, "*", report[7], "call_recording.wav")
                        ]
                        
                        audio_path = None
                        for path_pattern in possible_paths:
                            matching_files = glob.glob(path_pattern)
                            if matching_files:
                                audio_path = matching_files[0]
                                break
                        
                        if audio_path:
                            audio_filename = os.path.basename(audio_path)
                            audio_url = f"/api/audio/{report[7]}/{audio_filename}"
                        else:
                            audio_url = None
                    
                    if audio_url:
                        report_dict["audio"] = {
                            "url": audio_url,
                            "file_path": audio_file_path or audio_path,
                            "duration": None  # Could be added if needed
                        }
                
                # Get parameter scores for this report
                cursor.execute(
                    """
                    SELECT parameter_id, score, comments 
                    FROM QAParameterScore 
                    WHERE report_id = ?
                    """, 
                    (report[0],)
                )
                parameter_scores = cursor.fetchall()
                
                # Create parameter scores with detailed information
                report_dict["parameter_scores"] = []
                
                # Create a lookup dictionary for parameters by ID
                parameter_lookup = {param.id: param for param in QAAssistant.parameters}
                
                for score in parameter_scores:
                    param_id = int(score[0])
                    score_dict = {
                        "parameter_id": param_id,
                        "score": score[1],
                        "comments": score[2]
                    }
                    
                    # Add parameter details if requested
                    if include_parameter_details and param_id in parameter_lookup:
                        param = parameter_lookup[param_id]
                        score_dict["parameter_details"] = {
                            "name": param.name,
                            "max_score": param.max_score,
                            "parent_id": param.parent_id
                        }
                        
                        # Add parent parameter name if applicable
                        if param.parent_id and param.parent_id in parameter_lookup:
                            score_dict["parameter_details"]["parent_name"] = parameter_lookup[param.parent_id].name
                    
                    report_dict["parameter_scores"].append(score_dict)
                
                result.append(report_dict)
            
            # Add parameter definitions to the response if requested
            response_data = {"data": result, "count": len(result)}
            
            if include_parameter_details:
                # Create a hierarchical structure of parameters
                parameter_definitions = []
                
                # First, add all main categories
                for param in QAAssistant.parameters:
                    if param.parent_id is None:
                        param_dict = {
                            "id": param.id,
                            "name": param.name,
                            "max_score": param.max_score,
                            "sub_parameters": []
                        }
                        parameter_definitions.append(param_dict)
                
                # Then, add sub-parameters to their respective main categories
                for main_param in parameter_definitions:
                    for param in QAAssistant.parameters:
                        if param.parent_id == main_param["id"]:
                            main_param["sub_parameters"].append({
                                "id": param.id,
                                "name": param.name,
                                "max_score": param.max_score
                            })
                
                response_data["parameter_definitions"] = parameter_definitions
        
        return JSONResponse(
            status_code=200,
            content={"status": "success", **response_data}
        )
        
    except Exception as e:
        logger.error(f"Error retrieving QA results: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"Failed to retrieve QA results: {str(e)}"}
        )

@router.get("/api/audio/{call_id}/{filename}")
async def get_audio_file(call_id: str, filename: str):
    """
    Serve audio files for playback.
    
    Args:
        call_id: The call ID associated with the audio file
        filename: The name of the audio file
    
    Returns:
        The audio file as a streaming response
    """
    try:
        # Check for the file in various possible locations
        mobile = "api_upload"  # Default mobile number for API uploads
        possible_paths = [
            os.path.join(models.fileio.audio_storage, mobile, call_id, filename),
            os.path.join(models.fileio.audio_storage, "*", call_id, filename),
            os.path.join(models.fileio.sessions, "*", call_id, filename)
        ]
        
        audio_path = None
        for path_pattern in possible_paths:
            matching_files = glob.glob(path_pattern)
            if matching_files:
                audio_path = matching_files[0]
                break
        
        if not audio_path or not os.path.exists(audio_path):
            logger.error(f"Audio file not found for call_id: {call_id}, filename: {filename}")
            return JSONResponse(
                status_code=404,
                content={"status": "error", "message": "Audio file not found"}
            )
        
        # Determine the media type based on file extension
        media_type = "audio/wav"
        if filename.endswith(".mp3"):
            media_type = "audio/mpeg"
        elif filename.endswith(".ogg"):
            media_type = "audio/ogg"
        
        # Return the audio file
        return FileResponse(
            path=audio_path,
            media_type=media_type,
            filename=filename
        )
        
    except Exception as e:
        logger.error(f"Error serving audio file: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"status": "error", "message": f"Failed to serve audio file: {str(e)}"}
        )
