import uuid


class Vehicle:
    def __init__(self, number, id):
        self.number = number
        self.id = id


class UserInfo:
    def __init__(self, name, phone, id, vehicles):
        self.name = name
        self.phone = phone
        self.id = id
        self.vehicles = vehicles


class User:
    user_info_list = []

    @staticmethod
    def set_user_info(user_info):
        User.user_info_list.append(user_info)


def initialize_user_data():
    vehicles = []
    vehicles.append(Vehicle("MH00888888", "3"))
    vehicles.append(Vehicle("VB23332222", "17"))
    vehicles.append(Vehicle("MH09F787878", "96"))
    vehicles.append(Vehicle("MH09FF1810", "173"))
    vehicles.append(Vehicle("23BH8890A", "2110"))
    User.set_user_info(UserInfo("Ashish", "9595036832", "12020", vehicles))

    vehicles = []
    vehicles.append(Vehicle("MP041234TH", "27"))
    vehicles.append(Vehicle("KA51MC4084", "21"))
    vehicles.append(Vehicle("VSGGDHDH897", "25"))
    User.set_user_info(UserInfo("Nikesh", "9920399749", "12005", vehicles))

    vehicles = []
    vehicles.append(Vehicle("AB1234", "1"))
    User.set_user_info(UserInfo("Mayur", "7038360800", "0", vehicles))


def convert_to_specified_format():
    vehicle_number_list = []
    vehicle_id_map = {}

    for user_info in User.user_info_list:
        for vehicle in user_info.vehicles:
            number_lower = vehicle.number.lower()
            vehicle_number_list.append(number_lower)
            vehicle_id_map[number_lower] = int(vehicle.id)

    vehicle_numbers = {"vehicles": vehicle_number_list}

    return vehicle_numbers, vehicle_number_list, vehicle_id_map


# Initialize user data
initialize_user_data()

# Convert to specified format
vehicle_numbers, vehicle_number_list, vehicle_id_map = convert_to_specified_format()

# Output the results
print(vehicle_numbers)
print(vehicle_number_list)
print(vehicle_id_map)
