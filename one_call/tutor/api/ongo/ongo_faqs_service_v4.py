import time
from typing import Union

import requests
import json


class ONGOData:
    def __init__(self, accuracy, answer, query, response_time, source):
        self.accuracy = accuracy
        self.answer: str = answer
        self.query = query
        self.response_time = response_time
        self.source = source

    def is_source_ongo_faqs(self, match: str) -> bool:
        return match in self.source  # "./data/ongo_faqs.csv"

    @classmethod
    def from_dict(cls, data):
        return cls(
            accuracy=data["accuracy"],
            answer=data["answer"],
            query=data["query"],
            response_time=data["response_time"],
            source=data["sources"]["source"]
        )

    def parse_response(self):
        """
        Parse and format the instance for readability.

        :return: A formatted string representation of the instance.
        """
        formatted_response = (
            f"Query: {self.query}\n"
            f"Answer: {self.answer}\n"
            f"Response Time: {self.response_time} seconds\n"
            f"Sources: Row {self.source}"
        )
        return formatted_response


class OngoFaqsService:
    def __init__(self):
        """
        Initialize the OngoFaqsService with the base URL for the API.

        """
        self.base_url = "http://localhost:3020"
        self.session = requests.Session()

    def get_best_match(self, query) -> Union[ONGOData, None]:
        """
        Send a POST request to the best_match endpoint with the provided query.

        :param query: The query string to send to the API.
        :return: The response text from the API.
        """
        url = f"{self.base_url}/best_match"
        payload = self._create_payload(query)
        headers = self._create_headers()

        try:
            start_time = time.time()
            response = self.session.post(url, headers=headers, data=payload)
            response.raise_for_status()  # Raise an error for bad status codes
            end_time = time.time()

            response_time = end_time - start_time
            print(f"Response time for get_best_match: {response_time:.2f} seconds")

            response_data = response.json()
            return ONGOData.from_dict(response_data)
        except (requests.RequestException, json.JSONDecodeError, KeyError) as e:
            print(f"Error during request: {e}")
            return None

    @staticmethod
    def _create_payload(query):
        """
        Create the JSON payload for the request.

        :param query: The query string to include in the payload.
        :return: The JSON payload as a string.
        """
        return json.dumps({"query": query})

    @staticmethod
    def _create_headers():
        """
        Create the headers for the request.

        :return: A dictionary containing the headers.
        """
        return {'Content-Type': 'application/json'}


# Example usage
if __name__ == "__main__":
    service = OngoFaqsService()
    response = service.get_best_match("How can I replace my lost ONGO card?")
    formatted_response = response.parse_response()
    print(formatted_response)
