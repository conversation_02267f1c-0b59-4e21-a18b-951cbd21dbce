import requests
from modules.logger import logger

def call_preset_vehicle_api(result_data):
    base_url = "http://localhost:5001/preset-vehicle"  # Adjust the base URL as needed
    params = {
        'vehicle_number': result_data['vehicle_number'],
        'preset_amount': result_data['preset_amount'],
        'mobile_number': result_data['mobile_number'],
        'user_id': result_data['user_id'],
        'vehicle_id': result_data['vehicle_id']
    }

    response = requests.get(base_url, params=params)

    return response.json(), response.status_code


def get_response(result):
    try:
        response_data, status_code = call_preset_vehicle_api(result)
        # print(f"Response Status Code: {status_code}")
        # print(f"Response Data: {response_data}")
        if status_code == 200:
            return response_data.get('message_to_user')
        else:
            error_message = response_data.get('message_to_user',
                                              "error: Please try again later. Thank you for using Ongo Service")
            return error_message

    except requests.RequestException as e:
        # print(f"An error occurred while processing your request: {e}")
        logger.error(f"An error occurred while processing your request: {e}")
        return "error: Please try again later. Thank you for using Ongo Service"


"""
result = {'vehicle_number': 'ABC123', 'preset_amount': '50000'}
print("Result Dictionary:", result)
print("Response from get_response:", get_response(result))
"""
