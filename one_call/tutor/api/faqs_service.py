import time
import json
import httpx
import asyncio
from typing import Union, Optional, List, Tuple

class FAQsData:
    def __init__(self, accuracy, answer, query, response_time, source):
        self.accuracy = accuracy
        self.answer: str = answer
        self.query = query
        self.response_time = response_time
        self.source = source

    def is_source_faqs(self, match: str) -> bool:
        return match in self.source

    @classmethod
    def from_dict(cls, data):
        return cls(
            accuracy=data["accuracy"],
            answer=data["answer"],
            query=data["query"],
            response_time=data["response_time"],
            source=data["sources"]["source"]
        )

    def parse_response(self):
        """
        Parse and format the instance for readability.

        :return: A formatted string representation of the instance.
        """
        formatted_response = (
            f"Query: {self.query}\n"
            f"Answer: {self.answer}\n"
            f"Response Time: {self.response_time} seconds\n"
            f"Sources: Row {self.source}"
        )
        return formatted_response


class FaqsService:
    def __init__(self, logger,loop: None):
        """
        Initialize the FaqsService with the base URL for the API.
        """
        self.base_url = "http://localhost:3030" # Changed port to 3030
        self.client = httpx.AsyncClient()
        self.logger = logger
        self.loop = loop

    async def get_best_match(self, query: str) -> Optional[FAQsData]:
        """
        Send a POST request to the best_match endpoint with the provided query.

        :param query: The query string to send to the API.
        :return: The response text from the API.
        """
        url = f"{self.base_url}/best_match"
        payload = self._create_best_match_payload(query)
        headers = self._create_headers()

        try:
            response = await self.client.post(url, headers=headers, json=payload)
            response.raise_for_status()  # Raise an error for bad status codes

            response_data = response.json()
            return FAQsData.from_dict(response_data)
        except (httpx.RequestError, json.JSONDecodeError, KeyError) as e:
            self.logger.error(f"Error during request to FAQ service: {e}")
            return None

    async def get_top_matches(self, query: str, k: int = 3) -> Optional[Tuple[List[FAQsData], float]]:
        """
        Send a POST request to the top_matches endpoint with the provided query and k.

        :param query: The query string to send to the API.
        :param k: The number of top matches to retrieve.
        :return: A tuple containing a list of FAQsData objects and the response time, or None if an error occurs.
        """
        url = f"{self.base_url}/top_matches"
        payload = self._create_top_matches_payload(query, k)
        headers = self._create_headers()

        try:
            
            response = await self.client.post(url, headers=headers, json=payload)
            response.raise_for_status()  # Raise an error for bad status codes

            response_data = response.json()
            
            matches_list = []
            overall_query = response_data.get("query")
            overall_response_time = response_data.get("response_time")

            for match_dict in response_data.get("matches", []):
                faq_item = FAQsData(
                    accuracy=match_dict.get("accuracy"),
                    answer=match_dict.get("answer"),
                    query=overall_query, # Use the overall query for each match
                    response_time=overall_response_time, # Use overall response time
                    source=match_dict.get("sources", {}).get("source")
                )
                matches_list.append(faq_item)
            return matches_list, overall_response_time
        except (httpx.RequestError, json.JSONDecodeError, KeyError, TypeError) as e:
            self.logger.error(f"Error during request to FAQ top_matches service: {e}")
            return None

    @staticmethod
    def _create_best_match_payload(query: str) -> dict:
        """
        Create the JSON payload for the request.

        :param query: The query string to include in the payload.
        :return: The JSON payload as a string.
        """
        return {"query": query}
    
    @staticmethod
    def _create_top_matches_payload(query: str, k: int) -> dict:
        """
        Create the JSON payload for the /top_matches request.

        :param query: The query string.
        :param k: The number of top matches.
        :return: The JSON payload as a dictionary.
        """
        return {"query": query, "k": k}

    @staticmethod
    def _create_headers():
        """
        Create the headers for the request.

        :return: A dictionary containing the headers.
        """
        return {'Content-Type': 'application/json'}


# Example usage
if __name__ == "__main__":
    async def main():
        # A dummy logger for example usage
        class DummyLogger:
            def error(self, message):
                print(f"ERROR: {message}")

        service = FaqsService(DummyLogger(), loop= asyncio.get_event_loop())
        query = "How do I reset my password?"
        
        # Test get_best_match
        print("--- Testing get_best_match ---")
        best_match_result = await service.get_best_match(query)
        if best_match_result:
            print(best_match_result.parse_response())
        else:
            print("No best match found or an error occurred.")

        # Test get_top_matches
        print("\n--- Testing get_top_matches ---")
        top_matches_query = "Tell me about product features"
        top_matches_result = await service.get_top_matches(top_matches_query, k=2)
        if top_matches_result:
            matches, response_time = top_matches_result
            print(f"Overall response time: {response_time:.5f} seconds")
            for match in matches:
                print("---- Match ----")
                print(match.parse_response())
        else:
            print("No top matches found or an error occurred.")
    # Run the async main function
    asyncio.run(main())
