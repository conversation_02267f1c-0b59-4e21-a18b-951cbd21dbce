import os
import pathlib
import shutil
from datetime import datetime
from multiprocessing import Process
from threading import Thread

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from tutor import version
from tutor.api import routers
from tutor.api.logger import logger
from tutor.api.squire import discover
from tutor.executors import crontab
from tutor.modules.models import models

# Initiate API
app = FastAPI(
    title="Tutor API",
    description="#### Gateway to communicate with <PERSON>, and an entry point for the UI.\n\n"
                "**Contact:** Mayur ",
    version=version
)


def enable_cors() -> None:
    """Allow CORS: Cross-Origin Resource Sharing to allow restricted resources on the API."""
    logger.info('Setting CORS policy.')
    origins = [
        "http://localhost.com",
        "https://localhost.com",
        f"http://{models.env.website.host}",
        f"https://{models.env.website.host}",
        "https://localhost:5012",
        "http://localhost:5012",
         "*",
    ]

    app.add_middleware(
        CORSMiddleware,
        allow_origins=origins,
        allow_credentials=True,
        allow_methods=["GET", "POST"],
        allow_headers=["host", "user-agent",  # Default headers
                       "authorization", "apikey",  # Offline auth and stock monitor apikey headers
                       "email-otp", "email_otp",  # One time passcode sent via email
                       "access-token", "access_token"],  # Access token sent via email
    )


# Include all the routers
# WATCH OUT: for changes in function name
if models.settings.pname in ('AITUTOR', 'tutor_api', 'englishbot_api'):  # Avoid looping when called by subprocesses
    # Cannot add middleware after an application has started
    enable_cors()
    
    # Debug logging to see which routers are being discovered
    logger.info(f"Looking for routers in: {routers.__path__[0]}")
    discovered_routers = list(discover.routes(routers.__path__[0]))
    logger.info(f"Discovered routers: {discovered_routers}")
    
    # Explicitly include the english_bot router to ensure it's loaded
    try:
        from tutor.api.routers.english_bot import router as english_bot_router
        app.include_router(english_bot_router)
        logger.info("Explicitly included english_bot router")
    except ImportError as e:
        logger.error(f"Failed to import english_bot router: {e}")
    
    # Include all other discovered routers
    for router in discovered_routers:
        app.include_router(router)
        logger.info(f"Included router: {router}")


@app.on_event(event_type='startup')
async def startup_func() -> None:
    """Simple startup function to add anything that has to be triggered when Jarvis API starts up."""
    logger.info("Hosting at http://%s:%s", models.env.offline_host, models.env.offline_port)
    # if models.env.author_mode:
    #    Thread(target=stockanalysis_squire.nasdaq).start()
    if not os.path.isdir(models.fileio.startup_dir):
        return
    for startup_script in os.listdir(models.fileio.startup_dir):
        startup_script = pathlib.Path(startup_script)
        logger.info("Executing startup script: '%s'", startup_script)
        if startup_script.suffix in ('.py', '.sh', '.zsh') and not startup_script.stem.startswith('_'):
            starter = None
            if startup_script.suffix == ".py":
                starter = shutil.which(cmd='python')
            if startup_script.suffix == ".sh":
                starter = shutil.which(cmd='bash')
            if startup_script.suffix == ".zsh":
                starter = shutil.which(cmd='zsh')
            if not starter:
                continue
            script = starter + " " + os.path.join(models.fileio.startup_dir, startup_script)
            logger.debug("Running %s", script)
            log_file = datetime.now().strftime(os.path.join('logs', 'startup_script_%d-%m-%Y.log'))
            Process(target=crontab.executor, args=(script, log_file, 'startup_script')).start()
        else:
            logger.warning("Unsupported file format for startup script: %s", startup_script)
