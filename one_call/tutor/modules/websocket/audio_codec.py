"""
Audio codec utilities for bi-directional audio streaming
Handles mulaw/8000 format conversion and base64 encoding/decoding
"""

import audioop
import base64
import io
import numpy as np
import wave
from typing import Union, Tuple
from pydub import AudioSegment
from pydub.utils import make_chunks


class AudioCodec:
    """Handles audio format conversions for WebSocket streaming protocol"""
    
    # Protocol specifications
    SAMPLE_RATE = 8000
    BIT_DEPTH = 8
    BIT_RATE = 64  # kbps
    ENCODING = "audio/x-mulaw"  # G.711 µ-law (PCMU)
    CHANNELS = 1  # mono
    
    @staticmethod  
    def _normalize_audio(audio: AudioSegment) -> AudioSegment:
        """
        Normalize audio for better µ-law encoding quality
        
        Args:
            audio: AudioSegment to normalize
            
        Returns:
            Normalized AudioSegment
        """
        try:
            # Calculate RMS and peak levels
            raw_data = np.frombuffer(audio.raw_data, dtype=np.int16)
            rms = np.sqrt(np.mean(raw_data.astype(np.float32) ** 2))
            peak = np.max(np.abs(raw_data))
            
            # Target RMS level (about -12dB from peak to avoid clipping)
            target_rms = 8192  # About 25% of 16-bit range
            
            if rms > 0 and peak > 0:
                # Calculate gain needed, but limit to prevent clipping
                rms_gain = target_rms / rms
                peak_gain = 28000 / peak  # Leave headroom
                gain = min(rms_gain, peak_gain, 4.0)  # Limit max gain to 4x
                
                if abs(gain - 1.0) > 0.1:  # Only apply if significant change needed
                    audio = audio + (20 * np.log10(gain))  # Apply gain in dB
                    
            return audio
            
        except Exception as e:
            # If normalization fails, return original audio
            return audio
    
    @staticmethod
    def pcm_to_mulaw_enhanced(pcm_data: bytes, sample_width: int = 2) -> bytes:
        """
        Enhanced PCM to µ-law conversion with dithering for better quality
        
        Args:
            pcm_data: Raw PCM audio bytes
            sample_width: Sample width in bytes (typically 2 for 16-bit)
            
        Returns:
            µ-law encoded audio bytes with enhanced quality
        """
        try:
            # Add very light dithering to reduce quantization noise
            audio_array = np.frombuffer(pcm_data, dtype=np.int16)
            
            # Apply triangular dithering (very light)
            dither_amplitude = 0.5  # Very subtle
            dither = np.random.triangular(-dither_amplitude, 0, dither_amplitude, len(audio_array))
            dithered_audio = audio_array.astype(np.float32) + dither
            
            # Clip to valid range and convert back to int16
            dithered_audio = np.clip(dithered_audio, -32768, 32767).astype(np.int16)
            dithered_pcm = dithered_audio.tobytes()
            
            # Convert to µ-law using standard method
            mulaw_data = audioop.lin2ulaw(dithered_pcm, sample_width)
            return mulaw_data
            
        except Exception as e:
            # Fallback to standard conversion if enhanced fails
            return AudioCodec.pcm_to_mulaw(pcm_data, sample_width)
    
    @staticmethod
    def pcm_to_mulaw(pcm_data: bytes, sample_width: int = 2) -> bytes:
        """
        Convert PCM audio data to µ-law format
        
        Args:
            pcm_data: Raw PCM audio bytes
            sample_width: Sample width in bytes (typically 2 for 16-bit)
            
        Returns:
            µ-law encoded audio bytes
        """
        try:
            # Convert PCM to µ-law using audioop
            mulaw_data = audioop.lin2ulaw(pcm_data, sample_width)
            return mulaw_data
        except Exception as e:
            raise ValueError(f"Error converting PCM to µ-law: {e}")
    
    @staticmethod
    def mulaw_to_pcm(mulaw_data: bytes, sample_width: int = 2) -> bytes:
        """
        Convert µ-law audio data to PCM format
        
        Args:
            mulaw_data: µ-law encoded audio bytes
            sample_width: Target sample width in bytes
            
        Returns:
            PCM audio bytes
        """
        try:
            # Convert µ-law to PCM using audioop
            pcm_data = audioop.ulaw2lin(mulaw_data, sample_width)
            return pcm_data
        except Exception as e:
            raise ValueError(f"Error converting µ-law to PCM: {e}")
    
    @classmethod
    def convert_audio_to_protocol_format(cls, audio_data: bytes, 
                                       source_sample_rate: int = 44100,
                                       source_channels: int = 2,
                                       source_sample_width: int = 2) -> bytes:
        """
        Convert audio to protocol-compliant format (mulaw/8000/mono)
        
        Args:
            audio_data: Source audio data
            source_sample_rate: Source sample rate
            source_channels: Source channel count
            source_sample_width: Source sample width in bytes
            
        Returns:
            Protocol-compliant mulaw audio bytes
        """
        try:
            # Ensure audio data length is properly aligned
            bytes_per_sample = source_sample_width * source_channels
            if len(audio_data) % bytes_per_sample != 0:
                # Trim to proper alignment
                aligned_length = (len(audio_data) // bytes_per_sample) * bytes_per_sample
                audio_data = audio_data[:aligned_length]
            
            # Create AudioSegment from raw data
            audio = AudioSegment(
                data=audio_data,
                sample_width=source_sample_width,
                frame_rate=source_sample_rate,
                channels=source_channels
            )
            
            # Convert to mono if stereo
            if audio.channels > 1:
                audio = audio.set_channels(1)
            
            # Resample to 8000 Hz
            if audio.frame_rate != cls.SAMPLE_RATE:
                audio = audio.set_frame_rate(cls.SAMPLE_RATE)
            
            # Ensure 16-bit PCM for conversion
            if audio.sample_width != 2:
                audio = audio.set_sample_width(2)
            
            # Get raw PCM data (no normalization - preserve original audio)
            pcm_data = audio.raw_data
            
            # Convert to µ-law (standard conversion, no enhancements)
            mulaw_data = cls.pcm_to_mulaw(pcm_data, sample_width=2)
            
            return mulaw_data
            
        except Exception as e:
            raise ValueError(f"Error converting audio to protocol format: {e}")
    
    @classmethod
    def convert_protocol_audio_to_pcm(cls, mulaw_data: bytes,
                                    target_sample_rate: int = 44100,
                                    target_channels: int = 1,
                                    target_sample_width: int = 2) -> bytes:
        """
        Convert protocol audio (mulaw/8000) to target PCM format
        
        Args:
            mulaw_data: Protocol mulaw audio data
            target_sample_rate: Target sample rate
            target_channels: Target channel count
            target_sample_width: Target sample width in bytes
            
        Returns:
            Converted PCM audio bytes
        """
        try:
            # Convert µ-law to PCM (16-bit)
            pcm_data = cls.mulaw_to_pcm(mulaw_data, sample_width=2)
            
            # Create AudioSegment from PCM data
            audio = AudioSegment(
                data=pcm_data,
                sample_width=2,
                frame_rate=cls.SAMPLE_RATE,
                channels=1
            )
            
            # Resample if needed
            if target_sample_rate != cls.SAMPLE_RATE:
                audio = audio.set_frame_rate(target_sample_rate)
            
            # Set channels if needed
            if target_channels != 1:
                audio = audio.set_channels(target_channels)
            
            # Set sample width if needed
            if target_sample_width != 2:
                audio = audio.set_sample_width(target_sample_width)
            
            return audio.raw_data
            
        except Exception as e:
            raise ValueError(f"Error converting protocol audio to PCM: {e}")
    
    @staticmethod
    def encode_audio_to_base64(audio_data: bytes) -> str:
        """
        Encode audio data to base64 string for WebSocket transmission
        
        Args:
            audio_data: Audio bytes to encode
            
        Returns:
            Base64 encoded string
        """
        return base64.b64encode(audio_data).decode('utf-8')
    
    @staticmethod
    def decode_audio_from_base64(base64_data: str) -> bytes:
        """
        Decode base64 audio data from WebSocket message
        
        Args:
            base64_data: Base64 encoded audio string
            
        Returns:
            Decoded audio bytes
        """
        try:
            return base64.b64decode(base64_data)
        except Exception as e:
            raise ValueError(f"Error decoding base64 audio data: {e}")
    
    @classmethod
    def create_chunks(cls, audio_data: bytes, chunk_duration_ms: int = 20) -> list:
        """
        Split audio data into chunks for streaming
        
        Args:
            audio_data: Audio data to chunk
            chunk_duration_ms: Duration of each chunk in milliseconds
            
        Returns:
            List of audio chunks
        """
        try:
            # Create AudioSegment from mulaw data
            audio = AudioSegment(
                data=audio_data,
                sample_width=1,  # µ-law is 8-bit (1 byte)
                frame_rate=cls.SAMPLE_RATE,
                channels=1
            )
            
            # Create chunks
            chunks = make_chunks(audio, chunk_duration_ms)
            return [chunk.raw_data for chunk in chunks]
            
        except Exception as e:
            raise ValueError(f"Error creating audio chunks: {e}")
    
    @classmethod
    def create_silence(cls, duration_ms: int) -> bytes:
        """
        Create silence in protocol format
        
        Args:
            duration_ms: Duration in milliseconds
            
        Returns:
            Silent audio data in mulaw format
        """
        try:
            # Create silent AudioSegment
            silence = AudioSegment.silent(
                duration=duration_ms,
                frame_rate=cls.SAMPLE_RATE
            )
            
            # Convert to mono and set sample width
            silence = silence.set_channels(1).set_sample_width(2)
            
            # Convert to mulaw
            pcm_data = silence.raw_data
            mulaw_data = cls.pcm_to_mulaw(pcm_data, sample_width=2)
            
            return mulaw_data
            
        except Exception as e:
            raise ValueError(f"Error creating silence: {e}")
    
    @staticmethod
    def validate_protocol_audio(audio_data: bytes) -> Tuple[bool, str]:
        """
        Validate if audio data conforms to protocol requirements
        
        Args:
            audio_data: Audio data to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if not audio_data:
                return False, "Empty audio data"
            
            # µ-law is 8-bit, so each sample is 1 byte
            # At 8000 Hz, we expect samples to align properly
            sample_count = len(audio_data)
            
            # Basic sanity checks
            if sample_count == 0:
                return False, "No audio samples"
            
            # Check if duration makes sense (not too short/long)
            duration_seconds = sample_count / AudioCodec.SAMPLE_RATE
            if duration_seconds > 300:  # 5 minutes max
                return False, f"Audio too long: {duration_seconds:.2f} seconds"
            
            return True, "Valid protocol audio"
            
        except Exception as e:
            return False, f"Validation error: {e}"


class AudioBuffer:
    """Thread-safe audio buffer for streaming audio data"""
    
    def __init__(self, max_duration_seconds: int = 30):
        self.max_samples = max_duration_seconds * AudioCodec.SAMPLE_RATE
        self.buffer = bytearray()
        self._lock = None  # Will be set by user
    
    def append(self, audio_data: bytes) -> None:
        """Add audio data to buffer"""
        if self._lock:
            with self._lock:
                self.buffer.extend(audio_data)
                # Trim buffer if too long
                if len(self.buffer) > self.max_samples:
                    excess = len(self.buffer) - self.max_samples
                    self.buffer = self.buffer[excess:]
        else:
            self.buffer.extend(audio_data)
            if len(self.buffer) > self.max_samples:
                excess = len(self.buffer) - self.max_samples
                self.buffer = self.buffer[excess:]
    
    def get_and_clear(self) -> bytes:
        """Get all buffered audio and clear buffer"""
        if self._lock:
            with self._lock:
                data = bytes(self.buffer)
                self.buffer.clear()
                return data
        else:
            data = bytes(self.buffer)
            self.buffer.clear()
            return data
    
    def get_chunk(self, size: int) -> bytes:
        """Get a chunk of specified size from buffer"""
        if self._lock:
            with self._lock:
                if len(self.buffer) >= size:
                    chunk = bytes(self.buffer[:size])
                    self.buffer = self.buffer[size:]
                    return chunk
                else:
                    chunk = bytes(self.buffer)
                    self.buffer.clear()
                    return chunk
        else:
            if len(self.buffer) >= size:
                chunk = bytes(self.buffer[:size])
                self.buffer = self.buffer[size:]
                return chunk
            else:
                chunk = bytes(self.buffer)
                self.buffer.clear()
                return chunk
    
    def clear(self) -> None:
        """Clear the buffer"""
        if self._lock:
            with self._lock:
                self.buffer.clear()
        else:
            self.buffer.clear()
    
    def size(self) -> int:
        """Get current buffer size in bytes"""
        return len(self.buffer)
    
    def duration_seconds(self) -> float:
        """Get current buffer duration in seconds"""
        return len(self.buffer) / AudioCodec.SAMPLE_RATE