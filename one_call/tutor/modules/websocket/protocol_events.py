"""
WebSocket Protocol Events for Bi-Directional Audio Streaming
Based on the integration document requirements
"""

import base64
import json
import time
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, Union
from enum import Enum


class EventType(Enum):
    """WebSocket event types as defined in the protocol"""
    CONNECTED = "connected"
    START = "start"
    MEDIA = "media"
    STOP = "stop"
    DTMF = "dtmf"
    MARK = "mark"
    CLEAR = "clear"


@dataclass
class MediaFormat:
    """Audio media format specification"""
    encoding: str = "audio/x-mulaw"  # G.711 µ-law (PCMU)
    sampleRate: int = 8000
    bitRate: int = 64  # kbps
    bitDepth: int = 8


@dataclass
class StreamMetadata:
    """Stream metadata for start event"""
    streamSid: str
    accountSid: str
    callSid: str
    from_number: str  # 'from' is a reserved keyword
    to_number: str    # 'to' is a reserved keyword  
    mediaFormat: MediaFormat
    customParameters: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary with proper field names"""
        data = asdict(self)
        data['from'] = data.pop('from_number')
        data['to'] = data.pop('to_number')
        data['mediaFormat'] = asdict(self.mediaFormat)
        return data


class BaseEvent(ABC):
    """Base class for all WebSocket events"""
    
    def __init__(self, event_type: EventType, sequence_number: int, stream_sid: str):
        self.event = event_type.value
        self.sequenceNumber = str(sequence_number)
        self.streamSid = stream_sid
        self.timestamp = int(time.time() * 1000)  # milliseconds
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary for JSON serialization"""
        pass
    
    def to_json(self) -> str:
        """Convert event to JSON string"""
        return json.dumps(self.to_dict())


class ConnectedEvent(BaseEvent):
    """Connected event - handshake response"""
    
    def __init__(self):
        # Connected event doesn't have sequence number or stream ID initially
        self.event = EventType.CONNECTED.value
    
    def to_dict(self) -> Dict[str, Any]:
        return {"event": self.event}


class StartEvent(BaseEvent):
    """Start event with stream metadata"""
    
    def __init__(self, sequence_number: int, stream_metadata: StreamMetadata):
        super().__init__(EventType.START, sequence_number, stream_metadata.streamSid)
        self.start = stream_metadata
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "event": self.event,
            "sequenceNumber": self.sequenceNumber,
            "start": self.start.to_dict(),
            "streamSid": self.streamSid
        }


class MediaEvent(BaseEvent):
    """Media event with audio data"""
    
    def __init__(self, sequence_number: int, stream_sid: str, chunk: int, 
                 timestamp: int, payload: bytes):
        super().__init__(EventType.MEDIA, sequence_number, stream_sid)
        self.media = {
            "chunk": str(chunk),
            "timestamp": str(timestamp),
            "payload": base64.b64encode(payload).decode('utf-8')
        }
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "event": self.event,
            "sequenceNumber": self.sequenceNumber,
            "media": self.media,
            "streamSid": self.streamSid
        }


class StopEvent(BaseEvent):
    """Stop event to terminate stream"""
    
    def __init__(self, sequence_number: int, stream_sid: str, account_sid: str,
                 call_sid: str, reason: str):
        super().__init__(EventType.STOP, sequence_number, stream_sid)
        self.stop = {
            "accountSid": account_sid,
            "callSid": call_sid,
            "reason": reason
        }
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "event": self.event,
            "sequenceNumber": self.sequenceNumber,
            "stop": self.stop,
            "streamSid": self.streamSid
        }


class DTMFEvent(BaseEvent):
    """DTMF event for touch-tone detection"""
    
    def __init__(self, sequence_number: int, stream_sid: str, digit: str):
        super().__init__(EventType.DTMF, sequence_number, stream_sid)
        self.dtmf = {"digit": digit}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "event": self.event,
            "streamSid": self.streamSid,
            "sequenceNumber": self.sequenceNumber,
            "dtmf": self.dtmf
        }


class MarkEvent(BaseEvent):
    """Mark event for audio playback synchronization"""
    
    def __init__(self, sequence_number: int, stream_sid: str, name: str):
        super().__init__(EventType.MARK, sequence_number, stream_sid)
        self.mark = {"name": name}
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "event": self.event,
            "sequenceNumber": self.sequenceNumber,
            "streamSid": self.streamSid,
            "mark": self.mark
        }


class ClearEvent(BaseEvent):
    """Clear event to interrupt buffered audio"""
    
    def __init__(self, stream_sid: str):
        self.event = EventType.CLEAR.value
        self.streamSid = stream_sid
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "event": self.event,
            "streamSid": self.streamSid
        }


class EventFactory:
    """Factory class for creating events from incoming JSON data"""
    
    @staticmethod
    def create_event_from_json(json_data: Union[str, Dict[str, Any]]) -> Optional[BaseEvent]:
        """Create appropriate event object from JSON data"""
        try:
            if isinstance(json_data, str):
                data = json.loads(json_data)
            else:
                data = json_data
            
            event_type = data.get("event")
            
            if event_type == EventType.CONNECTED.value:
                return ConnectedEvent()
            
            elif event_type == EventType.MEDIA.value:
                # This is for incoming media events from vendor
                return None  # We'll handle incoming media differently
            
            elif event_type == EventType.MARK.value:
                return MarkEvent(
                    sequence_number=int(data.get("sequenceNumber", 0)),
                    stream_sid=data.get("streamSid", ""),
                    name=data.get("mark", {}).get("name", "")
                )
            
            elif event_type == EventType.CLEAR.value:
                return ClearEvent(stream_sid=data.get("streamSid", ""))
                
            else:
                return None
                
        except (json.JSONDecodeError, KeyError, ValueError) as e:
            print(f"Error creating event from JSON: {e}")
            return None


class StreamManager:
    """Manages WebSocket stream lifecycle and sequence numbers"""
    
    def __init__(self):
        self.streams: Dict[str, Dict[str, Any]] = {}
        self.sequence_counters: Dict[str, int] = {}
    
    def create_stream(self, account_sid: str = None, call_sid: str = None,
                     from_number: str = None, to_number: str = None,
                     custom_parameters: Dict[str, Any] = None) -> str:
        """Create a new stream and return stream ID"""
        stream_sid = "MZ" + str(uuid.uuid4()).replace("-", "").upper()
        
        if not account_sid:
            account_sid = "AC" + str(uuid.uuid4()).replace("-", "").upper()
        if not call_sid:
            call_sid = "CA" + str(uuid.uuid4()).replace("-", "").upper()
        
        self.streams[stream_sid] = {
            "streamSid": stream_sid,
            "accountSid": account_sid,
            "callSid": call_sid,
            "from": from_number or "XXXXXXXXXX",
            "to": to_number or "XXXXXXXXXX",
            "customParameters": custom_parameters or {},
            "created_at": time.time(),
            "status": "active"
        }
        
        self.sequence_counters[stream_sid] = 0
        return stream_sid
    
    def get_next_sequence_number(self, stream_sid: str) -> int:
        """Get next sequence number for a stream"""
        if stream_sid not in self.sequence_counters:
            self.sequence_counters[stream_sid] = 0
        
        self.sequence_counters[stream_sid] += 1
        return self.sequence_counters[stream_sid]
    
    def get_stream_metadata(self, stream_sid: str) -> Optional[StreamMetadata]:
        """Get stream metadata for creating start event"""
        if stream_sid not in self.streams:
            return None
        
        stream_data = self.streams[stream_sid]
        return StreamMetadata(
            streamSid=stream_sid,
            accountSid=stream_data["accountSid"],
            callSid=stream_data["callSid"],
            from_number=stream_data["from"],
            to_number=stream_data["to"],
            mediaFormat=MediaFormat(),
            customParameters=stream_data["customParameters"]
        )
    
    def end_stream(self, stream_sid: str, reason: str = "Stream ended") -> None:
        """Mark stream as ended"""
        if stream_sid in self.streams:
            self.streams[stream_sid]["status"] = "ended"
            self.streams[stream_sid]["end_reason"] = reason
            self.streams[stream_sid]["ended_at"] = time.time()
    
    def cleanup_stream(self, stream_sid: str) -> None:
        """Remove stream data"""
        self.streams.pop(stream_sid, None)
        self.sequence_counters.pop(stream_sid, None)