"""Module for database controls.

>>> Database

"""

from datetime import datetime
import importlib
import logging
import os
import random
import sqlite3
from typing import Dict, List, Optional, Tuple, Union

from pydantic import FilePath


from datetime import datetime
import sqlite3
from typing import List, Tuple, Union
from pydantic import FilePath


class Database:
    """Creates a connection to the base DB."""

    def __init__(self, database: Union[FilePath, str], timeout: int = 10):
        """Instantiates the class ``Database`` to create a connection and a cursor.

        Args:
            database: Name of the database file.
            timeout: Timeout for the connection to database.
        """
        if not database.endswith('.db'):
            database = database + '.db'
        self.datastore = database
        self.connection = sqlite3.connect(database=self.datastore, check_same_thread=False, timeout=timeout)
        self.create_support_ticket_table()
        self.create_transcripts_table()
        self.add_column("SupportTicket", "customer_name", "TEXT")
        self.add_column("SupportTicket", "is_sentiment_analyzed", "INTEGER", default_value=0)
        self.add_column("SupportTicket", "is_posted", "INTEGER", default_value=0)
        self.add_column("SupportTicket", "sentiment_category", "TEXT")
        self.add_column("SupportTicket", "sentiment_subcategory", "TEXT")
        self.add_column("SupportTicket", "sentiment_score", "INTEGER", default_value=0)
        self.add_column("SupportTicket", "key_emotions", "TEXT")
        self.add_column("SupportTicket", "text_sentiment", "TEXT")
        # Add is_qa_processed column to Transcripts table
        self.add_column("Transcripts", "is_qa_processed", "INTEGER", default_value=0)
        self.add_column("Transcripts", "is_audio_processed", "INTEGER", default_value=0)


    def create_table(self, table_name: str, columns: Union[List[str], Tuple[str]]) -> None:
        """Creates the table with the required columns."""
        with self.connection:
            cursor = self.connection.cursor()
            cursor.execute(f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})")

    def create_support_ticket_table(self) -> None:
        """Creates the SupportTicket table with the required schema."""
        columns = [
            "case_id TEXT PRIMARY KEY",
            "account_id TEXT",
            "case_type TEXT",
            "subject TEXT",
            "description TEXT",
            "status TEXT",
            "date TEXT"
        ]
        self.create_table("SupportTicket", columns)

    def create_transcripts_table(self) -> None:
        """Creates the Transcripts table with the required schema."""
        columns = [
            "id INTEGER PRIMARY KEY AUTOINCREMENT",
            "call_id TEXT UNIQUE NOT NULL",
            "timestamp REAL NOT NULL",
            "full_transcript TEXT NOT NULL",
            "utterances TEXT NOT NULL",  # JSON string for utterances
            "created_at TEXT DEFAULT (datetime('now'))"
        ]
        self.create_table("Transcripts", columns)
        # Create indexes for faster lookups
        with self.connection:
            cursor = self.connection.cursor()
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_transcripts_call_id ON Transcripts(call_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_transcripts_timestamp ON Transcripts(timestamp)")

    def add_column(self, table_name: str, column_name: str, data_type: str, default_value=None) -> None:
        """Adds a new column to an existing table if it does not already exist."""
        if not self.column_exists(table_name, column_name):
            default_clause = f"DEFAULT {default_value}" if default_value is not None else ""
            with self.connection:
                cursor = self.connection.cursor()
                cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {data_type} {default_clause}")

    def column_exists(self, table_name: str, column_name: str) -> bool:
        """Checks if a column exists in the specified table."""
        with self.connection:
            cursor = self.connection.cursor()
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [col[1] for col in cursor.fetchall()]
            return column_name in columns

    def get_unanalyzed_records(self, table_name: str="SupportTicket") -> List[dict]:
        """Fetches records where sentiment analysis has not been completed."""
        query = f"SELECT * FROM {table_name} WHERE is_sentiment_analyzed = 0"
        with self.connection:
            cursor = self.connection.cursor()
            cursor.execute(query)
            columns = [column[0] for column in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return results
        
    def get_unposted_records(self, table_name: str="SupportTicket") -> List[dict]:
        """Fetches records where sentiment analysis has been completed but not yet posted."""
        query = f"SELECT * FROM {table_name} WHERE is_sentiment_analyzed = 1 AND is_posted = 0"
        with self.connection:
            cursor = self.connection.cursor()
            cursor.execute(query)
            columns = [column[0] for column in cursor.description]
            results = [dict(zip(columns, row)) for row in cursor.fetchall()]
            return results        

    def update_record_with_sentiment(self, case_id: str, sentiment_category: str, sentiment_subcategory: str, score: int, emotions: List[str], text_sentiment: str) -> None:
        """Updates the record in the database with the analyzed sentiment category, subcategory, score, and key emotions."""
        emotions_str = ", ".join(emotions)
        query = """
            UPDATE SupportTicket
            SET is_sentiment_analyzed = 1, sentiment_category = ?, sentiment_subcategory = ?, sentiment_score = ?, key_emotions = ?, text_sentiment = ?
            WHERE case_id = ?
        """
        with self.connection:
            cursor = self.connection.cursor()
            cursor.execute(query, (sentiment_category, sentiment_subcategory, score, emotions_str, text_sentiment, case_id))
            self.connection.commit()

    def update_record_with_is_posted(self, case_id: str) -> None:
        """Marks a record as posted in the database."""
        query = """
            UPDATE SupportTicket
            SET is_posted = 1
            WHERE case_id = ?
        """
        with self.connection:
            cursor = self.connection.cursor()
            cursor.execute(query, (case_id,))
            self.connection.commit()            

    def insert_support_ticket(self, account_id: str, customer_name: str, case_type: str, subject: str, description: str) -> str:
        """Inserts a case into the SupportTicket table and returns the case ID."""
        case_id = self.generate_case_id()
        
        # Ensure all inputs are strings to avoid InterfaceError
        account_id = str(account_id) if account_id is not None else ""
        customer_name = str(customer_name) if customer_name is not None else ""
        case_type = str(case_type) if case_type is not None else ""
        subject = str(subject) if subject is not None else ""
        description = str(description) if description is not None else ""

        query = """
            INSERT INTO SupportTicket (case_id, account_id, customer_name, case_type, subject, description, status, date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        with self.connection:
            cursor = self.connection.cursor()
            cursor.execute(query, (
                case_id,
                account_id,
                customer_name,
                case_type,
                subject,
                description,
                "Pending",
                datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ))
            self.connection.commit()
            return case_id
        
    def generate_case_id(self) -> str:
        """Generates a unique case ID based on timestamp and an incrementing sequence number."""
        from tutor.modules.utils import shared
        shared.seq_number += 1
        timestamp = datetime.now().strftime("%d%H%M%S%f")[:9]  # Format: ddHHmmsss
        return f"{timestamp}{shared.seq_number:03}"

    def insert_record(self, table_name: str, record: dict) -> int:
        """Inserts a record into the specified table and returns the row ID.
        
        Args:
            table_name: Name of the table to insert into
            record: Dictionary containing column names and values
            
        Returns:
            The row ID of the inserted record
        """
        columns = ', '.join(record.keys())
        placeholders = ', '.join(['?' for _ in record])
        query = f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})"
        
        with self.connection:
            cursor = self.connection.cursor()
            cursor.execute(query, tuple(record.values()))
            self.connection.commit()
            return cursor.lastrowid

    def get_transcript(self, call_id: str) -> Optional[Dict]:
        """Get transcript by call_id.
        
        Args:
            call_id: The call ID to retrieve
        
        Returns:
            Dict containing transcript data or None if not found
        """
        try:
            with self.connection:
                cursor = self.connection.cursor()
                cursor.execute("SELECT * FROM Transcripts WHERE call_id = ?", (call_id,))
                result = cursor.fetchone()
                
                if result:
                    columns = [column[0] for column in cursor.description]
                    return dict(zip(columns, result))
                return None
        except Exception as e:
            self.logger.error(f"Error retrieving transcript: {str(e)}")
            return None

    def get_transcripts(self, limit: int = 100, offset: int = 0) -> List[dict]:
        """Retrieves multiple transcripts with pagination.
        
        Args:
            limit: Maximum number of records to return
            offset: Number of records to skip
            
        Returns:
            List of dictionaries containing transcript data
        """
        query = f"SELECT * FROM Transcripts ORDER BY timestamp DESC LIMIT ? OFFSET ?"
        with self.connection:
            cursor = self.connection.cursor()
            cursor.execute(query, (limit, offset))
            columns = [column[0] for column in cursor.description]
            return [dict(zip(columns, row)) for row in cursor.fetchall()]

    def create_qa_tables(self):
        """Create tables needed for QA functionality."""
        # Create QAReport table
        columns = [
            "report_id TEXT PRIMARY KEY",
            "call_summary TEXT",
            "total_score REAL",
            "call_category TEXT",
            "timestamp TEXT",
            "agent_strengths TEXT",
            "agent_areas_for_improvement TEXT"
        ]
        self.create_table("QAReport", columns)
        
        # Create QAParameterScore table
        columns = [
            "id INTEGER PRIMARY KEY AUTOINCREMENT",
            "report_id TEXT",
            "parameter_id INTEGER",
            "score REAL",
            "comments TEXT",
            "FOREIGN KEY (report_id) REFERENCES QAReport (report_id)"
        ]
        self.create_table("QAParameterScore", columns)
        
        # Add qa_report_id column to Transcripts table if it doesn't exist
        self.add_column("Transcripts", "qa_report_id", "TEXT")
        
        # Add audio_file_path column to Transcripts table if it doesn't exist
        self.add_column("Transcripts", "audio_file_path", "TEXT")

    def ensure_transcript_ready_for_qa(self, call_id: str, file_path: str) -> None:
        """Ensure transcript is properly marked for QA processing.
        
        This method makes sure that:
        1. The transcript exists in the database
        2. It's marked as audio processed (is_audio_processed = 1)
        3. It's marked as NOT QA processed (is_qa_processed = 0)
        
        Args:
            call_id: The call ID associated with the transcript
            file_path: The path to the audio file
        """
        try:
            with self.connection:
                cursor = self.connection.cursor()
                
                # Check if transcript exists
                cursor.execute("SELECT * FROM Transcripts WHERE call_id = ?", (call_id,))
                transcript = cursor.fetchone()
                
                if transcript:
                    # Update existing transcript
                    cursor.execute(
                        """UPDATE Transcripts 
                           SET is_audio_processed = 1, 
                               audio_file_path = ?,
                               is_qa_processed = 0
                           WHERE call_id = ?""",
                        (file_path, call_id)
                    )
                else:
                    # Create new transcript record
                    cursor.execute(
                        """INSERT INTO Transcripts 
                           (call_id, audio_file_path, is_audio_processed, is_qa_processed)
                           VALUES (?, ?, 1, 0)""",
                        (call_id, file_path)
                    )
                
                self.connection.commit()
                
        except Exception as e:
            raise Exception(f"Error ensuring transcript is ready for QA: {str(e)}")


class __TestDatabase:
    """Basic examples of a test database.

    >>> __TestDatabase

    """

    def __init__(self):
        """Initiates all the imported modules and creates a database file named ``sample``."""
        importlib.reload(module=logging)

        handler = logging.StreamHandler()
        fmt_ = logging.Formatter(
            fmt='%(asctime)s - %(levelname)s - [%(module)s:%(lineno)d] - %(funcName)s - %(message)s',
            datefmt='%b-%d-%Y %I:%M:%S %p'
        )
        handler.setFormatter(fmt=fmt_)
        logging.root.addHandler(hdlr=handler)
        logging.root.setLevel(level=logging.DEBUG)
        self.db = Database(database="sample")

    def at_exit(self):
        """Deletes the database file ``sample``."""
        os.remove(self.db.datastore)

    def random_single(self) -> None:
        """Example using a single column."""
        self.db.create_table(table_name="TestDatabase", columns=["column"])
        with self.db.connection:
            cursor_ = self.db.connection.cursor()
            cursor_.execute("INSERT INTO TestDatabase (column) VALUES (?);", (True,))
            self.db.connection.commit()
            if foo := cursor_.execute("SELECT column FROM TestDatabase").fetchone():
                logging.info(foo[0])
                cursor_.execute("DELETE FROM TestDatabase WHERE column=1")
                self.db.connection.commit()
            if bar := cursor_.execute("SELECT column FROM TestDatabase").fetchone():
                logging.warning(bar[0])
            cursor_.execute("DROP TABLE IF EXISTS TestDatabase")
            self.db.connection.commit()

    def random_double(self) -> None:
        """Example using two columns with only one holding a value at any given time."""
        self.db.create_table(table_name="TestDatabase", columns=["row", "column"])
        with self.db.connection:
            cursor_ = self.db.connection.cursor()
            cursor_.execute(f"INSERT INTO TestDatabase ({random.choice(['row', 'column'])}) VALUES (?);", (True,))
            self.db.connection.commit()
            if (row := cursor_.execute("SELECT row FROM TestDatabase").fetchone()) and row[0]:
                logging.info(f"Row: {row[0]}")
                cursor_.execute("DELETE FROM TestDatabase WHERE row=1")
                self.db.connection.commit()
            if (col := cursor_.execute("SELECT column FROM TestDatabase").fetchone()) and col[0]:
                logging.info(f"Column: {col[0]}")
                cursor_.execute("DELETE FROM TestDatabase WHERE column=1")
                self.db.connection.commit()
            cursor_.execute("DROP TABLE IF EXISTS TestDatabase")
            self.db.connection.commit()


if __name__ == '__main__':
    test_db = __TestDatabase()
    test_db.random_single()
    test_db.random_double()
    test_db.at_exit()
