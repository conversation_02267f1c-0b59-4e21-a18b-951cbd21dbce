import importlib
import logging
import os
from datetime import datetime
from logging import <PERSON><PERSON><PERSON>
from logging.config import dictConfig

from pydantic import BaseModel

from tutor.modules.builtin_overrides import AddProcessName
from tutor.modules.models import models

if not os.path.isdir('logs'):
    os.mkdir('logs')  # Creates only logs dir if limited mode is enabled

DEFAULT_LOG_FORM = '%(asctime)s - %(levelname)s - [%(processName)s:%(module)s:%(lineno)d] - %(funcName)s - %(message)s'
DEFAULT_FORMATTER = logging.Formatter(datefmt='%b-%d-%Y %I:%M:%S %p', fmt=DEFAULT_LOG_FORM)

importlib.reload(module=logging)
dictConfig({
    'version': 1,
    'disable_existing_loggers': True,
})
logging.getLogger("_code_cache").propagate = False

logger = logging.getLogger("OneCall")
if models.env.debug:
    logger.setLevel(level=logging.DEBUG)
else:
    logger.setLevel(level=logging.INFO)


def multiprocessing_logger(filename: str, log_format: Formatter = DEFAULT_FORMATTER) -> str:
    """Remove existing handlers and adds a new handler when a child process kicks in.

    Args:
        filename: Filename where the subprocess should log.
        log_format: Custom log format dedicated for each process.

    See Also:
        This will override the main logger and add a new logger pointing to the given filename.

    Returns:
        str:
        Actual log filename with datetime converted.
    """
    logger.propagate = False
    # Remove existing handlers
    for _handler in logger.handlers:
        logger.removeHandler(hdlr=_handler)
    log_handler = custom_handler(filename=filename, log_format=log_format)
    logger.addHandler(hdlr=log_handler)
    # Remove existing filters from the new log handler
    for _filter in logger.filters:
        logger.removeFilter(_filter)
    logger.addFilter(filter=AddProcessName(process_name=models.settings.pname))
    return log_handler.baseFilename


class APIConfig(BaseModel):
    """Custom log configuration to redirect all uvicorn logs to individual log files.

    >>> APIConfig

    """

    DEFAULT_LOG_LEVEL: str = "INFO"

    ACCESS_LOG_FILENAME: str = datetime.now().strftime(os.path.join('logs', 'onecall_api_access_%d-%m-%Y.log'))
    DEFAULT_LOG_FILENAME: str = datetime.now().strftime(os.path.join('logs', 'onecall_api_%d-%m-%Y.log'))

    ACCESS_LOG_FORMAT: str = '%(levelprefix)s %(client_addr)s - "%(request_line)s" %(status_code)s'
    ERROR_LOG_FORMAT: str = '%(levelname)s\t %(message)s'

    LOG_CONFIG: dict = {
        "version": 1,
        "disable_existing_loggers": True,
        "formatters": {
            "default": {
                "()": "uvicorn.logging.DefaultFormatter",
                "fmt": DEFAULT_LOG_FORM,
                "use_colors": False,
            },
            "access": {
                "()": "uvicorn.logging.AccessFormatter",
                "fmt": ACCESS_LOG_FORMAT,
                "use_colors": False,
            },
            "error": {
                "()": "uvicorn.logging.DefaultFormatter",
                "fmt": ERROR_LOG_FORMAT,
                "use_colors": False,
            },
        },
        "handlers": {
            "default": {
                "formatter": "default",
                "class": "logging.FileHandler",
                "filename": DEFAULT_LOG_FILENAME
            },
            "access": {
                "formatter": "access",
                "class": "logging.FileHandler",
                "filename": ACCESS_LOG_FILENAME
            },
            "error": {
                "formatter": "error",
                "class": "logging.FileHandler",
                "filename": DEFAULT_LOG_FILENAME
            }
        },
        "loggers": {
            "uvicorn": {
                "handlers": ["default"], "level": DEFAULT_LOG_LEVEL
            },
            "uvicorn.access": {
                "handlers": ["access"], "level": DEFAULT_LOG_LEVEL
            },
            "uvicorn.error": {
                "handlers": ["error"], "level": DEFAULT_LOG_LEVEL, "propagate": True  # Since FastAPI is threaded
            }
        }
    }


def log_file(filename: str) -> str:
    """Creates a log file and writes the headers into it.

    Returns:
        str:
        Log filename.
    """
    return datetime.now().strftime(filename)


def custom_handler(filename: str = None, log_format: logging.Formatter = None) -> logging.FileHandler:
    """Creates a FileHandler, sets the log format and returns it.

    Returns:
        logging.FileHandler:
        Returns file handler.
    """
    handler = logging.FileHandler(
        filename=log_file(filename=filename or os.path.join('logs', 'onecall_%d-%m-%Y_%H-%M.log')),
        mode='a'
    )
    handler.setFormatter(fmt=log_format or DEFAULT_FORMATTER)
    return handler


def setup_logger(session_id: str, mobile: str):
    log_dir = f"fileio/sessions/{mobile}/{session_id}"
    os.makedirs(log_dir, exist_ok=True)
    log_file = os.path.join(log_dir, "logs.log")
    
    logger = logging.getLogger(f"user_{session_id}")
    logger.setLevel(logging.INFO)
    
    # Remove existing handlers to avoid duplicate logs
    if logger.hasHandlers():
        logger.handlers.clear()
    
    # File handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(DEFAULT_FORMATTER)
    logger.addHandler(file_handler)
    
    # Stream handler
    stream_handler = logging.StreamHandler()
    stream_handler.setFormatter(DEFAULT_FORMATTER)
    logger.addHandler(stream_handler)
    
    return logger

logger.addHandler(hdlr=custom_handler())
