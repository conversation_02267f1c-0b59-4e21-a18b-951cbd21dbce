from datetime import datetime
import json
import os
import re
import time
from typing import Optional, Tuple, List, Dict

import openai
from dotenv import load_dotenv
from openai import OpenAIError
from openai.types.chat import ChatCompletion
import uuid

# Assuming you have similar modules for handling files and database operations
from tutor.executors import files
from tutor.modules.database import database
from tutor.modules.database.msql_database import Database
from tutor.modules.models import models

# Load environment variables
load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')
db = database.Database(database=models.fileio.base_db)

class WealthAccountManager:
    """Class to handle client portfolio details and transactions based on wealth management data."""

    def __init__(self, mobile: str):
        self.mobile = mobile
        self.company_name = "" # "Motilal Oswal"
        self.account_id = ""
        self.client_name = ""
        self.portfolio_value = 0.0
        self.portfolios = {}  # Stores portfolio information and values
        self.transactions = []  # List of transaction details
        self._load_client_data()

    def _load_client_data(self):
        """Loads the client-specific portfolio data, account details, and transaction histories based on the mobile number."""
        # Top 10 NSE stocks and their prices as of the knowledge cutoff (dummy prices for demonstration)
        self.stock_prices = {
            "RELIANCE INDUSTRIES": 2500.00,
            "TATA CONSULTANCY SERVICES": 3500.00,
            "HDFC BANK": 1500.00,
            "INFOSYS": 1700.00,
            "HINDUSTAN UNILEVER": 2200.00,
            "ICICI BANK": 700.00,
            "STATE BANK OF INDIA": 500.00,
            "BAJAJ FINANCE": 6000.00,
            "BHARTI AIRTEL": 700.00,
            "TATA MOTORS": 450.00
        }

        # For demonstration, we're using hardcoded data. In practice, load from a database or external source.
        client_data = {
            "+************": {
                "account_id": "P001",
                "client_name": "Mayur Badkat",
                "portfolio_value": 5000000,
                "portfolios": {
                    "TATA MOTORS": 1000,  # Quantity of shares
                    "INFOSYS": 500,
                    "HDFC BANK": 200,
                    "RELIANCE INDUSTRIES": 300,
                    "ICICI BANK": 400
                },
                "transactions": [
                    {"transaction_id": "T001", "instrument": "TATA MOTORS", "quantity": 1000, "amount": 400000, "date": "2024-09-10", "type": "buy"},
                    {"transaction_id": "T002", "instrument": "INFOSYS", "quantity": 500, "amount": 850000, "date": "2024-09-15", "type": "buy"},
                    {"transaction_id": "T003", "instrument": "HDFC BANK", "quantity": 200, "amount": 300000, "date": "2024-09-20", "type": "buy"},
                    {"transaction_id": "T004", "instrument": "RELIANCE INDUSTRIES", "quantity": 300, "amount": 750000, "date": "2024-09-21", "type": "buy"},
                    {"transaction_id": "T005", "instrument": "ICICI BANK", "quantity": 400, "amount": 240000, "date": "2024-09-22", "type": "buy"}
                ]
            },
            "+************": {
                "account_id": "P002",
                "client_name": "Nikesh Samaiya",
                "portfolio_value": ********,
                "portfolios": {
                    "STATE BANK OF INDIA": 800,
                    "BAJAJ FINANCE": 100,
                    "BHARTI AIRTEL": 600,
                    "HINDUSTAN UNILEVER": 400,
                    "TATA CONSULTANCY SERVICES": 200
                },
                "transactions": [
                    {"transaction_id": "T006", "instrument": "STATE BANK OF INDIA", "quantity": 800, "amount": 400000, "date": "2024-09-11", "type": "buy"},
                    {"transaction_id": "T007", "instrument": "BAJAJ FINANCE", "quantity": 100, "amount": 600000, "date": "2024-09-16", "type": "buy"},
                    {"transaction_id": "T008", "instrument": "BHARTI AIRTEL", "quantity": 600, "amount": 420000, "date": "2024-09-21", "type": "buy"},
                    {"transaction_id": "T009", "instrument": "HINDUSTAN UNILEVER", "quantity": 400, "amount": 880000, "date": "2024-09-22", "type": "buy"},
                    {"transaction_id": "T010", "instrument": "TATA CONSULTANCY SERVICES", "quantity": 200, "amount": 700000, "date": "2024-09-23", "type": "buy"}
                ]
            }
        }

        # Set client data to instance variables
        user_data = client_data.get(self.mobile)
        if user_data:
            self.account_id = user_data["account_id"]
            self.client_name = user_data["client_name"]
            self.portfolio_value = user_data["portfolio_value"]
            self.portfolios = user_data.get("portfolios", {})
            self.transactions = user_data["transactions"]

    def get_recent_transactions(self) -> List[Dict]:
        """Returns the client's last 5 transactions."""
        return self.transactions[-5:]  # Return last 5 transactions

    def get_recent_transactions_summary(self) -> str:
        """Returns a summary of the client's last 5 transactions as a formatted string."""
        return "\n".join(
            [f"Transaction ID: {tx['transaction_id']}, Type: {tx['type']}, Instrument: {tx['instrument']}, Quantity: {tx['quantity']}, Amount: Rupees {tx['amount']}, Date: {tx['date']}" for tx in self.get_recent_transactions()]
        )

    def get_portfolio_value(self) -> dict:
        """Returns the current portfolio value for the client's account."""
        return {
            "portfolio_value": self.portfolio_value
        }

    def get_portfolio_holdings(self) -> Dict[str, int]:
        """Returns the current portfolio holdings with quantities."""
        return self.portfolios
    
    def get_transaction_history(self) -> List[Dict]:
        """Returns the transaction history for a given account ID."""
        return self.transactions

    def validate_transaction(self, transaction_id: str) -> bool:
        """Checks if the transaction ID exists in the client's transaction history."""
        return any(tx["transaction_id"] == transaction_id for tx in self.get_transaction_history())

    def create_support_ticket(self, case_type: str, subject: str, description: str) -> dict:
        """Creates a support ticket (e.g., query, request, complaint, feedback) in the database and returns a confirmation."""
        case_id = db.insert_support_ticket(self.account_id, self.client_name, case_type, subject, description)
        return {
            "case_id": case_id,
            "status": "success",
        }

    def get_stock_price(self, instrument: str) -> float:
        """Returns the current price of the given stock."""
        return self.stock_prices.get(instrument.upper())

    def buy_stock(self, instrument: str, amount: Optional[float], quantity: Optional[int]) -> dict:
        """Executes a buy order for the specified instrument based on amount or quantity."""
        if instrument not in self.stock_prices:
            return {"status": "error", "message": f"{instrument} is not available."}

        price_per_share = self.stock_prices[instrument]

        if quantity is None and amount is not None:
            quantity = int(amount / price_per_share)
            amount = quantity * price_per_share
        elif amount is None and quantity is not None:
            amount = quantity * price_per_share
        elif amount is not None and quantity is not None:
            quantity = int(amount / price_per_share)
            amount = quantity * price_per_share
        else:
            return {"status": "error", "message": "Please provide either amount or quantity for the transaction."}

        transaction_id = f"T{len(self.transactions)+1:03d}"
        transaction = {
            "transaction_id": transaction_id,
            "instrument": instrument,
            "quantity": quantity,
            "amount": amount,
            "date": datetime.now().strftime("%Y-%m-%d"),
            "type": "buy"
        }
        self.transactions.append(transaction)
        self.portfolio_value += amount
        self.portfolios[instrument] = self.portfolios.get(instrument, 0) + quantity
        return {
            "status": "success",
            "transaction": transaction,
            "amount": amount,
            "quantity": quantity
        }

    def sell_stock(self, instrument: str, amount: Optional[float], quantity: Optional[int]) -> dict:
        """Executes a sell order for the specified instrument based on amount or quantity."""
        if instrument not in self.stock_prices:
            return {"status": "error", "message": f"{instrument} is not available."}

        if instrument not in self.portfolios or self.portfolios[instrument] == 0:
            return {"status": "error", "message": f"You do not own any shares of {instrument}."}

        price_per_share = self.stock_prices[instrument]

        if quantity is None and amount is not None:
            quantity = int(amount / price_per_share)
            amount = quantity * price_per_share
        elif amount is None and quantity is not None:
            amount = quantity * price_per_share
        elif amount is not None and quantity is not None:
            quantity = int(amount / price_per_share)
            amount = quantity * price_per_share
        else:
            return {"status": "error", "message": "Please provide either amount or quantity for the transaction."}

        if quantity > self.portfolios[instrument]:
            return {"status": "error", "message": f"You do not have enough shares of {instrument} to sell."}

        transaction_id = f"T{len(self.transactions)+1:03d}"
        transaction = {
            "transaction_id": transaction_id,
            "instrument": instrument,
            "quantity": quantity,
            "amount": amount,
            "date": datetime.now().strftime("%Y-%m-%d"),
            "type": "sell"
        }
        self.transactions.append(transaction)
        self.portfolio_value -= amount
        self.portfolios[instrument] -= quantity
        return {
            "status": "success",
            "transaction": transaction,
            "amount": amount,
            "quantity": quantity
        }
    
class WealthAssistant:
    """AI-powered assistant class to handle wealth management services, including buying and selling stocks."""

    def __init__(self, logger, mobile: str, session: str, full_name: str, loop: None, target: str):
        self.logger = logger
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.conversation_history = []
        self.authenticated = False
        self.model = "gpt-4o-mini"  # Adjust as needed
        self.mobile = mobile
        self.client = openai
        self.wealth_manager = WealthAccountManager(mobile)  # Initialize account manager with client's mobile number
        self.session = session
        self.user_level = "beginner"
        self.greeting = f"Hello {self.first_name}, welcome to {self.wealth_manager.company_name} Wealth Management! How can I assist you today?"
        self.functions = self._create_functions()  # Initialize the functions

    def _create_functions(self) -> List[Dict]:
        """Define the functions to handle portfolio inquiries, transaction history requests, buying and selling stocks, and support ticket creation."""
        return [
            {
                "name": "manage_portfolio",
                "description": "Handles portfolio inquiries, transaction history requests, buying and selling stocks, and support ticket creation by inferring details from the client's input.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "operation": {
                            "type": "string",
                            "enum": ["portfolio_inquiry", "transaction_history", "buy_stock", "sell_stock", "create_support_ticket"],
                            "description": "The type of operation inferred from the client's request."
                        },
                        "instrument": {
                            "type": "string",
                            "description": "The financial instrument involved in the transaction (e.g., 'TATA MOTORS', 'INFOSYS'). Required for buy_stock and sell_stock operations."
                        },
                        "amount": {
                            "type": "number",
                            "description": "The amount of the transaction in Rupees. Required for buy_stock and sell_stock operations and if 'quantity' is not provided."
                        },
                        "quantity": {
                            "type": "integer",
                            "description": "The quantity of shares for the transaction. Required for buy_stock and sell_stock operations and if 'amount' is not provided."
                        },
                        "ticket_type": {
                            "type": "string",
                            "enum": ["query", "request", "complaint", "feedback"],
                            "description": "The inferred type of support ticket. Required for 'create_support_ticket' operation."
                        },
                        "subject": {
                            "type": "string",
                            "description": "The inferred subject of the support ticket. Required for 'create_support_ticket' operation."
                        },
                        "description": {
                            "type": "string",
                            "description": "The detailed description, as inferred from the client's input. Required for 'create_support_ticket' operation."
                        },
                        "confirmed": {
                            "type": "boolean",
                            "description": "Indicates whether the client has confirmed the description. Required for 'create_support_ticket' operation."
                        }
                    },
                    "required": ["operation"]
                }
            }
        ]

    def handle_response(self, assistant_response: ChatCompletion):
        """Processes the assistant's response and takes necessary actions."""
        function_call = assistant_response.choices[0].message.function_call
        if function_call and function_call.name == "manage_portfolio":
            # Modify here to include role and name
            message = assistant_response.choices[0].message
            message_dict = {
                "role": message.role,
                "name": function_call.name,
                "content": message.content,
                "function_call": message.function_call
            }
            self.conversation_history.append(message_dict)

            arguments = json.loads(function_call.arguments)
            operation = arguments.get("operation")
            instrument = arguments.get("instrument", "")
            amount = arguments.get("amount", 0)
            quantity = arguments.get("quantity", 0)
            ticket_type = arguments.get("ticket_type", "")
            subject = arguments.get("subject", "")
            description = arguments.get("description", "")
            confirmed = arguments.get("confirmed", False)

            if operation == "portfolio_inquiry":
                portfolio_value = self.wealth_manager.get_portfolio_value()
                holdings = self.wealth_manager.get_portfolio_holdings()
                portfolio_info = {
                    "portfolio_value": portfolio_value["portfolio_value"],
                    "holdings": holdings
                }
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": json.dumps(portfolio_info)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)

            elif operation == "transaction_history":
                transactions = self.wealth_manager.get_transaction_history()
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": json.dumps(transactions)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)

            elif operation == "buy_stock":
                if not instrument or amount <= 0:
                    return "Please provide a valid instrument and amount to proceed with buying stock."
                buy_response = self.wealth_manager.buy_stock(instrument, amount, quantity)
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": json.dumps(buy_response)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)

            elif operation == "sell_stock":
                if not instrument or amount <= 0:
                    return "Please provide a valid instrument and amount to proceed with selling stock."
                sell_response = self.wealth_manager.sell_stock(instrument, amount, quantity)
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": json.dumps(sell_response)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)

            elif operation == "create_support_ticket":
                if not ticket_type or not subject or not description:
                    return "Please provide the ticket type, subject, and description for creating a support ticket."
                ticket_response = self.wealth_manager.create_support_ticket(ticket_type, subject, description)
                self.conversation_history.append({"role": "function", "name": function_call.name, "content": json.dumps(ticket_response)})
                chat_response = self._get_chat_response(self.conversation_history)
                return self.handle_response(chat_response)

        return assistant_response.choices[0].message.content

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Client"

    def start_new_session(self):
        self.authenticate()
        if not self.authenticated:
            raise Exception("Authentication failed.")
        return self.greeting

    def authenticate(self) -> None:
        """Authenticates and prepares the session for wealth management services."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'OPENAI_API_KEY' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            self._get_chat_response(self.conversation_history)
            self.authenticated = True
        except OpenAIError as error:
            self.logger.error(error)
        except Exception as error:
            self.logger.critical(error)

    def query(self, sentence: str, context: str) -> str:
        """Handles client queries such as portfolio inquiries, buying, and selling stocks."""
        self.conversation_history.append(self._create_message("user", sentence))

        messages_for_llm = self.conversation_history[-12:]
        system_message = self._create_message("system", self._generate_initial_content())
        if len(messages_for_llm) == 0 or messages_for_llm[0]['role'] != "system":
            messages_for_llm.insert(0, system_message)
        else:
            messages_for_llm[0] = system_message

        response, _ = self._get_chat_response_with_timing(messages_for_llm)
        if response:
            self.conversation_history.append(self._create_message("assistant", response))
        return response

    def _get_chat_response_with_timing(self, messages) -> Tuple[str, float]:
        """Gets a response from the GPT model and tracks the time taken."""
        try:
            start_time = time.time()
            chat_response = self._get_chat_response(messages)
            end_time = time.time()
            response_time = end_time - start_time
        except OpenAIError as error:
            self.logger.error(error)
            return "Sorry, I can't process your request right now.", 0

        if chat_response.choices:
            return self.handle_response(chat_response), response_time
        else:
            self.logger.error(chat_response)
            return "I'm sorry, I'm having trouble processing your request.", 0

    def _generate_initial_content(self) -> str:
        """Generates the system prompt with available portfolios and company name."""
        current_date_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        base_instructions = (
            f'You are a wealth assistant named "Lisa", providing wealth management services for {self.wealth_manager.company_name}. '
            "You are a professional and courteous wealth advisor assisting clients via voice call. "
            "The client is a non-native English speaker, and the transcribed input may contain errors due to mispronunciations. "
            "Please interpret the client's input carefully, considering possible mispronunciations, and seek clarification if needed. "
            "Respond only in English. Your responses will be audio-fed, so keep them concise within 1 to 2 sentences without any parentheses.\n"
            f"Today's date and time is {current_date_time}.\n\n"
        )

        portfolio_details = f"Client Name: {self.wealth_manager.client_name}, Portfolio Value: Rupees {self.wealth_manager.portfolio_value}"
        holdings = "\n".join([f"{instrument}: {quantity} shares" for instrument, quantity in self.wealth_manager.get_portfolio_holdings().items()])
        portfolio_info = (
            f"Here is the client's portfolio:\n{portfolio_details}\nHoldings:\n{holdings}\n\n" if holdings else "No client portfolio available.\n\n"
        )

        # Provide recent transactions if available
        recent_transactions = self.wealth_manager.get_recent_transactions_summary()
        transactions_info = (
            f"Here are the Recent Transactions:\n{recent_transactions}\n\n" if recent_transactions else "No recent transactions available.\n\n"
        )

        # Include top 10 NSE stocks and their prices
        stock_prices_info = "Top 10 NSE Stocks and their Current Prices:\n"
        for stock, price in self.wealth_manager.stock_prices.items():
            stock_prices_info += f"- {stock}: Rupees {price} per share\n"
        stock_prices_info += "\n"


        # Common instructions for handling out-of-scope queries
        common_instructions = (
            "- Keep the conversation short and simple.\n"
            "- Provide feedback if any information is missing or needs clarification. Respond clearly if the client's input is out of scope.\n"
        )

        specific_instructions = (
            "\n\nInstructions:\n"
            "- Assist clients with portfolio inquiries, transaction history requests, buying and selling stocks, and support ticket creation.\n"
            "- Infer the client's intended operation (portfolio inquiry, transaction history, buy stock, sell stock, create support ticket) from their input without asking them directly.\n"
            "- If buying or selling stock:\n"
            "   - Confirm the instrument and either amount or quantity with the client before proceeding.\n"
            "   - If the client provides amount, calculate the quantity based on the current stock price.\n"
            "   - If the client provides quantity, calculate the amount based on the current stock price.\n"
            "   - The available instruments are top NSE stocks like TATA MOTORS, RELIANCE INDUSTRIES, etc.\n"
            "   - Provide a summary of the transaction once completed.\n"
            "- If creating a support ticket:\n"
            "   - Analyze the client's message to determine the ticket type (query, request, complaint, feedback).\n"
            "   - Create an appropriate subject that summarizes the client's input.\n"
            "   - Generate a detailed description based on the client's input.\n"
        )

        # Combine all parts to form the final prompt
        prompt = (
            f"{base_instructions}"
            f"{portfolio_info}"
            f"{transactions_info}"
            f"{stock_prices_info}"
            f"{specific_instructions}"
            f"{common_instructions}"
        )

        return prompt

    def _get_chat_response(self, messages) -> ChatCompletion:
        """Gets the chat response from the OpenAI API."""
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.model,
            temperature=0.3,
            max_tokens=150,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.6,
            functions=self.functions,
            function_call="auto"
        )
        return response

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}

    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file."""
        data = files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)
