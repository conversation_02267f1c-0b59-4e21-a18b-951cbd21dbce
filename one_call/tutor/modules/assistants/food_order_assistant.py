import json
import os
import re
import time
from threading import Thread
from typing import <PERSON><PERSON>, <PERSON><PERSON>, List

import openai
from dotenv import load_dotenv
from openai import OpenAIError
from openai import OpenAI
from openai.types.chat.chat_completion import ChatCompletion

from tutor.executors import static_responses, files
from tutor.executors.static_responses import un_processable
from tutor.modules.exceptions import MissingEnvVars

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')

class FoodOrderAssistant:
    def __init__(self, logger, mobile: str, session: str, full_name: str, loop: None):
        self.logger = logger
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.conversation_history = []
        self.authenticated = False
        self.model = "gpt-3.5-turbo"
        clear_model = re.sub(r'\W', '_', self.model)
        self.file_name = f"{clear_model}.yaml"
        self.mobile = mobile
        self.client = OpenAI()
        self.functions = self._create_functions()
        self.menu = self._initialize_menu()
        self.session = session
        self.user_level = "beginner"
        self.greeting = f"Hello {self.first_name}, welcome to our restaurant! How can I assist you today?"

    def _initialize_menu(self) -> dict:
        """Initializes the menu with items."""
        return {
            "Burger": 99,
            "Pizza": 399,
            "Pasta": 199,
            "Salad": 49,
            "Fries": 299,
            "Ice Cream": 99,
            "Coffee": 49,
            "Soda": 19
        }

    def _create_functions(self):
        """Define the functions to handle food ordering process."""
        return [
            {
                "name": "place_order",
                "description": "Takes the customer’s order and processes it.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "order_items": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of items the customer wants to order."
                        }
                    },
                    "required": ["order_items"]
                }
            },
            {
                "name": "confirm_order",
                "description": "Confirms the customer's order and provides delivery details.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "estimated_time": {
                            "type": "string",
                            "description": "Estimated delivery or pickup time."
                        }
                    },
                    "required": ["estimated_time"]
                }
            },
            {
                "name": "inform_delay",
                "description": "Informs the customer about a delay in their order.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "delay_time": {
                            "type": "string",
                            "description": "Time the order will be delayed by."
                        }
                    },
                    "required": ["delay_time"]
                }
            },
            {
                "name": "suggest_alternatives",
                "description": "Suggests alternative menu items if the requested item is unavailable.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "unavailable_item": {"type": "string"},
                        "suggestions": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of alternative items."
                        }
                    },
                    "required": ["unavailable_item", "suggestions"]
                }
            },
            {
                "name": "get_menu",
                "description": "Displays the available menu items to the customer.",
                "parameters": {
                    "type": "object",
                    "properties": {}
                }
            }
        ]

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Customer"

    def start_new_session(self):
        self.authenticate()
        if not self.authenticated:
            raise MissingEnvVars
        return self.greeting

    def authenticate(self) -> None:
        """Authenticates and prepares the session for food ordering."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'openai_api' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            chat_response = self._get_chat_response(self.conversation_history)
            self.authenticated = True
        except OpenAIError as error:
            self.logger.error(error)
        except Exception as error:
            self.logger.critical(error)

    def query(self, sentence: str, context: str) -> str:
        response_time = 0.0
        sentence = sentence.lower()

        self.conversation_history.append(self._create_message("user", sentence))

        # Prepare messages for the LLM
        messages_for_llm = self.conversation_history[-1000:]  # Last 6 messages to keep within token limits

        self.english_context = context
        system_message = self._create_message("system", self._generate_initial_content())
        messages_for_llm.insert(0, system_message)

        response, response_time = self._get_chat_response_with_timing(messages_for_llm)

        if response:
            self.conversation_history.append(self._create_message("assistant", response))

        return response

    def _get_chat_response_with_timing(self, messages) -> tuple:
        try:
            start_time = time.time()  # Start timing
            chat_response = self._get_chat_response(messages)
            end_time = time.time()  # End timing
            response_time = end_time - start_time  # Calculate the duration
        except OpenAIError as error:
            self.logger.error(error)
            return un_processable("Customer"), '0'

        if chat_response.choices:
            return self.handle_response(chat_response), response_time
        else:
            self.logger.error(chat_response)
            return un_processable("Customer"), '0'

    def handle_response(self, assistant_response: ChatCompletion):
        function_call = assistant_response.choices[0].message.function_call

        if function_call and function_call.name == "place_order":
            order_items = json.loads(function_call.arguments).get("order_items", [])
            unavailable_items = [item for item in order_items if item not in self.menu]
            if unavailable_items:
                suggestions = self._suggest_alternatives(unavailable_items)
                return f"Sorry, {', '.join(unavailable_items)} are not available. You could try {', '.join(suggestions)} instead."
            return f"Your order for {', '.join(order_items)} has been placed successfully."
        
        elif function_call and function_call.name == "confirm_order":
            estimated_time = json.loads(function_call.arguments).get("estimated_time")
            return f"Your order is confirmed. It will be ready in {estimated_time}."
        
        elif function_call and function_call.name == "inform_delay":
            delay_time = json.loads(function_call.arguments).get("delay_time")
            return f"Unfortunately, your order is delayed by {delay_time}. Sorry for the inconvenience."

        elif function_call and function_call.name == "suggest_alternatives":
            unavailable_item = json.loads(function_call.arguments).get("unavailable_item")
            suggestions = json.loads(function_call.arguments).get("suggestions", [])
            return f"Sorry, {unavailable_item} is unavailable. How about trying {', '.join(suggestions)} instead?"

        elif function_call and function_call.name == "get_menu":
            return f"Here's our menu: {', '.join([f'{item} (Rs {self.menu[item]:.2f})' for item in self.menu])}"

        return assistant_response.choices[0].message.content

    def _suggest_alternatives(self, unavailable_items: List[str]) -> List[str]:
        """Suggests alternatives based on the menu."""
        return list(self.menu.keys())[:3]  # Example of suggesting the first 3 items from the menu

    def _generate_initial_content(self) -> str:
        """Generates the initial content for food order assistant."""
        return (
            f"Hello {self.first_name}, welcome to our restaurant! "
            "I’m here to help you place your order. "
            "Please let me know what you would like to order today. "
            "You can also ask for our menu."
        )

    def _get_chat_response(self, messages) -> ChatCompletion:
        """Gets the chat response from the OpenAI API."""
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.model,
            temperature=0.7,
            max_tokens=150,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.6,
            functions=self.functions,
            function_call="auto"
        )
        return response

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}
    
    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file."""
        data = files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)
