import json
import os
import re
import time
from threading import Thread
from typing import Optional, Tuple, List, Dict

import openai
from dotenv import load_dotenv
from openai import OpenAIError
from openai import OpenAI
from openai.types.chat.chat_completion import ChatCompletion

from tutor.executors import files

# Load environment variables
load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')

class RestaurantMenu:
    """Class to handle restaurant categories and menu items based on RestaurantId."""
    def __init__(self, restaurant_id: str):
        self.restaurant_id = restaurant_id
        self.restaurant_name =""
        self.categories = {}
        self.menu_items = {}
        self._load_menu()

    def _load_menu(self):
        """Loads the menu for the given restaurant_id."""
        # For simplicity, we'll define hardcoded menus for different restaurant IDs
        if self.restaurant_id == "+919999999991" or self.restaurant_id == "restaurant_order_assistant":
            self.restaurant_name = "Sai IceCream Shop"
            self.categories = {
                1: "flavors",
                2: "toppings",
            }
            self.menu_items = {
                1: [
                    {"itemName": "Strawberry", "count": 20, "price": 90},
                    {"itemName": "Chocolate", "count": 15, "price": 160},
                ],
                2: [
                    {"itemName": "Hot Fudge", "count": 50, "price": 80},
                    {"itemName": "Sprinkles", "count": 100, "price": 100},
                    {"itemName": "Whipped Cream", "count": 100, "price": 100},
                ]
            }
        elif self.restaurant_id == "+919999999992":
            self.restaurant_name = "Pizza Palace"
            self.categories = {
                1: "Pizza",
                2: "Sides",
                3: "Beverages"
            }
            self.menu_items = {
                1: [
                    {"itemName": "Margherita Pizza", "count": 20, "price": 350},
                    {"itemName": "Pepperoni Pizza", "count": 15, "price": 400},
                ],
                2: [
                    {"itemName": "Garlic Bread", "count": 50, "price": 80},
                    {"itemName": "French Fries", "count": 100, "price": 100},
                ],
                3: [
                    {"itemName": "Coke", "count": 100, "price": 50},
                    {"itemName": "Orange Juice", "count": 80, "price": 70},
                ]
            }
        elif self.restaurant_id == "+************":
            self.restaurant_name = "Curry House"
            self.categories = {
                1: "Indian Curries",
                2: "Breads",
                3: "Desserts"
            }
            self.menu_items = {
                1: [
                    {"itemName": "Butter Chicken", "count": 30, "price": 300},
                    {"itemName": "Paneer Butter Masala", "count": 25, "price": 250},
                ],
                2: [
                    {"itemName": "Naan", "count": 100, "price": 20},
                    {"itemName": "Roti", "count": 200, "price": 15},
                ],
                3: [
                    {"itemName": "Gulab Jamun", "count": 50, "price": 80},
                    {"itemName": "Ice Cream", "count": 60, "price": 100},
                ]
            }
        else:
            # Default menu if restaurant_id is not recognized
            self.categories = {}
            self.menu_items = {}

    def get_menu_details(self) -> str:
        """Generates a string representation of the menu."""
        menu_details = ""
        for category_id, category_name in self.categories.items():
            menu_details += f"{category_name}:\n"
            items = self.menu_items.get(category_id, [])
            for item in items:
                # menu_details += f"  {item['itemName']} (Rs{item['price']}, Available: {item['count']})\n"
                menu_details += f"  {item['itemName']} (INR {item['price']})\n"
        return menu_details

    def is_item_available(self, item_name: str) -> bool:
        """Checks if an item is available in any category."""
        for items in self.menu_items.values():
            for item in items:
                if item_name.lower() == item["itemName"].lower() and item["count"] > 0:
                    return True
        return False

    def get_item_price(self, item_name: str) -> float:
        """Gets the price of the item."""
        for items in self.menu_items.values():
            for item in items:
                if item_name.lower() == item["itemName"].lower():
                    return item["price"]
        return 0.0

class RestaurantOrderAssistant:
    """Assistant class to handle restaurant orders using OpenAI's GPT models."""
    def __init__(self, logger, mobile: str, session: str, full_name: str, loop: None, target: str):
        self.logger = logger
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.conversation_history = []
        self.authenticated = False
        self.model = "gpt-3.5-turbo"
        clear_model = re.sub(r'\W', '_', self.model)
        self.file_name = f"{clear_model}.yaml"
        self.mobile = mobile
        self.client = OpenAI()
        self.functions = self._create_functions()
        self.menu = RestaurantMenu(target)  # Initialize the menu based on restaurant_id
        self.special_offers = self._initialize_special_offers()
        self.session = session
        self.delivery_address = None  # Stores the delivery address
        self.user_level = "beginner"
        self.greeting = f"Hello {self.first_name}, welcome to {self.menu.restaurant_name}! How can I assist you today?"

    def _initialize_special_offers(self) -> dict:
        """Initializes special offers for the restaurant."""
        # This could also be based on restaurant_id if necessary
        return {
            "offers": [
                {
                    "offerName": "Weekend Feast",
                    "details": "Get a free side dish with any order above Rs500 on weekends."
                },
                {
                    "offerName": "Happy Hour Discount",
                    "details": "20% off on all orders between 4 PM to 6 PM every day."
                },
            ]
        }

    def _create_functions(self):
        """Define the functions to handle food ordering process."""
        return [
            {
                "name": "place_order",
                "description": "Takes the customer’s order and processes it, including delivery address.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "items": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "List of items the customer wants to order."
                        },
                        "delivery_address": {
                            "type": "string",
                            "description": "The address where the order should be delivered."
                        }
                    },
                    "required": ["items", "delivery_address"]
                }
            }
        ]

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Customer"

    def start_new_session(self):
        self.authenticate()
        if not self.authenticated:
            raise Exception("Authentication failed.")
        return self.greeting

    def authenticate(self) -> None:
        """Authenticates and prepares the session for food ordering."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'OPENAI_API_KEY' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            self._get_chat_response(self.conversation_history)
            self.authenticated = True
        except OpenAIError as error:
            self.logger.error(error)
        except Exception as error:
            self.logger.critical(error)


    def query(self, sentence: str, context: str) -> str:
        sentence = sentence.lower()

        self.conversation_history.append(self._create_message("user", sentence))

        # Prepare messages for the LLM
        messages_for_llm = self.conversation_history[-12:]  # Last 12 messages to keep within token limits

        self.english_context = context
        system_message = self._create_message("system", self._generate_initial_content())

        # Ensure the system message is always at position 0 and is updated
        if len(messages_for_llm) == 0 or messages_for_llm[0]['role'] != "system":
            messages_for_llm.insert(0, system_message)
        else:
            messages_for_llm[0] = system_message  # Update the system message if already at position 0

        response, _ = self._get_chat_response_with_timing(messages_for_llm)

        if response:
            self.conversation_history.append(self._create_message("assistant", response))

        return response



    def _get_chat_response_with_timing(self, messages) -> Tuple[str, float]:
        try:
            start_time = time.time()  # Start timing
            chat_response = self._get_chat_response(messages)
            end_time = time.time()    # End timing
            response_time = end_time - start_time  # Calculate the duration
        except OpenAIError as error:
            self.logger.error(error)
            return "I'm sorry, I'm having trouble processing your request.", 0

        if chat_response.choices:
            return self.handle_response(chat_response), response_time
        else:
            self.logger.error(chat_response)
            return "I'm sorry, I'm having trouble processing your request.", 0

    def handle_response(self, assistant_response: ChatCompletion):
        function_call = assistant_response.choices[0].message.function_call

        if function_call and function_call.name == "place_order":
            arguments = json.loads(function_call.arguments)
            items = arguments.get("items", [])
            self.delivery_address = arguments.get("delivery_address")
            return self._process_order(items)

        return assistant_response.choices[0].message.content

    def _process_order(self, items: List[str]) -> str:
        """Processes the order, checks availability, calculates total price, and confirms the order."""
        unavailable_items = [item for item in items if not self.menu.is_item_available(item)]

        if unavailable_items:
            unavailable_message = f"The following items are unavailable: {', '.join(unavailable_items)}."
            return unavailable_message

        # Calculate total price
        total_price = self._calculate_price(items)

        return (
            f"Your order for items: {', '.join(items)} "
            f"has been placed successfully. The total price is Rs{total_price:.2f}. "
            f"The order will be delivered to: {self.delivery_address}."
        )

    def _calculate_price(self, items: List[str]) -> float:
        """Calculates the total price for the selected items."""
        total = sum(self.menu.get_item_price(item) for item in items)
        return total

    def _generate_initial_content(self) -> str:
        """Generates the system prompt with the menu, prices, and special offers."""
        menu_details = self.menu.get_menu_details()
        offers = [f"{offer['offerName']}: {offer['details']}" for offer in self.special_offers["offers"]]
        special_offer_details = "\nSpecial Offers:\n" + "\n".join(offers)

        return (            
            'You are a restaurant assistant chatbot named "Pooja". '
            "You are a helpful customer care representative for {self.menu.restaurant_name}, connecting with user via voice call. "
            "Your responses will be audio-fed, so keep them concise within 1 to 2 sentences without any parentheses. "
            "Your expertise is in assisting with restaurant orders, answering menu-related questions, and processing customer orders. "
            "Once the user confirms their items, ask them for their delivery address to complete the order. "
            "Mention special offers only if the user asks about them or shows interest."
            f"\n\nHere is the current menu and offers:\n\n{menu_details}\n{special_offer_details}"
        )

    def _get_chat_response(self, messages) -> ChatCompletion:
        """Gets the chat response from the OpenAI API."""
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.model,
            temperature=0.3,
            max_tokens=150,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.6,
            functions=self.functions,
            function_call="auto"
        )
        return response

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}

    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file."""
        data = files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)

# Example usage:
if __name__ == "__main__":
    # Mock logger and other dependencies
    class Logger:
        def warning(self, msg): print(f"WARNING: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def critical(self, msg): print(f"CRITICAL: {msg}")

    logger = Logger()
    assistant = RestaurantOrderAssistant(
        logger=logger,
        mobile="1234567890",
        session="session123",
        full_name="John Doe",
        loop=None,
        restaurant_id=1  # Specify the RestaurantId here
    )

    # Start a new session
    greeting = assistant.start_new_session()
    print(greeting)

    # Simulate a user query
    user_input = "I would like to order a Margherita Pizza and Garlic Bread."
    response = assistant.query(user_input, context="")
    print(response)

    # Simulate providing delivery address
    user_input = "My address is 123 Main Street."
    response = assistant.query(user_input, context="")
    print(response)
