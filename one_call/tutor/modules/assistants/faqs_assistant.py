import json
import os
import time
from typing import Op<PERSON>, Tuple, List, Dict, Any
import openai
from dotenv import load_dotenv
from openai import OpenAIError
from openai.types.chat.chat_completion import ChatCompletion
from tutor.api.faqs_service import FaqsService, FAQsData
from tutor.executors import files
from threading import Thread

# Load environment variables
load_dotenv()
# Ensure OPENAI_API_KEY is always a string if it exists
openai_api_key = os.getenv('OPENAI_API_KEY')
if openai_api_key:
    os.environ["OPENAI_API_KEY"] = openai_api_key

class FaqsAssistant:
    """AI-powered assistant class to handle FAQ queries."""

    def __init__(self, logger, mobile: str, session: str, full_name: str, loop: None, target: str):
        self.logger = logger
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.conversation_history = []
        self.authenticated = False
        self.model = "gpt-4o-mini"  # "gpt-3.5-turbo" "gpt-4o" "gpt-4o-mini"
        self.mobile = mobile
        self.client = openai
        self.faqs_service = FaqsService(logger, loop)  # Initialize FAQ service
        self.session = session
        self.user_level = "beginner"
        self.greeting = f"Hello {self.first_name}, how can I help you with your questions today?"
        self.ongo_context = ""
        self.loop = loop
        self.target = target 

    def handle_response(self, assistant_response: ChatCompletion):
        """Processes the assistant's response."""
        return str(assistant_response.choices[0].message.content)

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Customer"

    def start_new_session(self):
        self.authenticate()
        if not self.authenticated:
            raise Exception("Authentication failed.")
        return self.greeting

    def authenticate(self) -> None:
        """Authenticates and prepares the session for FAQ services."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'OPENAI_API_KEY' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            self._get_chat_response(self.conversation_history)
            self.authenticated = True
        except OpenAIError as error:
            self.logger.error(error)
        except Exception as error:
            self.logger.critical(error)

    async def query(self, sentence: str, context: str) -> str:
        """Handles user queries for FAQs."""
        self.conversation_history.append(self._create_message("user", sentence))

        is_faqs, context = await self._handle_faqs_query(sentence)
        self.ongo_context = context # Set the context here

        messages_for_llm = self.conversation_history[-12:]
        system_message = self._create_message("system", self._generate_initial_content())
        if len(messages_for_llm) == 0 or messages_for_llm[0]['role'] != "system":
            messages_for_llm.insert(0, system_message)
        else:
            messages_for_llm[0] = system_message

        response_time = 0.0 # Initialize response_time
        response, response_time = self._get_chat_response_with_timing(messages_for_llm)
        if response:
            # Ensure response is a string before appending
            if isinstance(response, str):
                self.conversation_history.append(self._create_message("assistant", response))
            else:
                self.logger.warning(f"Unexpected response type from LLM: {type(response)}. Content: {response}")
                self.conversation_history.append(self._create_message("assistant", str(response)))
            Thread(target=self._dump_history, args=(sentence, response, f"{response_time:.2f}")).start()
        return response

    def _get_chat_response_with_timing(self, messages) -> Tuple[str, float]:
        """Gets a response from the GPT model and tracks the time taken."""
        try:
            start_time = time.time()
            chat_response = self._get_chat_response(messages)
            end_time = time.time()
            response_time = end_time - start_time
        except OpenAIError as error:
            self.logger.error(error)
            return "Sorry, I can't process your request right now.", 0

        if chat_response.choices:
            return self.handle_response(chat_response), response_time
        else:
            self.logger.error(chat_response)
            return "I'm sorry, I'm having trouble processing your request.", 0

    def _generate_initial_content(self) -> str:
        """Generates the system prompt for the FAQ assistant."""
        base_instructions = (
            f'You are an Finacle Core banking Solution FAQ assistant for {self.first_name}, providing answers to frequently asked questions. '
            "You are a professional and courteous customer care representative assisting users via voice call. "
            "The user might be a non-native English speaker, and transcribed input may contain errors. "
            "Interpret the user's input carefully and seek clarification if needed. "
            "Respond only in English. Your responses will be audio-fed, so keep them concise within 1 to 4 sentences without any parentheses.\n"
        )

        specific_instructions = (
            "Your responsibilities:\n"
            "- Review the 'Context from FAQs' section below. This section contains potential answers retrieved from our knowledge base based on the user's query.\n"
            "- Synthesize the best possible answer for the user using this information. You may select the most relevant answer, combine information if appropriate, or indicate if none of the provided answers are suitable.\n"
            "- Do not rely on external knowledge or make up information not found in the context.\n"
            "- If the provided context does not adequately answer the question, state that you couldn't find a specific answer in the FAQs and suggest rephrasing or asking a different question.\n"
            "\n"
            "Guidelines for your answers:\n"
            "- Be clear, concise, and helpful.\n"
            "- Maintain a professional and respectful tone.\n"
            "- Do not mention that you are an AI or describe your behavior.\n"
            "- Avoid phrases like 'Based on the context provided...' or 'According to the FAQs...'. Directly provide the answer as if it's your own knowledge derived from the system.\n"
            "\n"
            "Example behaviors:\n"
            "- ✅ If the context includes relevant data, answer the question directly using that data.\n"
            "- ❌ If the answer is not found in the provided context, do not make it up. Instead, acknowledge the gap and, if possible, suggest what might help (e.g., rephrasing the question).\n"
            "\n"
            "Always prioritize helpfulness and accuracy based on the provided FAQ context."
        )

        # Format the context properly
        ongo_context_formatted = f"Context:\n{self.ongo_context}\n\n" if self.ongo_context else ""

        # Combine all parts to form the final prompt
        prompt = (
            f"{base_instructions}"
            f"{ongo_context_formatted}"
            f"{specific_instructions}"
        )
        return prompt

    def _get_chat_response(self, messages) -> ChatCompletion:
        """Gets the chat response from the OpenAI API."""
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.model,
            temperature=0.3,
            max_tokens=150,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.6,
        )
        return response

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}

    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file.

        Args:
            request: The request from the user.
            response: The response from the assistant.
            response_time: The time taken for the response.
        """
        clear_model = self.model.replace('.', '_').replace('-', '_')
        file_name = f"{clear_model}.yaml"
        data = files.load_yaml_from_session(self.mobile, self.session, file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, file_name, data)

    async def _handle_faqs_query(self, phrase: str) -> Tuple[bool, str]:
        """Handles queries by fetching top K matches from the FaqsService and formatting them as context.

        :param phrase: The query phrase from the user.
        :return: A tuple containing a boolean indicating if a match was found
                 and a string representing the context (formatted matches or a 'not found' message).
        """
        # Fetch top 3 matches to provide richer context to the LLM
        top_matches_result = await self.faqs_service.get_top_matches(phrase, k=3)
        
        if top_matches_result:
            matches_list, _ = top_matches_result # We don't need overall_response_time here
            if matches_list:
                context_parts = ["Context from FAQs:"]
                for i, match_data in enumerate(matches_list):
                    context_parts.append(
                        f"{i+1}. Answer: \"{match_data.answer}\" (Source: {match_data.source}, Accuracy: {match_data.accuracy:.2f})"
                    )
                return True, "\n".join(context_parts)
            else:
                # No matches found by the service
                return False, "I couldn't find any specific information for that in our FAQs. Could you try rephrasing or asking something else?"
        else:
            # Error occurred in FaqsService
            self.logger.error(f"Error fetching top matches for query: {phrase} from FaqsService.")
            return False, "I encountered an issue trying to find an answer in our FAQs. Please try again shortly."
