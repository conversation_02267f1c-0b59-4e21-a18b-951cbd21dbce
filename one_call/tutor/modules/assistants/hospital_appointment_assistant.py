from datetime import datetime
import json
import os
import re
import time
from typing import Optional, Tuple, List, Dict

import openai
from dotenv import load_dotenv
from openai import OpenAIError
from openai.types.chat.chat_completion import ChatCompletion
from tutor.executors import files

# Load environment variables
load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')


class HospitalAppointmentManager:
    """Class to handle patient appointments and rescheduling based on hospital data."""
    
    def __init__(self, hospital_id: str):
        self.hospital_id = hospital_id
        self.hospital_name = ""
        self.doctors = {}  # Stores doctor names and specialties
        self.appointments = {}  # Key: doctor_id, Value: List of appointment slots
        self._load_hospital_data()

    def _load_hospital_data(self):
        """Loads the hospital data and appointment schedules."""
        # For simplicity, we'll define hardcoded doctors and appointments for different hospital IDs
        if self.hospital_id == "+919899999991":
            self.hospital_name = "City Hospital"
            self.doctors = {
                1: {"name": "Dr. <PERSON><PERSON>", "specialty": "Cardiology"},
                2: {"name": "Dr. <PERSON><PERSON>", "specialty": "Orthopedics"},
            }
            self.appointments = {
                1: ["10:00 AM", "11:00 AM", "2:00 PM"],  # Slots for Dr. <PERSON>esh Kumar
                2: ["9:00 AM", "12:00 PM", "3:00 PM"],   # Slots for Dr. Priya Mehta
            }
        elif self.hospital_id == "+919899999992":
            self.hospital_name = "Metro Medical Center"
            self.doctors = {
                1: {"name": "Dr. Aditi Sharma", "specialty": "Dermatology"},
                2: {"name": "Dr. Vivek Patel", "specialty": "Pediatrics"},
            }
            self.appointments = {
                1: ["9:30 AM", "11:30 AM", "1:30 PM"],
                2: ["10:00 AM", "12:30 PM", "3:30 PM"],
            }
        else:
            self.doctors = {}
            self.appointments = {}


    def get_available_slots(self, doctor_id: int) -> List[str]:
        """Returns a list of available appointment slots for a given doctor."""
        return self.appointments.get(doctor_id, [])

    def is_slot_available(self, doctor_id: int, time_slot: str, appointment_date: str) -> bool:
        """Checks if a specific time slot is available for the given doctor and appointment date."""
        # Ensure the appointment date is not in the past
        current_date = datetime.now().date()
        input_date = datetime.strptime(appointment_date, "%Y-%m-%d").date()

        if input_date < current_date:
            return False  # Appointment date is in the past, return False
        
        return time_slot in self.appointments.get(doctor_id, [])
    
    def validate_time(self, appointment_time: str, appointment_date: str) -> Tuple[bool, Optional[str]]:
        """Validates the appointment time to ensure it is not in the past."""
        try:
            # Convert the time to 24-hour format for comparison
            appointment_datetime_str = f"{appointment_date} {appointment_time}"
            appointment_datetime = datetime.strptime(appointment_datetime_str, "%Y-%m-%d %I:%M %p")

            current_datetime = datetime.now()

            # Check if the appointment time is in the past
            if appointment_datetime < current_datetime:
                return False, "The appointment time is in the past. Please choose a future time."

            return True, None
        except ValueError:
            return False, "Invalid time. Please provide the time."

    def validate_date(self, appointment_date: str) -> Tuple[bool, Optional[str]]:
        """Validates the appointment date to ensure it's not in the past and not on a Sunday."""
        try:
            current_date = datetime.now().date()
            input_date = datetime.strptime(appointment_date, "%Y-%m-%d").date()

            if input_date < current_date:
                return False, "The appointment date is in the past. Please choose a future date."

            # Check if the date falls on a Sunday (weekday() returns 6 for Sunday)
            if input_date.weekday() == 6:
                return False, "Sunday is a holiday. Please choose another date."

            return True, None
        except ValueError:
            return False, "Invalid date. Please provide the date."
        
    def confirm_appointment(self, doctor_id: int, patient_name: str, time_slot: str, appointment_date: str) -> str:
        """Ask the user to confirm the appointment date and time before booking."""
        # Validate date
        is_valid_date, error_message_date = self.validate_date(appointment_date)
        if not is_valid_date:
            return error_message_date

        # Validate time
        is_valid_time, error_message_time = self.validate_time(time_slot, appointment_date)
        if not is_valid_time:
            return error_message_time

        confirmation_message = (
            f"Please confirm: You are booking an appointment with {self.doctors[doctor_id]['name']} "
            f"at {time_slot} on {appointment_date}. Is this correct? (Yes/No)"
        )
        return confirmation_message
    
    def book_appointment(self, doctor_id: int, patient_name: str, time_slot: str, appointment_date: str, confirmed: bool) -> str:
        """Books an appointment for the patient if the slot is available on the given date and user confirms."""
        if not confirmed:
            return "Appointment booking canceled. Please provide a new date."
        
        # Validate date and time
        is_valid_date, error_message_date = self.validate_date(appointment_date)
        if not is_valid_date:
            return error_message_date

        is_valid_time, error_message_time = self.validate_time(time_slot, appointment_date)
        if not is_valid_time:
            return error_message_time

        if self.is_slot_available(doctor_id, time_slot, appointment_date):
            self.appointments[doctor_id].remove(time_slot)
            return f"Appointment booked with {self.doctors[doctor_id]['name']} at {time_slot} on {appointment_date}."
        return f"Sorry, the slot {time_slot} on {appointment_date} is no longer available."


    def reschedule_appointment(self, doctor_id: int, current_slot: str, new_slot: str, appointment_date: str, confirmed: bool) -> str:
        """Reschedules an appointment to a new slot on the given date if available and user confirms."""
        if not confirmed:
            return "Rescheduling canceled. Please provide a new date."

        # Validate date and time
        is_valid_date, error_message_date = self.validate_date(appointment_date)
        if not is_valid_date:
            return error_message_date

        is_valid_time, error_message_time = self.validate_time(new_slot, appointment_date)
        if not is_valid_time:
            return error_message_time

        if self.is_slot_available(doctor_id, new_slot, appointment_date):
            self.appointments[doctor_id].append(current_slot)  # Add old slot back
            self.appointments[doctor_id].remove(new_slot)  # Remove new slot
            return f"Your appointment has been rescheduled to {new_slot} on {appointment_date}."
        return f"Sorry, the new slot {new_slot} on {appointment_date} is unavailable."

    def get_doctor_details(self) -> str:
        """Generates a string of available doctors and their specialties."""
        doctor_details = ""
        for doctor_id, doctor in self.doctors.items():
            doctor_details += f"Doctor: {doctor['name']}, Specialty: {doctor['specialty']}\n"
        return doctor_details

class HospitalAppointmentAssistant:
    """AI-powered assistant class to handle hospital appointment bookings and rescheduling."""
    
    def __init__(self, logger, mobile: str, session: str, full_name: str, loop: None, target: str):
        self.logger = logger
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.conversation_history = []
        self.authenticated = False
        self.model = "gpt-3.5-turbo"
        self.mobile = mobile
        self.client = openai
        self.hospital_manager = HospitalAppointmentManager(target)  # Initialize appointment manager
        self.session = session
        self.user_level = "beginner"
        self.greeting = f"Hello {self.first_name}, welcome to {self.hospital_manager.hospital_name}! How can I assist you today?"
        self.functions = self._create_functions()  # Initialize the functions

    def _create_functions(self) -> List[Dict]:
        """Define the functions to handle the appointment management process."""
        return [
            {
                "name": "manage_appointment",
                "description": "Handles booking and rescheduling appointments for patients.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "doctor_id": {
                            "type": "integer",
                            "description": "ID of the doctor with whom the patient wants to book or reschedule an appointment."
                        },
                        "patient_name": {
                            "type": "string",
                            "description": "Name of the patient booking the appointment."
                        },
                        "time_slot": {
                            "type": "string",
                            "description": "Preferred time slot for the appointment.",
                            "pattern": r"^(0[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$"
                        },
                        "operation": {
                            "type": "string",
                            "enum": ["book", "reschedule"],
                            "description": "Whether the patient is booking a new appointment or rescheduling an existing one."
                        },
                        "appointment_date": {
                            "type": "string",
                            "description": "Preferred appointment date for the appointment.",
                            "pattern": r"^\d{4}-\d{2}-\d{2}$"  # Date format validation (YYYY-MM-DD)
                        },
                        "new_slot": {
                            "type": "string",
                            "description": "New time slot for rescheduling, required if rescheduling.",
                            "pattern": r"^(0[1-9]|1[0-2]):[0-5][0-9] (AM|PM)$"
                        },
                        "confirmed": {
                            "type": "boolean",
                            "description": "Whether the user has confirmed the appointment date."
                        }
                    },
                    "required": ["doctor_id", "patient_name", "time_slot", "appointment_date", "operation", "confirmed"]
                }
            }
        ]

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Patient"

    def start_new_session(self):
        self.authenticate()
        if not self.authenticated:
            raise Exception("Authentication failed.")
        return self.greeting

    def authenticate(self) -> None:
        """Authenticates and prepares the session for appointment management."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'OPENAI_API_KEY' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            self._get_chat_response(self.conversation_history)
            self.authenticated = True
        except OpenAIError as error:
            self.logger.error(error)
        except Exception as error:
            self.logger.critical(error)

    def query(self, sentence: str, context: str) -> str:
        """Handles patient queries such as booking and rescheduling appointments."""
        self.conversation_history.append(self._create_message("user", sentence))

        messages_for_llm = self.conversation_history[-12:]
        system_message = self._create_message("system", self._generate_initial_content())
        if len(messages_for_llm) == 0 or messages_for_llm[0]['role'] != "system":
            messages_for_llm.insert(0, system_message)
        else:
            messages_for_llm[0] = system_message

        response, _ = self._get_chat_response_with_timing(messages_for_llm)
        if response:
            self.conversation_history.append(self._create_message("assistant", response))
        return response

    def _get_chat_response_with_timing(self, messages) -> Tuple[str, float]:
        """Gets a response from the GPT model and tracks the time taken."""
        try:
            start_time = time.time()
            chat_response = self._get_chat_response(messages)
            end_time = time.time()
            response_time = end_time - start_time
        except OpenAIError as error:
            self.logger.error(error)
            return "Sorry, I can't process your request right now.", 0
        
        if chat_response.choices:
            return self.handle_response(chat_response), response_time
        else:
            self.logger.error(chat_response)
            return "I'm sorry, I'm having trouble processing your request.", 0

    def handle_response(self, assistant_response: ChatCompletion):
        """Processes the assistant's response and takes necessary actions such as booking or rescheduling."""
        function_call = assistant_response.choices[0].message.function_call
        if function_call and function_call.name == "manage_appointment":
            arguments = json.loads(function_call.arguments)
            doctor_id = arguments.get("doctor_id")
            patient_name = arguments.get("patient_name")
            time_slot = arguments.get("time_slot")
            appointment_date = arguments.get("appointment_date")
            operation = arguments.get("operation")  # Either 'book' or 'reschedule'
            confirmed = arguments.get("confirmed", False)  # Check if the user has confirmed

            if operation == "book":
                # If confirmation hasn't been provided yet, ask for it.
                if not confirmed:
                    return self.hospital_manager.confirm_appointment(doctor_id, patient_name, time_slot, appointment_date)
                # Proceed to book the appointment if confirmed.
                return self.hospital_manager.book_appointment(doctor_id, patient_name, time_slot, appointment_date, confirmed)

            elif operation == "reschedule":
                new_slot = arguments.get("new_slot")
                # If confirmation hasn't been provided yet, ask for it.
                if not confirmed:
                    return self.hospital_manager.confirm_appointment(doctor_id, patient_name, new_slot, appointment_date)
                # Proceed to reschedule the appointment if confirmed.
                return self.hospital_manager.reschedule_appointment(doctor_id, time_slot, new_slot, appointment_date, confirmed)

        return assistant_response.choices[0].message.content

    def _generate_initial_content(self) -> str:
        """Generates the system prompt with available doctors and appointment slots."""
        doctor_details = self.hospital_manager.get_doctor_details()
        current_date_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")  # Get current date and time

        return (
            f'You are a hospital appointment assistant named "Pooja", managing appointments for {self.hospital_manager.hospital_name}. , connecting with user via voice call. '
            f'Today\'s date and time is {current_date_time}. '
            "Your responses will be audio-fed, so keep them concise within 1 to 2 sentences without any parentheses. "
            "Assist patients in booking or rescheduling appointments with available doctors. "
            f"\n\nHere are the available doctors:\n{doctor_details}"
        )

    def _get_chat_response(self, messages) -> ChatCompletion:
        """Gets the chat response from the OpenAI API."""
        response = self.client.chat.completions.create(
            messages=messages,
            model=self.model,
            temperature=0.3,
            max_tokens=150,
            top_p=0.9,
            frequency_penalty=0.5,
            presence_penalty=0.6,
            functions=self.functions,
            function_call="auto"
        )
        return response

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}
    
    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file."""
        data = files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)