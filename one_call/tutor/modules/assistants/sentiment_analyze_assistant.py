import os
import json
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import openai
import logging
from openai import OpenAIError

class SentimentCategory:
    def __init__(self, id: int, name: str, parent_id: Optional[int] = None):
        self.id = id
        self.name = name
        self.parent_id = parent_id
        
class SentimentAnalyzeAssistant:
    """Assistant class for analyzing sentiment in customer interactions using detailed sentiment categories."""

    # Static list of sentiments
    sentiments: List[SentimentCategory] = [
        # Top-level categories (ParentId = None)
        SentimentCategory(1, "Negative"),
        SentimentCategory(2, "Positive"),
        SentimentCategory(3, "Need Attention"),
        SentimentCategory(4, "Customer Request"),
        SentimentCategory(5, "Product Feedback"),
        SentimentCategory(6, "Escalation Activity"),
        SentimentCategory(7, "Other"),

        # Subcategories with ParentId set to the respective top-level category Id
        # Negative
        SentimentCategory(101, "Confusion", 1),
        Sentiment<PERSON>ategory(102, "Customer Waiting", 1),
        SentimentCategory(103, "Frustration", 1),
        SentimentCategory(104, "Impatience", 1),
        SentimentCategory(105, "Negative", 1),
        SentimentCategory(106, "Not Helpful", 1),
        SentimentCategory(107, "Profanity", 1),
        SentimentCategory(108, "Slightly Negative", 1),
        SentimentCategory(109, "Very Negative", 1),
        SentimentCategory(110, "Neutral", 1),

        # Positive
        SentimentCategory(201, "Fast Response", 2),
        SentimentCategory(202, "Good Information", 2),
        SentimentCategory(203, "Great Support", 2),
        SentimentCategory(204, "Helpful", 2),
        SentimentCategory(205, "Positive", 2),
        SentimentCategory(206, "Slightly Positive", 2),
        SentimentCategory(207, "Very Positive", 2),

        # Need Attention
        SentimentCategory(301, "Critical Issue", 3),
        SentimentCategory(302, "Production Issue", 3),
        SentimentCategory(303, "Urgency", 3),

        # Customer Request
        SentimentCategory(401, "Call Request", 4),
        SentimentCategory(402, "Follow-up Request", 4),

        # Product Feedback
        SentimentCategory(501, "Churn Risk", 5),
        SentimentCategory(502, "Feature Request", 5),
        SentimentCategory(503, "Negative Feedback", 5),
        SentimentCategory(504, "Positive Feedback", 5),
        SentimentCategory(505, "Usability Issue", 5),

        # Escalation Activity
        SentimentCategory(601, "Escalation Request", 6),

        # Other
        SentimentCategory(701, "Root Cause", 7),
        SentimentCategory(702, "Documentation", 7),
        SentimentCategory(703, "Feature Request", 7),
        SentimentCategory(704, "Log Message", 7),
        SentimentCategory(705, "Error Message", 7),
        SentimentCategory(706, "Command Output", 7),
        SentimentCategory(707, "Negative Feedback", 7),
        SentimentCategory(708, "Positive Feedback", 7),
        SentimentCategory(709, "Usability Issue", 7)
    ]

    def __init__(self, logger, record: Dict, db):
        self.logger = logger
        self.record = record  # Single record dictionary passed in for analysis
        self.db = db  # Database instance for updating sentiment in the record
        self.model = "gpt-4o-mini"  # Specify the model for sentiment analysis
        self.client = openai
        self.sentiment_history = []  # Stores analyzed sentiments
        self.functions = self._create_functions()

    def _create_functions(self) -> List[Dict]:
        """Define the function for analyzing detailed customer sentiment based on pre-defined categories."""
        categories = list(set([s.name for s in self.sentiments if s.parent_id is None]))
        subcategories = [s.name for s in self.sentiments if s.parent_id is not None]
        return [
            {
                "name": "analyze_sentiment",
                "description": "Analyzes the sentiment of a text, categorizes it, assigns a score, and identifies key emotions.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "text": {
                            "type": "string",
                            "description": "The text to analyze for sentiment.",
                        },
                        "sentiment_category": {
                            "type": "string",
                            "enum": categories,
                            "description": "The top-level sentiment category derived from the text."
                        },
                        "sentiment_subcategory": {
                            "type": "string",
                            "enum": subcategories,
                            "description": "The specific sentiment subcategory derived from the text."
                        },
                        "sentiment_score": {
                            "type": "number",
                            "description": "The sentiment score ranging from 0 to 100.",
                        },
                        "key_emotions": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "Key emotions associated with the sentiment (e.g., joy, sadness, anger, surprise)."
                        }
                    },
                    "required": ["text"]
                }
            }
        ]

    def analyze_sentiment(self) -> Tuple[str, str, str, int, List[str]]:
        """Analyzes the sentiment of the record's description text, categorizes it, assigns a score, and identifies key emotions."""

        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            self.logger.warning("'openai_api' wasn't found to proceed")
            return "", "Neutral", "Neutral", 50, []

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        messages = []
        
        try:
            # Generate the initial content for the assistant
            content = self._generate_initial_content()
            messages.append(self._create_message("system", content))
            
            # Add the user's text (description) to analyze
            text = self.record.get("description", "")
            messages.append(self._create_message("user", text))
            
            # Get the chat response from the OpenAI API
            chat_response = self._get_chat_response(messages)
            
            # Parse the sentiment from the response
            text_sentiment, sentiment_category, sentiment_subcategory, score, emotions = self._parse_sentiment(chat_response)
            
            # Log the sentiment analysis
            self._log_sentiment_analysis(text, sentiment_category, sentiment_subcategory, score, emotions)
            
            return text_sentiment, sentiment_category, sentiment_subcategory, score, emotions

        except OpenAIError as error:
            self.logger.error(f"Sentiment analysis error: {error}")
            return "", "Neutral", "Neutral", 50, []



    def _parse_sentiment(self, response) -> Tuple[str, str, str, int, List[str]]:
        """Extracts the sentiment text, category, subcategory, score, and key emotions from the response."""
        if response.choices and response.choices[0].message.function_call:
            function_call = response.choices[0].message.function_call
            if function_call.name == "analyze_sentiment":
                result = json.loads(function_call.arguments)
                text_sentiment = result.get("text", "")
                sentiment_category = result.get("sentiment_category", "Neutral")
                sentiment_subcategory = result.get("sentiment_subcategory", "Neutral")
                sentiment_score = result.get("sentiment_score", 50)
                key_emotions = result.get("key_emotions", [])
                return text_sentiment, sentiment_category, sentiment_subcategory, sentiment_score, key_emotions
        return "", "Neutral", "Neutral", 50, []


    def _log_sentiment_analysis(self, text: str, sentiment_category: str, sentiment_subcategory: str, score: int, emotions: List[str]) -> None:
        """Logs each sentiment analysis to a history list for record-keeping."""
        self.sentiment_history.append({
            "text": text,
            "sentiment_category": sentiment_category,
            "sentiment_subcategory": sentiment_subcategory,
            "score": score,
            "emotions": emotions,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
        self.logger.info(
            f"Sentiment analysis logged: Text='{text}', "
            f"Category='{sentiment_category}', Subcategory='{sentiment_subcategory}', "
            f"Score={score}, Emotions={emotions}"
        )


    def _generate_initial_content(self) -> str:
        """Generates the system prompt with detailed instructions for sentiment analysis."""
        current_date_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        category_instructions = {}
        for sentiment in self.sentiments:
            if sentiment.parent_id is None:
                category_instructions[sentiment.name] = []
            else:
                parent_name = next((s.name for s in self.sentiments if s.id == sentiment.parent_id), None)
                if parent_name:
                    category_instructions[parent_name].append(sentiment.name)

        instructions_text = "\n".join(
            f"- {category}: {', '.join(subcategories)}"
            for category, subcategories in category_instructions.items()
        )


        base_instructions = (
            "You are a sentiment analysis assistant that categorizes customer feedback "
            "using predefined sentiment categories and assigns a score from 0 to 100.\n"
            f"Current date and time: {current_date_time}.\n"
            "Sentiment Categories:\n"
            f"{instructions_text}\n\n"
            "Instructions:\n"
            "- Assign a score between 0 and 100 based on the sentiment.\n"
            "- Identify key emotions such as joy, sadness, anger, and surprise if present.\n"
            "- Carefully read each message and tag it with the most relevant category.\n"
            "- If unsure, assign 'Neutral' with a score of 50.\n"
        )

        return base_instructions
    
    def _get_chat_response(self, messages):
        """Gets the chat response from the OpenAI API and prints the response time."""
        if self.functions:
            response = self.client.chat.completions.create(
                messages=messages,
                model=self.model,
                temperature=0.2,
                max_tokens=300,
                functions=self.functions,
                function_call="auto",
            )

        return response
        
    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}