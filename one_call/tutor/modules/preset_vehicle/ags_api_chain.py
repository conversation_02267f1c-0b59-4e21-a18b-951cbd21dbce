from langchain.chains import <PERSON>Chain
from langchain_openai import OpenAI

from modules.preset_vehicle.api_docs import ags_api_docs
from modules.preset_vehicle.prompts import api_url_prompt, api_response_prompt

llm = OpenAI(model='gpt-3.5-turbo-instruct', temperature=0)
api_chain = APIChain.from_llm_and_api_docs(
    llm=llm,
    api_docs=ags_api_docs,
    api_url_prompt=api_url_prompt,
    api_response_prompt=api_response_prompt,
    verbose=True,
    limit_to_domains=["http://127.0.0.1:5000/"]
)


def run(query):
    response = api_chain.run(query).lower().replace("summary:", "").replace("output:", "").replace("$", "")
    if "please try again later" in response:
        response = response.replace("error:", "")
    return response.strip()
