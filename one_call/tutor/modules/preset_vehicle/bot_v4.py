import os
from dotenv import load_dotenv
import re
from dataclasses import dataclass
from typing import Literal, Optional, Tuple

from fuzzywuzzy import fuzz
from fuzzywuzzy import process
from langchain.chains import Conversation<PERSON>hain
from langchain.memory import ConversationBufferMemory
from langchain.prompts.prompt import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain.llms import OpenAI

from modules.preset_vehicle.data_store import vehicle_number_list

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')


@dataclass
class Message:
    """Class for keeping track of interview history."""
    role: Literal["user", "assistant"]
    content: str


greeting = "Thank you for calling On<PERSON>. could you please provide the vehicle number and the amount you wish to preset now?"

conversation_history = []

conversation_memory = ConversationBufferMemory(human_prefix="User: ", ai_prefix="Assistant")

# llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0.3)
llm = OpenAI(model='gpt-3.5-turbo-instruct', temperature=0)

PROMPT_TEMPLATE = """
    You are a vehicle preset assistant. Your task is to gather information from the user regarding vehicle numbers and their respective preset amounts.

    1. Ask questions to obtain both the vehicle number and preset amount from the user.
    2. Ensure that both the vehicle number and preset amount are not null or empty.
    3. Validate the vehicle number against the user list of vehicle numbers ({vehicle_numbers}). If the provided vehicle number is not in the list, prompt the user to provide a valid vehicle number.
    4. Ensure the preset amount is within the range of 100 to 10,000.
    5. Once you have obtained the vehicle number is in the list of vehicle numbers ({vehicle_numbers}) and preset amount from the user, respond with "Vehicle number: 'vehicle number', Preset amount: 'preset amount', Output: completed".
    
    If the user asks for related information about the vehicle list or vehicle numbers, provide the following:
    "Here is a list of your vehicle numbers: {vehicle_numbers}."

    Remember to:
    * All your response will be audio fed    
    * Keep your responses short (1 or 2 sentences).
    * Ask only one question at a time.
    * Prompt follow-up questions if necessary.
    * Avoid explanations.
    
    Current Conversation:
    {history}
    User: {input}
    Assistant:
    """

PROMPT_TEMPLATE = PROMPT_TEMPLATE.replace("{vehicle_numbers}", ", ".join(vehicle_number_list))

PROMPT = PromptTemplate(input_variables=["history", "input"], template=PROMPT_TEMPLATE)


# Function to create a new conversation chain instance
def create_conversation_chain():
    return ConversationChain(prompt=PROMPT, llm=llm,
                             memory=ConversationBufferMemory(human_prefix="User: ", ai_prefix="Assistant"))


conversation_chain = create_conversation_chain()


def answer_call_back(human_input: str = None):
    if not human_input:
        input_text = "Sorry, I didn't get that. Please try again."
        # conversation_history.append(Message("ai", input_text))
        return input_text
    else:
        input_text = human_input

    conversation_history.append(Message("user", input_text))
    ai_response = conversation_chain.run(input_text)
    ai_response = ai_response.replace("Assistant:", "")
    conversation_history.append(Message("assistant", ai_response))
    return ai_response


def start_new_call():
    global conversation_chain
    conversation_chain = create_conversation_chain()
    conversation_history.clear()


def extract_vehicle_number_and_amount_1(text: str) -> (Optional[str], Optional[int]):
    # Use regex patterns to identify potential vehicle numbers and amounts
    vehicle_number_pattern = re.compile(
        r'\b(' + '|'.join(re.escape(vehicle) for vehicle in vehicle_number_list) + r')\b')
    amount_pattern = re.compile(r'\b(\d+)\b')

    vehicle_number = vehicle_number_pattern.search(text)
    amount = amount_pattern.search(text)

    if vehicle_number:
        vehicle_number = vehicle_number.group(0)
    else:
        vehicle_number = None

    if amount:
        amount = int(amount.group(0))
        if not (100 <= amount <= 10000):
            amount = None
    else:
        amount = None

    return vehicle_number, amount


def extract_vehicle_number_and_amount_2(text: str) -> (Optional[str], Optional[int]):
    # Use regex patterns to identify potential vehicle numbers and amounts
    amount_pattern = re.compile(r'\b(\d+)\b')
    vehicle_number_pattern = re.compile(r'\b[A-Z]{2,3}\s?\d{2,3}\b')

    vehicle_number_match = vehicle_number_pattern.search(text)
    amount_match = amount_pattern.findall(text)

    vehicle_number = vehicle_number_match.group(0) if vehicle_number_match else None
    amounts = [int(amount) for amount in amount_match]

    preset_amount = next((amount for amount in amounts if 100 <= amount <= 10000), None)

    return vehicle_number, preset_amount


def extract_vehicle_number_and_amount_3(text: str) -> (Optional[str], Optional[int]):
    # Normalize text
    normalized_text = text.lower()

    vehicle_number = None
    preset_amount = None

    # Search for vehicle number with fuzzy matching
    for vehicle in vehicle_number_list:
        # ratio = fuzz.ratio(normalize_vehicle_number(vehicle), normalize_vehicle_number(normalized_text))
        ratio = fuzz.partial_ratio(normalize_vehicle_number(vehicle), normalize_vehicle_number(normalized_text))
        # ratio = fuzz.token_sort_ratio(normalize_vehicle_number(vehicle), normalize_vehicle_number(normalized_text))
        # ratio = fuzz.token_set_ratio(normalize_vehicle_number(vehicle), normalize_vehicle_number(normalized_text))
        print(f"Comparing {vehicle} with {normalized_text}: Ratio = {ratio}")
        if ratio >= 80:
            vehicle_number = vehicle
            # Remove the found vehicle number from the normalized text
            normalized_text = normalized_text.replace(normalize_vehicle_number(vehicle), '')

    # Use regex pattern to identify potential amount
    amount_pattern = re.compile(r'\b(\d+)\b')
    amount_match = amount_pattern.search(normalized_text)

    if amount_match:
        preset_amount = int(amount_match.group(0))
        if not (100 <= preset_amount <= 10000):
            preset_amount = None

    return vehicle_number, preset_amount


def extract_vehicle_number_and_amount_4(text: str) -> (Optional[str], Optional[int]):
    # Normalize text
    normalized_text = text.lower()

    vehicle_number = None
    preset_amount = None

    # Search for vehicle number with fuzzy matching
    best_match, ratio = process.extractOne(normalized_text, [vehicle for vehicle in vehicle_number_list])

    if ratio >= 40:
        vehicle_number = vehicle_number_list[[vehicle for vehicle in vehicle_number_list].index(best_match)]

        # Remove only the found vehicle number from the normalized text
        normalized_text = normalized_text.replace(vehicle_number, '', 1)  # Remove only the first occurrence

    # Use regex pattern to identify potential amount
    amount_pattern = re.compile(r'\b(\d+)\b')
    amount_match = amount_pattern.search(normalized_text)

    if amount_match:
        preset_amount = int(amount_match.group(0))
        if not (100 <= preset_amount <= 10000):
            preset_amount = None

    return vehicle_number, preset_amount


def extract_vehicle_number_and_amount_5(text: str) -> (Optional[str], Optional[int]):
    # Normalize text
    normalized_text = text.lower()

    vehicle_number = None
    preset_amount = None

    # Search for vehicle number with fuzzy matching
    for vehicle in vehicle_number_list:
        ratio = fuzz.partial_ratio(normalize_vehicle_number(vehicle), normalize_vehicle_number(normalized_text))

        print(f"Comparing {vehicle} with {normalized_text}: Ratio = {ratio}")
        if ratio >= 80:
            vehicle_number = vehicle
            # Extract the matched word(s) from the normalized text
            matched_word = re.search(r'\b(' + re.escape(normalize_vehicle_number(vehicle)) + r')\b',
                                     normalize_vehicle_number(normalized_text))
            if matched_word:
                matched_word = matched_word.group(0)
                # Remove only the matched word from the normalized text
                normalized_text = normalized_text.replace(matched_word, '', 1)  # Remove only the first occurrence

    # Use regex pattern to identify potential amount
    amount_pattern = re.compile(r'\b(\d+)\b')
    amount_match = amount_pattern.search(normalized_text)

    if amount_match:
        preset_amount = int(amount_match.group(0))
        if not (100 <= preset_amount <= 10000):
            preset_amount = None

    return vehicle_number, preset_amount


def extract_vehicle_number_and_amount(text: str, vehicle_number_list: list) -> Tuple[
    Optional[str], Optional[int], Optional[str]]:
    # Normalize text
    normalized_text = text.lower()
    normalized_text_remove_space = normalize_vehicle_number(normalized_text)

    vehicle_number = None
    preset_amount = None

    # Search for vehicle number with fuzzy matching
    best_match, ratio = process.extractOne(normalized_text_remove_space, vehicle_number_list)

    print(f"Comparing {best_match} with {normalized_text}: Ratio = {ratio}")
    sub_string = ""
    if ratio >= 60:
        vehicle_number = best_match

        # Find the start and end index of the best match in the normalized text
        # Use fuzzy matching to get a more flexible start and end
        match_start = -1
        match_end = -1
        for i in range(len(normalized_text_remove_space) - len(best_match) + 1):
            sub_string = normalized_text_remove_space[i:i + len(best_match)]
            ratio = process.extractOne(sub_string, [best_match])[1]
            if ratio >= 80:  # Use a high threshold for sub-string match
                match_start = i
                match_end = i + len(best_match)
                break

        if match_start != -1:
            # Remove the matched vehicle number from the normalized text
            normalized_text_remove_space = normalized_text_remove_space[:match_start] + normalized_text_remove_space[
                                                                                        match_end:]

    # Use regex pattern to identify potential amount
    amount_pattern = re.compile(r'\b(\d+)\b')
    amount_match = amount_pattern.search(normalized_text_remove_space)

    if amount_match:
        preset_amount = int(amount_match.group(0))
        if not (100 <= preset_amount <= 10000):
            preset_amount = None

    return vehicle_number, preset_amount, sub_string


def normalize_vehicle_number(vehicle_number: str) -> str:
    # Normalize vehicle number by removing spaces and converting to lowercase
    return vehicle_number.replace(" ", "").lower()


def count_vehicle_numbers_with_substring(vehicle_number_list, sub_string):
    count = sum(1 for vehicle in vehicle_number_list if sub_string in vehicle)
    return count


def get_user_conversation_summary() -> Optional[str]:
    vehicle_number = None
    preset_amount = None
    sub_string = None
    for message in conversation_history:
        if message.role == "user":
            potential_vehicle_number, potential_amount, sub_string = extract_vehicle_number_and_amount(message.content,
                                                                                                       vehicle_number_list)
            if potential_vehicle_number:
                vehicle_number = potential_vehicle_number
            if potential_amount:
                preset_amount = potential_amount

    preset_amount = None
    if vehicle_number and preset_amount:
        return f"Vehicle number: {vehicle_number}, Preset amount: {preset_amount}, Match count: {count_vehicle_numbers_with_substring(vehicle_number_list, sub_string)}"
    elif vehicle_number:
        # return f"Vehicle number: {vehicle_number}, Preset amount: not provided or out of range, Match count: {count_vehicle_numbers_with_substring(vehicle_number_list, sub_string)}"
        return f", Vehicle number is {vehicle_number}."
    elif preset_amount:
        # return f"Vehicle number: not provided, Preset amount: {preset_amount}"
        return ""
    else:
        # return "Vehicle number and preset amount not provided"
        return ""


def get_user_vehicle_number(response) -> Optional[str]:
    vehicle_number = None
    preset_amount = None
    sub_string = None
    if response:
        potential_vehicle_number, potential_amount, sub_string = extract_vehicle_number_and_amount(response,
                                                                                                   vehicle_number_list)
        if potential_vehicle_number:
            vehicle_number = potential_vehicle_number
        if potential_amount:
            preset_amount = potential_amount

    preset_amount = None
    if vehicle_number and preset_amount:
        return f"{response}, Vehicle number: {vehicle_number}, Preset amount: {preset_amount}, Match count: {count_vehicle_numbers_with_substring(vehicle_number_list, sub_string)}"
    elif vehicle_number:
        # return f"Vehicle number: {vehicle_number}, Preset amount: not provided or out of range, Match count: {count_vehicle_numbers_with_substring(vehicle_number_list, sub_string)}"
        return f"{response}, Vehicle number is {vehicle_number}."
    elif preset_amount:
        # return f"Vehicle number: not provided, Preset amount: {preset_amount}"
        return response
    else:
        # return "Vehicle number and preset amount not provided"
        return response


# Function to print the interaction for debugging
def print_interaction(user_input, response):
    print(f"User: {user_input}")
    print(f"Assistant: {response}\n")


"""
conversation_history.append(Message("user", "My vehicle number is ABC 123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB 23 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is a b 123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B 1 23 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B C 1 2 3 and I want to preset 5000"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B C 12 34 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B 1 2 34 and I want to preset 50000"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB1234 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is 1234 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()


# Simulate the conversation with debugging outputs
print("Initial Greeting:")
print(greeting)
print()

user_input = "My vehicle number is AB1234 and I want to preset 500."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AB1234"
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 500."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is XYZ1234 and I want to preset 500."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AB1234 and I want to preset 50000."
response = answer_call_back(user_input)
print_interaction(user_input, response)

start_new_call()
user_input = "My vehicle number is A123, Vehicle number: ab1234".lower()
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 1000."
response = answer_call_back(user_input)
print_interaction(user_input, response)

# Start a new call to reset the conversation
print("Starting a new call:")
start_new_call()
print(greeting)
print()

user_input = "My vehicle number is AC1234 and I want to preset 1000."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AC1234"
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 1000"
response = answer_call_back(user_input)
print_interaction(user_input, response)
"""
