import re
from typing import Op<PERSON>, <PERSON><PERSON>, <PERSON>, Dict

from api.ongo import ongo_app_api
from modules.logger import logger
from modules.preset_vehicle import vehicle_preset_bot, ags_api_chain
from modules.preset_vehicle.bot import VehiclePresetAssistant
from modules.text import amount


class VehicleValidator:
    def __init__(self, user):
        self.user = user
        self.vehicle_number_list = [vehicle['number'].lower() for vehicle in self.user.vehicles]
        self.assistant = VehiclePresetAssistant(session=self.user.session, full_name=self.user.name,
                                                vehicle_number_list=self.vehicle_number_list)
        self.minimum_amount = 30
        self.maximum_amount = 100000
        self.vehicle_number_str = None

    def is_vehicle_number_and_preset_amount_complete(self, llm_answer: str) -> Tuple[
        bool, Optional[Dict], bool, Optional[str]]:
        """
        Check if the vehicle number and preset amount are complete.

        Parameters:
        llm_answer (str): The input string from the LLM.

        Returns:
        tuple: A tuple with a boolean indicating if completed, the JSON object or None,
               a boolean indicating if the phrase was found, and the preset amount check or None.
        """
        phrase = "output: completed"
        is_phrase = phrase in llm_answer

        if is_phrase:
            try:
                llm_answer_change = f"mobile_number:{self.user.mobile}, user_id:{self.user.user_id}, {llm_answer}"
                result = self.convert_to_json_combine(llm_answer_change)
                preset_amount = result.get('preset_amount')

                if result.get('vehicle_number') and preset_amount is not None:
                    preset_amount_check, is_preset_valid = amount.get_amount_from_ai_response(
                        preset_amount, self.minimum_amount, self.maximum_amount, self.vehicle_number_list
                    )
                    if not is_preset_valid:
                        return False, None, is_phrase, preset_amount_check

                    result['preset_amount'] = preset_amount_check
                    result['vehicle_id'] = self.get_vehicle_id_by_number(result.get('vehicle_number'))
                    return True, result, is_phrase, None
            except ValueError as e:
                logger.error(f"Error in processing vehicle number and preset amount: {e}")
                return True, None, is_phrase, None

        return False, None, is_phrase, None

    def convert_to_json_combine(self, input_string: str) -> dict:
        def parse_value(value):
            if value.isdigit():
                return int(value)
            try:
                return float(value)
            except ValueError:
                return value

        input_string = input_string.strip().lower()
        pattern = re.compile(r"\s*(\w+(?: \w+)*?)\s*:\s*'?(.*?)'?\s*(?=,|;|$)")
        matches = pattern.findall(input_string)

        data_dict = {}
        for key, value in matches:
            key = key.strip().replace(' ', '_')
            data_dict[key] = parse_value(value.strip())

        return data_dict

    def get_amount(self, user_input):
        return amount.get_amount_from_user_response(user_input, self.minimum_amount, self.maximum_amount,
                                                    self.vehicle_number_list)

    def get_vehicle_id_by_number(self, vehicle_number: str) -> Optional[str]:
        """Get the vehicle ID by vehicle number."""
        for vehicle in self.user.vehicles:
            if vehicle['number'].lower() == vehicle_number:
                return vehicle['id']
        return None

    def post_vehicle_number_and_preset_amount(self, ai_response: str, result: Optional[Dict], is_phrase: bool) -> str:
        """Post the vehicle number and preset amount to the API."""
        if result:
            is_valid_vehicle, response = self.validate_vehicle(result)
            if is_valid_vehicle:
                return ongo_app_api.get_response(result)
            else:
                return response
        else:
            if "vehicle" in ai_response and "preset" in ai_response:
                return ai_response
            else:
                return vehicle_preset_bot.get_current_vehicle_preset(self.assistant.conversation_history)

        return ags_api_chain.run(ai_response)

    def validate_vehicle(self, result: Dict) -> Tuple[bool, str]:
        """Validate the vehicle number."""
        if result.get('vehicle_number') in self.vehicle_number_list:
            return True, ""
        else:
            return False, "error: please provide a valid vehicle number"
