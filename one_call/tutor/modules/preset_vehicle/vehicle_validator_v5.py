import random
import re
from typing import Optional, Tuple, Dict, List

from fuzzywuzzy import process

from api.ongo import ongo_app_api
from executors import static_responses
from modules.logger import logger
from modules.preset_vehicle import vehicle_preset_bot, ags_api_chain
from modules.preset_vehicle.bot import VehiclePresetAssistant
from modules.text import amount, word_to_number, auto_correct


def get_no_tag_vehicle_message():
    messages = [
        "Your vehicle isn't tagged. Please visit the nearest petrol pump to get one. Thank you.",
        "Vehicle not tagged. Please go to the nearest gas station for a tag. Thank you.",
        "Please tag your vehicle at the nearest petrol station and call us again. Thank you.",
        "No tag detected. Visit the closest fuel station for a tag. Thank you.",
        "Tag required. Please get one at the nearest petrol pump. Thank you.",
        "Vehicle tag missing. Head to the nearest petrol station to obtain one. Thank you.",
        "Please visit the nearest gas station to tag your vehicle. Thank you.",
        "Your vehicle needs a tag. Visit the closest petrol pump. Thank you.",
        "Vehicle not tagged. Please get a tag at the nearest petrol station. Thank you.",
        "Please tag your vehicle at the nearest petrol station. Thank you."
    ]
    return random.choice(messages)


def normalize_vehicle_number(vehicle_number: str) -> str:
    return vehicle_number.replace(" ", "").lower()


def remove_symbols(text):
    # Use regular expression to remove all non-alphanumeric characters
    cleaned_text = re.sub(r'[^a-zA-Z0-9]', '', text)
    return cleaned_text


def extract_numbers(text):
    # Use regular expression to find all sequences of digits in the text
    numbers = re.findall(r'\d+', text)
    return numbers


def extract_vehicle_number(text: str, vehicle_number_list) -> str | None:
    if len(vehicle_number_list) == 0:
        return None

    normalized_text = remove_symbols(text.lower())
    normalized_text_remove_space = normalize_vehicle_number(normalized_text)
    numbers = extract_numbers(normalized_text_remove_space)
    vehicles = []

    for number in numbers:
        for vehicle in vehicle_number_list:
            if number in vehicle:
                vehicles.append(vehicle)

    if len(vehicles) == 1:
        return vehicles[0]

    vehicle_number = None
    best_match, ratio = process.extractOne(normalized_text_remove_space, vehicle_number_list)
    if ratio >= 46:
        vehicle_number = best_match

    return vehicle_number if vehicle_number in vehicle_number_list else None


class VehicleValidator:
    def __init__(self, user, logger):
        self.user = user
        self.logger = logger
        # self.vehicle_number_list = [vehicle['number'].lower() for vehicle in self.user.vehicles]
        self.vehicle_data = user.vehicle_data
        self.vehicle_make_list = list(self.vehicle_data.keys())
        self.vehicle_makes = ", ".join(self.vehicle_make_list)
        self.vehicle_number_list = [num for numbers in self.vehicle_data.values() for num in numbers]
        self.vehicle_numbers = ", ".join(self.vehicle_number_list)
        self.vehicle_top_make_list = [
            'toyota', 'mahindra', 'maruti', 'ford', 'honda', 'bmw', 'audi', 'mercedes-benz',
            'chevrolet', 'nissan', 'hyundai', 'kia', 'volkswagen', 'subaru', 'mazda',
            'jeep', 'dodge', 'lexus', 'infiniti', 'acura', 'dc'
        ]
        self.assistant = VehiclePresetAssistant(mobile=self.user.mobile, session=self.user.session,
                                                full_name=self.user.name,
                                                vehicle_top_make_list=self.vehicle_top_make_list,
                                                loop=self.user.loop,
                                                vehicle_data=self.vehicle_data)
        self.minimum_amount = 30
        self.maximum_amount = 100000
        self.vehicle_number_str = self._check_vehicle()

    def _check_vehicle(self) -> str | None:
        if len(self.vehicle_number_list) == 1:
            vehicle_number = self.vehicle_number_list[0]
            vehicle_number_str, is_found_vehicle = self.get_user_vehicle_number(vehicle_number)
            if is_found_vehicle:
                return vehicle_number_str
        return None

    def start_new_call(self):
        self.assistant.start_new_call()

    def process_phrase(self, phrase: str) -> Tuple[List[str], Optional[str]]:
        phrase = phrase.lower()
        phrase = word_to_number.words_to_digits(phrase)
        vehicle_number = extract_vehicle_number(phrase, self.vehicle_number_list)

        if vehicle_number:
            self.vehicle_number_str = f"my vehicle number is {vehicle_number}."
            phrase = auto_correct.replace_with_best_match(phrase, vehicle_number)
            # print("auto_correct :", phrase)
            self.logger.debug("auto_correct: %s", phrase)
            return re.split(re.escape(vehicle_number), phrase), vehicle_number
        else:
            return phrase.split(". "), None

    def answer_call_back(self, phrase: str, context: str = None) -> str:
        if len(self.vehicle_number_list) == 0:
            return self.assistant.answer_call_back(phrase, context)

        sub_phrases, vehicle_number = self.process_phrase(phrase)
        user_input_final = []

        # print(f"sub_phrases :{sub_phrases}")
        self.logger.debug("sub_phrases: %s", sub_phrases)
        if vehicle_number and sub_phrases:
            sub_phrases[0] = f"{sub_phrases[0]}, my vehicle number is {vehicle_number} ,".strip()

        for sub_phrase in sub_phrases:
            if sub_phrase.strip():
                user_str, is_amount = self.get_amount(sub_phrase)
                if is_amount:
                    user_input_final.append(user_str)
                else:
                    # print("AI response (user_str):", user_str)
                    self.logger.debug("AI response (user_str): %s", user_str)
                    return user_str

        user_input_final_str = ' '.join(user_input_final).strip()
        user_input_final_str = auto_correct.remove_extra_spaces(user_input_final_str)
        # print("user_input_final : ", user_input_final_str)
        self.logger.debug("user_input_final: %s", user_input_final_str)

        return self.assistant.answer_call_back(user_input_final_str, context)

    def answer_call_back_1(self, phrase: str, context: str = None) -> str:

        is_amount = False
        end_call = False
        user_input_final = ''
        # Split the phrase
        phrases = phrase.split(". ")
        # Process each sub-phrase
        for sub_phrase in phrases:
            if sub_phrase:
                sub_phrases = sub_phrase.split("and ")
                for sub_sub_phrase in sub_phrases:
                    llm_answer = None
                    answer_is_not_valid_amount = None
                    user_input = ''
                    if sub_sub_phrase:
                        if self.vehicle_number_str:
                            user_amount_str, is_amount = self.get_amount(sub_sub_phrase)
                            if is_amount:
                                user_input = user_amount_str
                            else:
                                answer_is_not_valid_amount = user_amount_str
                        else:
                            vehicle_number_str, is_found_vehicle = self.get_user_vehicle_number(
                                sub_sub_phrase)
                            if is_found_vehicle:
                                self.vehicle_number_str = vehicle_number_str
                                user_input = vehicle_number_str
                                print("user input modified", user_input)
                                # self.entry_dumper.start_dump_task("modified_user_input", user_input)

                        if answer_is_not_valid_amount:
                            print("AI response (user_amount_str):", answer_is_not_valid_amount)
                            return answer_is_not_valid_amount
                        else:
                            if user_input:
                                user_input_final += user_input + ' '
                            else:
                                user_input_final += sub_sub_phrase + ' '
                            # self.entry_dumper.start_dump_task("user_amount_str", llm_answer)

        return self.assistant.answer_call_back(user_input_final, context)

    def ai_response_greeting_validation(self, llm_answer: str) -> str:
        phrase = "output completed"
        llm_answer = llm_answer.replace(phrase, "")
        phrase = "output: completed"
        return llm_answer.replace(phrase, "")

    def _check_completed_phrase(self, llm_answer):
        """
        Check if any of the predefined completed phrases are found in the given LLM answer.

        Args:
            llm_answer (str): The answer provided by the language model.

        Returns:
            bool: True if any of the completed phrases are found, False otherwise.
        """
        completed_phrases = ["output completed", "output: completed"]
        return any(phrase in llm_answer for phrase in completed_phrases)

    def process_llm_answer(self, llm_answer: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        if not llm_answer:
            logger.error("llm_answer is None")
            return False, None, static_responses.un_processable('')

        is_phrase_found = self._check_completed_phrase(llm_answer)

        if is_phrase_found and self.vehicle_number_list:

            try:
                """
                new_llm_answer, is_completed = self.assistant.output_completed()
                if not is_completed:
                   return False, None, is_phrase_found, new_llm_answer

                """

                llm_answer_change = f"mobile_number:{self.user.mobile}, user_id:{self.user.user_id}, {llm_answer}"
                result = self.convert_to_json_combine(llm_answer_change)
                """
                llm_answer_for_amount, is_valid_preset = self._validate_preset_amount(result)
                if not is_valid_preset:
                    new_llm_answer = llm_answer_for_amount
                    return False, None, is_phrase_found, new_llm_answer
                else:
                    result['preset_amount'] = llm_answer_for_amount

                preset_amount = result.get('preset_amount')
                """
                result['vehicle_id'] = self.get_vehicle_id_by_number(result.get('vehicle_number'))
                """
                if result.get('vehicle_number'):
                    vehicle_number_str, is_found_vehicle = self.get_ai_vehicle_number(result.get('vehicle_number'))
                    if is_found_vehicle:
                        result['vehicle_number'] = vehicle_number_str
                        result['vehicle_id'] = self.get_vehicle_id_by_number(vehicle_number_str)
                        return True, result, is_phrase_found, None
                    else:
                        new_llm_answer = vehicle_number_str
                        return False, None, is_phrase_found, new_llm_answer

                """
                return True, result, None
            except ValueError as e:
                logger.error(f"Error in processing vehicle number and preset amount: {e}")
                return True, None, static_responses.un_processable('')

        return False, None, llm_answer

    def get_ai_vehicle_number(self, response: str) -> Tuple[str, bool]:
        vehicle_number = None
        if response:
            response = word_to_number.words_to_digits(response)
            response = remove_symbols(response)
            potential_vehicle_number = extract_vehicle_number(response, self.vehicle_number_list)
            if potential_vehicle_number:
                vehicle_number = potential_vehicle_number

        if vehicle_number:
            return vehicle_number, True
        else:
            is_valid_vehicle, vehicle_number = self.validate_vehicle(response)
            if is_valid_vehicle:
                return vehicle_number, True
            else:
                return "please provide valid vehicle number", False

    def get_user_vehicle_number(self, response: str) -> Tuple[str, bool]:
        vehicle_number = None
        preset_amount = None
        sub_string = None
        if response:
            if not self.vehicle_number_list:
                return response, False

            response = word_to_number.words_to_digits(response)
            response = remove_symbols(response)
            potential_amount = None
            potential_vehicle_number = extract_vehicle_number(response, self.vehicle_number_list)
            if potential_vehicle_number:
                vehicle_number = potential_vehicle_number
            if potential_amount:
                preset_amount = potential_amount

        if vehicle_number and preset_amount:
            return f"my vehicle number is {vehicle_number}, preset amount is {preset_amount}.", True
        elif vehicle_number:
            return f"my vehicle number is {vehicle_number}.", True
        elif preset_amount:
            return response, False
        else:
            return response, False

    def _validate_preset_amount(self, result: Dict) -> Tuple[Optional[str], bool]:
        """
        Validate the preset amount from the result.

        Parameters:
        result (dict): The JSON object containing the result.

        Returns:
        tuple: A tuple with the validated preset amount and a boolean indicating if it's valid.
        """
        preset_amount = result.get('preset_amount')

        if preset_amount in ("full", "stop"):
            return preset_amount, True

        llm_answer, is_preset_valid = amount.get_amount_from_ai_response(
            preset_amount, self.minimum_amount, self.maximum_amount, self.vehicle_number_list
        )

        return llm_answer, is_preset_valid

    def handle_no_vehicle_case(self, llm_answer: str) -> Tuple[Optional[str], bool]:
        """
        Handle the case where there is no vehicle in the vehicle list.

        Parameters:
        llm_answer (str): The input string from the LLM.
        phrase (str): The phrase to be removed from the input.

        Returns:
        tuple: A tuple with the modified LLM answer and a boolean indicating if no vehicle was found.
        """
        phrase = "output: completed"
        try:
            if not self.vehicle_number_list:
                cleaned_answer = llm_answer.replace(phrase, "").replace("  ", " ").strip()
                cleaned_llm_answer = re.sub(r'[^a-zA-Z0-9\s]', '', cleaned_answer)
                if not cleaned_llm_answer:
                    return get_no_tag_vehicle_message(), True
        except ValueError:
            return get_no_tag_vehicle_message(), True
        return None, False

    def is_vehicle_number_and_preset_amount_complete_1(self, llm_answer: str) -> Tuple[
        bool, Optional[Dict], bool, Optional[str], bool, str | None]:
        """
        Check if the vehicle number and preset amount are complete.

        Parameters:
        llm_answer (str): The input string from the LLM.

        Returns:
        tuple: A tuple with a boolean indicating if completed, the JSON object or None,
               a boolean indicating if the phrase was found, and the preset amount check or None.
        """
        phrase = "output: completed"
        is_phrase = phrase in llm_answer
        new_llm_answer = None
        is_end_no_vehicle = False
        if is_phrase:
            try:
                if not self.vehicle_number_list:
                    try:
                        new_llm_answer = llm_answer.replace(phrase, "").replace("  ", " ").strip()
                        cleaned_llm_answer = re.sub(r'[^a-zA-Z0-9\s]', '', new_llm_answer)
                        if not cleaned_llm_answer:
                            is_end_no_vehicle = True
                            new_llm_answer = get_no_tag_vehicle_message()
                    except ValueError:
                        is_end_no_vehicle = True
                        new_llm_answer = get_no_tag_vehicle_message()
                    return False, None, is_phrase, None, is_end_no_vehicle, new_llm_answer

                llm_answer_change = f"mobile_number:{self.user.mobile}, user_id:{self.user.user_id}, {llm_answer}"
                result = self.convert_to_json_combine(llm_answer_change)
                preset_amount = result.get('preset_amount')

                if result.get('vehicle_number') and preset_amount is not None:
                    if preset_amount in ("full", "stop"):
                        preset_amount_check = preset_amount
                    else:
                        preset_amount_check, is_preset_valid = amount.get_amount_from_ai_response(
                            preset_amount, self.minimum_amount, self.maximum_amount, self.vehicle_number_list
                        )
                        if not is_preset_valid:
                            return False, None, is_phrase, preset_amount_check, False, new_llm_answer

                    result['preset_amount'] = preset_amount_check
                    result['vehicle_id'] = self.get_vehicle_id_by_number(result.get('vehicle_number'))
                    return True, result, is_phrase, None, False, new_llm_answer
            except ValueError as e:
                logger.error(f"Error in processing vehicle number and preset amount: {e}")
                return True, None, is_phrase, None, False, new_llm_answer

        return False, None, is_phrase, None, False, new_llm_answer

    def convert_to_json_combine(self, input_string: str) -> dict:
        def parse_value(value):
            if value.isdigit():
                return int(value)
            try:
                return float(value)
            except ValueError:
                return value

        input_string = input_string.strip().lower()
        pattern = re.compile(r"\s*(\w+(?: \w+)*?)\s*:\s*'?(.*?)'?\s*(?=,|;|$)")
        matches = pattern.findall(input_string)

        data_dict = {}
        for key, value in matches:
            key = key.strip().replace(' ', '_')
            data_dict[key] = parse_value(value.strip())

        return data_dict

    def get_amount(self, user_input):
        return amount.get_amount_from_user_response(user_input, self.minimum_amount, self.maximum_amount,
                                                    self.vehicle_number_list)

    def get_vehicle_id_by_number(self, vehicle_number: str) -> Optional[str]:
        """Get the vehicle ID by vehicle number."""
        for vehicle in self.user.vehicles:
            if vehicle['number'].lower() == vehicle_number:
                return vehicle['id']
        return None

    def post_vehicle_number_and_preset_amount(self, ai_response: str, result: Optional[Dict]) -> str:
        """Post the vehicle number and preset amount to the API."""
        if result:
            """
            is_valid_vehicle, response = self.validate_vehicle(result.get('vehicle_number'))
            if is_valid_vehicle:
                return ongo_app_api.get_response(result)
            else:
                return response
            """
            return ongo_app_api.get_response(result)
        else:
            """
            if "vehicle" in ai_response and "preset" in ai_response:
                return ai_response
            else:
                return vehicle_preset_bot.get_current_vehicle_preset(self.assistant.conversation_history)
            """
            return f"error: {static_responses.un_processable('')}"

        return ags_api_chain.run(ai_response)

    def validate_vehicle(self, result: str) -> Tuple[bool, str]:
        """Validate the vehicle number."""
        if result in self.vehicle_number_list:
            return True, result
        elif result == "all":
            return True, result
        else:
            return False, "error: please provide a valid vehicle number"
