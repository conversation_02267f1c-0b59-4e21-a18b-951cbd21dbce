import os
import re
import time
from threading import Thread
from typing import Op<PERSON>, Tu<PERSON>, List

import openai
from dotenv import load_dotenv
from fuzzywuzzy import process
from langchain.llms import OpenAI
from openai import OpenAIError

from executors import static_responses, files
from modules.exceptions import MissingEnvVars
from modules.logger import logger
from modules.text import word_to_number, amount
from modules.utils import util

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')


class VehiclePresetAssistant:
    def __init__(self, mobile: str, session: str, full_name: str, vehicle_number_list: List[str], loop: None):
        self.full_name = full_name
        self.first_name = self._extract_first_name(full_name)
        self.vehicle_number_list = vehicle_number_list
        # self.greeting = "Thank you for calling Ongo. Could you please provide the vehicle number you wish to preset now?"
        self.greeting = f"{static_responses.greeting()} {self.first_name}, Good {util.get_part_of_day()}!, Thank you for calling ongo, "
        # self.vehicle_validator = vehicle_validator
        self.conversation_history = []
        self.llm = OpenAI()
        self.authenticated = False
        self.model = "gpt-3.5-turbo"  # "gpt-3.5-turbo" "gpt-4o"
        clear_model = re.sub(r'\W', '_', self.model)
        self.file_name = f"{clear_model}.yaml"
        self.minimum_amount = 30
        self.maximum_amount = 100000
        self.title = ""  # sir or madam
        self.session = session
        self.vehicle_number = None
        self.preset_amount = None
        self.mobile = mobile

        self.client = openai

    def _extract_first_name(self, full_name: str) -> str:
        """Extracts the first name from the full name."""
        return full_name.split()[0] if full_name else "Customer"

    def start_new_call(self):
        self.authenticate()
        if not self.authenticated:
            raise MissingEnvVars  # todo
        # self.greeting = self.vehicle_validator.ai_response_greeting_validation(self.greeting)
        return self.greeting

    def authenticate(self) -> None:
        """Initiates authentication and prepares GPT responses ready to be audio fed."""
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            logger.warning("'openai_api' wasn't found to proceed")
            return

        os.environ["OPENAI_API_KEY"] = api_key
        openai.api_key = api_key

        content = self._generate_initial_content()
        self.conversation_history.append(self._create_message("system", content))

        try:
            chat_response = self._get_chat_response()
            self._process_chat_response(chat_response)
            self.authenticated = True
        except OpenAIError as error:
            logger.error(error)
        except Exception as error:
            logger.critical(error)

    def _update_context_message(self, context):
        if context and self.conversation_history:
            system_message = self.conversation_history[0]
            if system_message.get('role') == 'system':
                content = system_message['content']
                # Split the content to isolate the static prompt part
                if "ongo_context:" in content:
                    prompt_parts = content.split("ongo_context:")
                    prompt = prompt_parts[0].strip()
                    answer_context = prompt_parts[1].strip() if len(prompt_parts) > 1 else ""
                else:
                    prompt = content.strip()
                    answer_context = ""

                # Check if the new context is already present
                if context not in answer_context:
                    # Construct the new content
                    new_content = f"{prompt}\nongo_context:\n{context} {answer_context}".strip()
                    # Update the existing context message
                    self.conversation_history[0]['content'] = new_content

    def query(self, phrase: str, context: str) -> str:
        """Queries ChatGPT API with the request and returns the response.

        Args:
            phrase: The phrase spoken by the user.
            :param phrase:
            :param context:
        """
        self._update_context_message(context)
        phrase = phrase.lower()
        # phrase, is_found_vehicle = self.get_user_vehicle_number(phrase)
        self.conversation_history.append(self._create_message("user", phrase))

        # Extract and store the vehicle number and preset amount if provided by the user
        previous_vehicle_number = self.vehicle_number
        self._store_vehicle_number(phrase)
        self._store_preset_amount(phrase)

        if previous_vehicle_number and self.vehicle_number != previous_vehicle_number:
            # Vehicle number changed, confirm with the user
            confirm_change_message = f"It looks like you changed the vehicle number from {previous_vehicle_number} to {self.vehicle_number}. Do you want to proceed with the new vehicle number?"
            self.conversation_history.append(self._create_message("system", confirm_change_message))
            return confirm_change_message

        try:
            start_time = time.time()  # Start timing
            chat_response = self._get_chat_response()
            end_time = time.time()  # End timing
            response_time = end_time - start_time  # Calculate the duration
        except OpenAIError as error:
            logger.error(error)
            return static_responses.un_processable(self.title)

        if chat_response.choices:
            reply = chat_response.choices[0].message.content.lower()
            self.conversation_history.append(self._create_message("assistant", reply))
            Thread(target=self._dump_history, args=(phrase, reply, f"{response_time:.2f}")).start()

            return reply
        else:
            logger.error(chat_response)
            return static_responses.un_processable(self.title)

    def answer_call_back(self, human_input: str = None, context: str = None) -> str:
        """Handles user input and returns the assistant's response.

        Args:
            human_input: The input text from the user.

        Returns:
            The response text from the assistant.
            :param human_input:
            :param context:
        """
        if not human_input:
            return "Sorry, I didn't get that. Please try again."

        return self.query(human_input.lower(), context)

    def _dump_history(self, request: str, response: str, response_time: str) -> None:
        """Dumps the conversation history to a YAML file.

        Args:
            request: The request from the user.
            response: The response from the assistant.
        """
        data = files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []
        data.append({'request': request, 'response': response, 'response_time': response_time})
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)

    def _generate_initial_content(self) -> str:
        """Generates the initial content based on the vehicle number list."""
        base_content = (
            """
            You are a vehicle preset amount assistant. Your responses will be audio fed,
            so keep them concise within 1 to 2 sentences without any parenthesis.
            You need to:
            """
        )

        if not self.vehicle_number_list:
            content = (
                f"""
                {base_content}"
                1. Inform the user that their vehicle is not tagged and they should go to the nearest petrol pump to obtain a tag.
                2. Once the vehicle is tagged, they can call again for assistance.
                3. If the user question is related to the given ongo_context, provide an answer and Do ask follow-up questions if necessary.
                
                ongo_context:
                
                """
            )
        elif len(self.vehicle_number_list) == 1:
            self.vehicle_number = self.vehicle_number_list[0]
            content = (
                f"""
                {base_content}
                1. Directly ask for the preset amount for the vehicle number '{self.vehicle_number}'.
                2. Ensure that the preset amount is not null or empty.
                3. If the user question is related to the given ongo_context, provide an answer and Do ask follow-up questions if necessary.
                4. If the user mentions 'full tank' or related terms, ask for confirmation by saying 'Do you want to set the preset amount to full?'. If confirmed, set the preset amount to 'full'.
                5. If the user mentions 'stop fueling' or related terms, ask for confirmation by saying 'Do you want to set the preset amount to stop?'. If confirmed, set the preset amount to 'stop'.
                6. Once you have obtained the preset amount, convert it to an integer if it is not 'full' or 'stop'.
                7. Ensure the vehicle number is '{self.vehicle_number}'.
                8. If the user asks for the vehicle list or has other related questions, inform them about the available vehicle number '{self.vehicle_number}'.
                9. Once you have obtained the vehicle number and preset amount, respond only 'vehicle_number: '{self.vehicle_number}', preset_amount: 'preset amount', output: completed'.
                
                ongo_context:
                
                """
            )
        else:
            vehicle_numbers = ", ".join(self.vehicle_number_list).lower()
            content = (
                f"""
                {base_content}
                1. Ask questions to obtain the vehicle number and preset amount from the user. 
                2. Ensure that both the vehicle number and preset amount are not null or empty.
                3. If the user question is related to the given ongo_context, provide an answer and Do ask follow-up questions if necessary.
                4. If the user mentions 'full tank' or related terms, ask for confirmation by saying 'Do you want to set the preset amount to full?'. If confirmed, set the preset amount to 'full'.
                5. If the user mentions 'stop fueling' or related terms, ask for confirmation by saying 'Do you want to set the preset amount to stop?'. If confirmed, set the preset amount to 'stop'.
                6. If the user wants to set the preset amount for all vehicles, respond with 'Are you sure you want to set the preset amount for all vehicles?'.
                7. If the user confirms with 'yes' or related terms, ask 'What is the preset amount you want to set for all vehicles?'
                8. Once the user confirms, respond only with 'vehicle_number: all, preset_amount: preset amount, output: completed'.
                9. Once you have obtained the preset amount, convert it to an integer if it is not 'full' or 'stop'.
                10. If the user requests a list of vehicle numbers, provide the list and then ask a follow-up question related to 'Which vehicle number are you setting the preset amount for?'.
                11. Ensure the vehicle number is in the list of user vehicle numbers ({vehicle_numbers}) or 'all' if the user wants to set all vehicles.
                12. Once you have obtained the vehicle number and preset amount, respond only 'vehicle_number: 'vehicle number', preset_amount: 'preset amount', output: completed'.
                
                ongo_context:
                
                """
            )

        return content

    def _store_vehicle_number(self, phrase: str) -> None:
        previous_vehicle_number = self.vehicle_number
        """stores the vehicle number from the user's input."""
        for vehicle_number in self.vehicle_number_list:
            if vehicle_number.lower() in phrase:
                self.vehicle_number = vehicle_number
                break

        if "all" in phrase:
            self.vehicle_number = "all"

        if self.vehicle_number:
            # Create the message for the vehicle number
            previous_vehicle_number_msg = self._create_vehicle_number_message(previous_vehicle_number)
            vehicle_number_msg = self._create_vehicle_number_message(self.vehicle_number)
            # Check if the vehicle number message already exists in conversation_history
            found = False
            for i, message in enumerate(self.conversation_history):
                if message.get('content') == previous_vehicle_number_msg:
                    # Update the existing message
                    self.conversation_history[i] = self._create_message("system", vehicle_number_msg)
                    found = True
                    break

            # If not found, append the new message
            if not found:
                self.conversation_history.append(self._create_message("system", vehicle_number_msg))

    def _create_vehicle_number_message(self, vehicle_number: str) -> str:
        """
        Creates a vehicle number identification message.

        :param vehicle_number: The vehicle number to include in the message.
        :return: A formatted string containing the vehicle number message.
        """
        return f"Vehicle number identified: {vehicle_number}"

    def _store_preset_amount(self, phrase: str) -> None:
        """Extracts and stores the preset amount from the user's input."""
        previous_preset_amount = self.preset_amount
        if 'full tank' in phrase or 'full' in phrase:
            self.preset_amount = 'full'
        elif 'stop fueling' in phrase or 'stop' in phrase:
            self.preset_amount = 'stop'

        preset_amount = amount.get_amount_from_string(phrase, self.vehicle_number_list)
        if preset_amount:
            self.preset_amount = preset_amount

        if self.preset_amount:
            previous_preset_amount_msg = self._create_preset_amount_message(previous_preset_amount)
            preset_amount_msg = self._create_preset_amount_message(self.preset_amount)
            self.conversation_history.append(self._create_message("system", preset_amount_msg))
            found = False
            for i, message in enumerate(self.conversation_history):
                if message.get('content') == previous_preset_amount_msg:
                    # Update the existing message
                    self.conversation_history[i] = self._create_message("system", preset_amount_msg)
                    found = True
                    break

            # If not found, append the new message
            if not found:
                self.conversation_history.append(self._create_message("system", preset_amount_msg))

    def _create_preset_amount_message(self, preset_amount: str) -> str:
        """
        Creates a preset amount identification message.

        :param preset_amount: The preset amount to include in the message.
        :return: A formatted string containing the preset amount message.
        """
        return f"Preset amount identified: {preset_amount}"

    def _create_message(self, role: str, content: str) -> dict:
        """Creates a message dictionary for the conversation history."""
        return {"role": role, "content": content}

    def _get_chat_response(self):
        """Gets the chat response from the OpenAI API and prints the response time."""

        response = self.client.chat.completions.create(
            messages=self.conversation_history,
            model=self.model,
            temperature=0.3,
            max_tokens=50,
            top_p=1,
            frequency_penalty=0,
            presence_penalty=0
        )

        return response

    def _process_chat_response(self, chat_response) -> None:
        """Processes the chat response and updates the conversation history."""
        response_content = chat_response.choices[0].message.content
        self.greeting += response_content
        self.conversation_history.append(self._create_message("system", response_content))

    def _extract_vehicle_number(self, text: str) -> Tuple[str, Optional[str]]:
        normalized_text = text.lower()
        normalized_text_remove_space = self.normalize_vehicle_number(normalized_text)

        vehicle_number = None

        best_match, ratio = process.extractOne(normalized_text_remove_space, self.vehicle_number_list)
        sub_string = ""
        if ratio >= 40:
            vehicle_number = best_match
            match_start = -1
            match_end = -1
            for i in range(len(normalized_text_remove_space) - len(best_match) + 1):
                sub_string = normalized_text_remove_space[i:i + len(best_match)]
                ratio = process.extractOne(sub_string, [best_match])[1]
                if ratio >= 80:
                    match_start = i
                    match_end = i + len(best_match)
                    break

            if match_start != -1:
                normalized_text_remove_space = normalized_text_remove_space[
                                               :match_start] + normalized_text_remove_space[match_end:]

        if vehicle_number:
            vehicle_number = self._remove_symbols(vehicle_number)

        return vehicle_number, sub_string

    def normalize_vehicle_number(self, vehicle_number: str) -> str:
        return vehicle_number.replace(" ", "").lower()

    def count_vehicle_numbers_with_substring(self, sub_string: str) -> int:
        return sum(1 for vehicle in self.vehicle_number_list if sub_string in vehicle)

    def _get_user_conversation_summary(self) -> Optional[str]:
        vehicle_number = None
        preset_amount = None
        sub_string = None
        for message in self.conversation_history:
            if message.role == "user":
                potential_amount = None
                potential_vehicle_number, sub_string = self._extract_vehicle_number(message.content)
                if potential_vehicle_number:
                    vehicle_number = potential_vehicle_number
                if potential_amount:
                    preset_amount = potential_amount

        if vehicle_number and preset_amount:
            return f"Vehicle number: {vehicle_number}, Preset amount: {preset_amount}, Match count: {self.count_vehicle_numbers_with_substring(sub_string)}"
        elif vehicle_number:
            return f", Vehicle number is {vehicle_number}."
        elif preset_amount:
            return ""
        else:
            return ""

    def _remove_symbols(self, sentence):
        # Define a regular expression pattern that matches any character that is not a letter, a number, or a period
        pattern = re.compile('[^A-Za-z0-9 ]+')
        # Use the pattern to substitute all matching characters with an empty string
        cleaned_sentence = pattern.sub(' ', sentence)
        return cleaned_sentence

    def output_completed(self) -> Tuple[Optional[str], bool]:

        # Ensure preset_amount is either a string or a number and check its value
        if isinstance(self.preset_amount, str) and self.preset_amount.strip():
            preset_amount_valid = True
        elif isinstance(self.preset_amount, (int, float)) and self.preset_amount > 0:
            preset_amount_valid = True
        else:
            preset_amount_valid = False

        if self.vehicle_number and preset_amount_valid:
            return f"vehicle_number: '{self.vehicle_number}', preset_amount: '{self.preset_amount}', output: completed", True
        elif not self.vehicle_number and not preset_amount_valid:
            return "Please provide both vehicle number and preset amount.", False
        elif not self.vehicle_number:
            return "Please provide vehicle number.", False
        elif not preset_amount_valid:
            return "Please provide preset amount.", False
        return None, False


def print_interaction(user_input: str, response: str):
    print(f"User: {user_input}")
    print(f"Assistant: {response}\n")


"""

print(remove_symbols("A B 123 and 100, Sorry, my vehicle @ $^&&^%!number is ABC.123."))

# Example usage:
vehicle_number_list = ["ABC123", "XYZ789"]
assistant = VehiclePresetAssistant(vehicle_number_list)
assistant.start_new_call()

print_interaction("What vehicles do you have?", assistant.answer_call_back("What vehicles do you have?"))
print_interaction("A B 123 and 100, Sorry, my vehicle number is ABC123.",
                  assistant.answer_call_back("A B 123 and 100, Sorry, my vehicle number is ABC123."))
user_input = "1000"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)
if "completed" in response:
    print("done")
else:
    user_input = "yes"
    response = assistant.answer_call_back(user_input)
    print_interaction(user_input, response)


test_strings = [
    "Can you give me a list of available vehicle numbers?",
    "What vehicles do you have?",
    "Please provide the vehicle numbers.",
    "List all the vehicle numbers.",
    "I need to see the available vehicles.",
    "Show me the vehicle list.",
    "Can I get a list of the vehicles?",
    "What are the vehicle numbers I can choose from?",
    "Provide me with the list of vehicles.",
    "Tell me the available vehicle numbers.",
    "Could you list the vehicle numbers for me?",
    "What vehicle numbers do you have?",
    "Give me the list of vehicle numbers.",
    "I want to know the vehicle numbers.",
    "Please show me the list of vehicles.",
]
for i, test_string in enumerate(test_strings, 1):
    print_interaction("", assistant.start_new_call())
    print_interaction(test_string, assistant.answer_call_back(test_string))
"""

"""
# print(assistant.answer_call_back("I'd like to preset my vehicle ABC123 and preset amount 123"))
print(assistant.answer_call_back("ABC123"))

print(assistant.answer_call_back("123"))

print(assistant.answer_call_back("5000"))

# Test strings
test_strings = [
    "Vehicle number: ap31az8789, preset amount: 10000, output: completed.",
    "Vehicle number: ap 31 az 8789, preset amount: 20000, status: in progress, message: all good.",
    "Vehicle number: 'ap31az8789', preset amount: '10000', output: 'completed'.",
    "Vehicle number: ap31az8789, preset amount: 10000.50, discount: 25, output: completed, approved: True.",
    "Vehicle number: ap31az8789, preset amount: 10000.75, tax rate: 0.18, output: completed.",
    "Vehicle number: ap31az8789, preset amount: 10000, approved: True, output: completed.",
    "Vehicle number: ap31az8789, description: high-end model, preset amount: 10000, output: completed.",
    "Vehicle number: ap31az8789, preset amount: 10000, output: , approved: True.",
    "output: completed.",
    "Vehicle number:ap31az8789,preset amount:10000,output:completed.",
    "Vehicle number: ap31az8789; preset amount: 10000; output: completed.",
    "Vehicle details: {'number': 'ap31az8789', 'model': 'Sedan'}, preset amount: 10000, output: completed.",
    " Vehicle number :  ap31az8789 ,  preset amount :  10000 ,  output :  completed . ",
    "Vehicle number: ap31az8789, preset amount:, output: completed."
]

for i, test_string in enumerate(test_strings, 1):
    result_dict = convert_to_json_combine(test_string)
    print(f"Test Case {i}:\n{json.dumps(result_dict, indent=4)}\n")


conversation_history.append(Message("user", "My vehicle number is ABC 123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB 23 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is a b 123 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B 1 23 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B C 1 2 3 and I want to preset 5000"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B C 12 34 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is A B 1 2 34 and I want to preset 50000"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is AB1234 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()
conversation_history.append(Message("user", "My vehicle number is 1234 and I want to preset 500"))
print(get_user_conversation_summary())
conversation_history.clear()


# Simulate the conversation with debugging outputs
print("Initial Greeting:")
print(greeting)
print()

vehicle_number_list = ["AB1234", "XYZ789"]
assistant = VehiclePresetAssistant(vehicle_number_list)

user_input = "My vehicle number is AB1234 and I want to preset 500."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AB1234"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 500."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "can you provide me vehicle list"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "ab1234 and 500"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is ab 1 2 and I want to preset 500."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is A123, my vehicle number is ab1234"
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 25."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AB1234 and I want to preset 50000."
response = assistant.answer_call_back(user_input)
print_interaction(user_input, response)
# Start a new call to reset the conversation
print("Starting a new call:")
start_new_call()
print(greeting)
print()

user_input = "My vehicle number is AC1234 and I want to preset 1000."
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "My vehicle number is AC1234"
response = answer_call_back(user_input)
print_interaction(user_input, response)

user_input = "I want to preset 1000"
response = answer_call_back(user_input)
print_interaction(user_input, response)
"""
