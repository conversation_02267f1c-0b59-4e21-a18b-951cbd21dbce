import json

ags_api_docs = {
    "base_url": "http://127.0.0.1:5000/",
    "endpoints": {
        "/get-vehicle-list": {
            "method": "GET",
            "description": "Retrieve a list of vehicles.",
            "parameters": None,
            "response": {
                "description": "A JSON object containing a list of vehicles.",
                "content_type": "application/json"
            }
        },
        "/preset-vehicle": {
            "method": "GET",
            "description": "Validate a vehicle number and preset amount. If both are valid, the vehicle will be preset with the specified amount.",
            "parameters": {
                "description": "Query parameter in the URL.",
                "vehicle_number": {
                    "type": "string",
                    "description": "The vehicle number to be validated."
                },
                "amount": {
                    "type": "number",
                    "description": "The preset amount to be validated."
                }
            },
            "response": {
                "description": "A JSON object indicating whether the vehicle number is valid or not.",
                "content_type": "application/json"
            }
        }
    }
}

ags_api_docs = json.dumps(ags_api_docs, indent=2)
