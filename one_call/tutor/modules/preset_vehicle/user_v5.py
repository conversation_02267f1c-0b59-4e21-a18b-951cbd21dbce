import asyncio
import json
from concurrent.futures import ThreadPoolExecutor

from modules.audio.audio_processor import AudioProcessor
from modules.logger import logger
from modules.manager.bot_manager import BotManager
from modules.preset_vehicle.vehicle_validator import VehicleValidator


def process_data(data):
    vehicles = data.get('vehicles', [])
    vehicle_data = {}
    for vehicle in vehicles:
        make = vehicle['make'].lower()
        number = vehicle['number'].lower()
        if make in vehicle_data:
            vehicle_data[make].append(number)
        else:
            vehicle_data[make] = [number]
    return vehicle_data


class User:
    def __init__(self, session: str, name: str, websocket, data: dict, event_loop):
        self.session = session
        self.name = name
        self.conn = websocket
        self.mobile = data.get('mobile')
        self.user_id = data.get('userId')
        self.vehicles = data.get('vehicles', [])
        self.vehicle_data = process_data(data)
        self.loop = event_loop
        self.is_end_call = False
        self.ai_start_listening = False
        self.executor = ThreadPoolExecutor()

        self.audio_processor = AudioProcessor(self)
        self.realtime_transcription = None
        # self.vehicle_validator = VehicleValidator(self)
        self.bot_manager = BotManager(self)

    def __repr__(self):
        return (f"User(session={self.session}, name={self.name}, mobile={self.mobile}, "
                f"user_id={self.user_id}, vehicles={self.vehicles})")

    async def send(self, message: str):
        """Send a message to the user."""
        try:
            await self.conn.send(message)
        except Exception as e:
            pass
            # logger.error(f"Failed to send message: {e}")

    def sync_send(self, message: str):
        """Send a message to the user synchronously."""
        asyncio.run_coroutine_threadsafe(self.send(message), self.loop)

    def start_ai_call(self):
        """Start AI call for the user."""
        self.audio_processor.start_ai_call()

    def end_ai_call(self):
        """End AI call for the user and save audio file."""
        self.is_end_call = True
        self._run_async(self.conn.close())
        self.audio_processor.save_audio_file()

    def send_end_call(self):
        """Notify the user that the AI call has ended and close the connection."""
        self.is_end_call = True
        self.sync_send(json.dumps({"type": "ai_end_call", "data": "ai call completed"}))
        self._run_async(self.conn.close())
        self.audio_processor.save_audio_file()

    def _run_async(self, coroutine):
        """Run an asynchronous coroutine."""
        asyncio.run_coroutine_threadsafe(coroutine, self.loop)
