import os
from dotenv import load_dotenv

from dataclasses import dataclass
from typing import Literal

from langchain.chains import <PERSON>vers<PERSON><PERSON><PERSON><PERSON>
from langchain.memory import ConversationBufferMemory
from langchain.prompts.prompt import PromptTemplate
from langchain_openai import ChatOpenAI
from langchain.llms import OpenAI

load_dotenv()

os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')

vehicle_numbers = ["AB1234", "BC1234", "AC1234"]

@dataclass
class Message:
    """Class for keeping track of interview history."""
    role: Literal["user", "assistant"]
    content: str

greeting = "Thank you for calling On<PERSON>. could you please provide the vehicle number and the amount you wish to preset now?"

conversation_history = []

conversation_memory = ConversationBufferMemory(human_prefix="User: ", ai_prefix="Assistant")

#llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0)
llm = OpenAI(model='gpt-3.5-turbo-instruct', temperature=0)

PROMPT_TEMPLATE = """
    You are an vehicle preset assistant,so keep them short, like 1 or 2 sentences.
    Your task is to retrieve data from the user regarding vehicle number and their respective preset amount. 
    Ask questions to the user to gather this information.
    Ensure that both the vehicle number and preset amount are provided and not null or empty.
    Ensure the preset amount provided is within the range of 100 to 10,000.
    Once you have obtained the vehicle number and preset amount from the user, respond with "Output: completed".
    Validate the vehicle number against the provided list of {vehicle_numbers}.
    
    Remember to ask only one question at a time and to prompt follow-up questions if necessary. 

    This includes vehicle list, and general vehicle number, preset amount related queries. 
    You do not provide information outside of this scope.

    Current Conversation:
    {history}
    User: {input}
    Assistant:
    """

PROMPT_TEMPLATE = PROMPT_TEMPLATE.replace("{vehicle_numbers}", ", ".join(vehicle_numbers))

PROMPT = PromptTemplate(input_variables=["history", "input"], template=PROMPT_TEMPLATE)

conversation_chain = ConversationChain(prompt=PROMPT, llm=llm, memory=conversation_memory)

def answer_call_back(human_input: str):
    if not human_input:
        input_text = "Sorry, I didn't get that. Please try again."
        # conversation_history.append(Message("ai", input_text))
        return input_text
    else:
        input_text = human_input

    conversation_history.append(Message("user", input_text))
    ai_response = conversation_chain.run(input_text)
    conversation_history.append(Message("assistant", ai_response))
    return ai_response


def start_new_call():
    conversation_memory.clear()
    conversation_history.clear()
