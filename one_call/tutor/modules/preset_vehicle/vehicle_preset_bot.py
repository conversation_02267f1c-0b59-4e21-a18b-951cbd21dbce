import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv('OPENAI_API_KEY')

def get_current_vehicle_preset(chat_history):
    llm = ChatOpenAI(model_name="gpt-3.5-turbo", temperature=0.7)
    query_template = """
                   You are a vehicle preset assistant chatbot named "<PERSON>".
                   Based on the conversation history, please provide the current vehicle number and preset amount.

                   Conversation History:
                   {history}

                   User: What is my current vehicle number and preset amount?
                   Assistant:
                   """
    history_text = "\n".join([f"{entry.role}: {entry.content}" for entry in chat_history])
    query = query_template.replace("{history}", history_text)
    response = llm.invoke(query)
    return response.content
