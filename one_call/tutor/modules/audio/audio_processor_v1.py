import asyncio
from collections import deque

from modules.audio.listener import AudioRecognizer
from modules.audio.text_speech import TextToSpeech


class AudioProcessor:
    def __init__(self, user):
        self.user = user
        self.audio_queue = deque()
        self.recognizer = AudioRecognizer(user)
        self.tts = TextToSpeech()
        self.audio_chunk_is_send = False

    def start_ai_call(self):
        """Start AI call for the user."""
        self.recognizer.start_ai_call()

    def end_ai_call(self):
        """End AI call for the user and save audio file."""
        self.user.is_end_call = True
        self._run_async(self.user.conn.close())
        self.recognizer.save_audio_file()

    def _run_async(self, coroutine):
        """Run an asynchronous coroutine."""
        asyncio.run_coroutine_threadsafe(coroutine, self.user.loop)

    def save_audio_file(self):
        self.recognizer.save_audio_file()

    def process_audio_chunk(self, data):
        """Process incoming audio chunks."""
        audio_bytes = bytes([x & 0xFF for x in data])
        self.audio_queue.append(audio_bytes)
