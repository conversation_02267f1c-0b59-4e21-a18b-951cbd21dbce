import os
import re
import unittest
from pathlib import Path
from difflib import Sequence<PERSON>atch<PERSON>
from dotenv import load_dotenv
from gtts import gTTS
from pydub import AudioSegment
import io
import pyaudio
import openai
import re
from datetime import datetime
import logging


class TextToSpeech:
    def __init__(self, voice_type="google"):
        load_dotenv()
        self.api_key = os.getenv('OPENAI_API_KEY')
        openai.api_key = self.api_key
        self.client = openai
        self.audio_dir = Path(__file__).parent / f"audio_file_{voice_type}"
        self.audio_dir.mkdir(exist_ok=True)
        self.voice_type = voice_type
        self.logger = logging.getLogger(__name__)

    def calculate_similarity(self, text1, text2):
        """Calculate the similarity ratio between two strings."""
        return SequenceMatcher(None, text1, text2).ratio()

    def find_existing_audio(self, text):
        """Check if the audio for the given text already exists in the audio directory."""
        # Check if the text contains any digits
        if any(char.isdigit() for char in text):
            return None, True  # Skip comparison and return None

        for file in os.listdir(self.audio_dir):
            if file.endswith(".mp3"):
                txt_file_path = self.audio_dir / file.replace(".mp3", ".txt")
                if txt_file_path.exists():
                    with open(txt_file_path, "r") as f:
                        saved_text = f.read()
                        if self.calculate_similarity(text, saved_text) >= 0.98:
                            return self.audio_dir / file, False
        return None, False

    def generate_audio(self, text):
        # Process text for replacements
        text = self.process_text(text)
        # Define the path to save the new audio file
        speech_file_path = self.audio_dir / f"{hash(text)}.mp3"
        """Convert text to audio using OpenAI's text-to-speech API and return the audio stream."""
        existing_audio_path, isdigit = self.find_existing_audio(text)
        if existing_audio_path:
            # Load existing audio file
            audio_segment = AudioSegment.from_file(existing_audio_path, format="mp3")
        elif self.voice_type == "alloy":
            # Generate speech using OpenAI API
            response = self.client.audio.speech.create(
                model="tts-1",
                voice="alloy",
                input=text
            )

            response.write_to_file(speech_file_path)

            # Save the corresponding text for future comparison
            with open(speech_file_path.with_suffix('.txt'), "w") as f:
                f.write(text)

            # Load the newly saved audio file
            audio_segment = AudioSegment.from_file(speech_file_path, format="mp3")
        else:
            tts = gTTS(text=text, lang='en', slow=False, tld='co.in')
            if isdigit:
                # Save speech to a byte stream
                audio_stream = io.BytesIO()
                tts.write_to_fp(audio_stream)
                audio_stream.seek(0)

                # Convert audio to PCM format
                audio_segment = AudioSegment.from_file(audio_stream, format="mp3")
            else:
                tts.save(speech_file_path)
                # Save the corresponding text for future comparison
                with open(speech_file_path.with_suffix('.txt'), "w") as f:
                    f.write(text)

                # Load the newly saved audio file
                audio_segment = AudioSegment.from_file(speech_file_path, format="mp3")

        # Convert the audio file to PCM format
        pcm_audio = audio_segment.set_frame_rate(44100).set_channels(1).set_sample_width(2)
        pcm_audio_1 = audio_segment.set_frame_rate(16000).set_channels(1).set_sample_width(2)

        # Save PCM audio to a byte stream
        pcm_audio_stream = io.BytesIO()
        pcm_audio.export(pcm_audio_stream, format="raw")
        pcm_audio_stream.seek(0)

        pcm_audio_stream_1 = io.BytesIO()
        pcm_audio_1.export(pcm_audio_stream_1, format="wav")
        pcm_audio_stream_1.seek(0)

        return pcm_audio_stream, pcm_audio_stream_1

    def play_audio_stream(self, pcm_audio_stream):
        """Play an audio stream using PyAudio."""
        # Initialize PyAudio
        p = pyaudio.PyAudio()

        # Open a stream
        stream = p.open(format=p.get_format_from_width(2),  # 16-bit audio
                        channels=1,  # Mono
                        rate=44100,  # 44.1 kHz
                        output=True)

        # Read and play audio stream in chunks
        chunk_size = 1024
        pcm_audio_stream.seek(0)
        data = pcm_audio_stream.read(chunk_size)
        while data:
            stream.write(data)
            data = pcm_audio_stream.read(chunk_size)

        # Stop and close the stream
        stream.stop_stream()
        stream.close()
        p.terminate()

    def process_text(self, text):
        """Process text for replacements."""
        text = text.replace("%", " percent")
        text = text.replace('\n', '\t').strip()
        if time_in_str := re.findall(r'(\d+:\d+\s?(?:AM|PM|am|pm:?))', text):
            for t_12 in time_in_str:
                t_24 = datetime.strftime(datetime.strptime(t_12, "%I:%M %p"), "%H:%M")
                self.logger.info("Converted %s -> %s", t_12, t_24)
                text = text.replace(t_12, t_24)

        if 'IP' in text.split():
            ip_new = '-'.join([i for i in text.split(' ')[-1]]).replace('-.-',
                                                                        ', ')  # *********** -> 1-9-2, 1-6-8, 1, 1
            text = text.replace(text.split(' ')[-1], ip_new).replace(' IP ', ' I.P. ')

        # Raises UnicodeDecodeError within docker container
        text = text.replace("\N{DEGREE SIGN}F", " degrees fahrenheit")
        text = text.replace("\N{DEGREE SIGN}C", " degrees celsius")
        text = ' '.join(text.split())

        return text.strip()


# Example usage

"""
if __name__ == "__main__":
    tts = TextToSpeech()
    text = "Hello, this is a test message."
    audio_stream = tts.generate_audio(text)
    tts.play_audio_stream(audio_stream)

    text = "This is a\nmultiline\nstring"
    audio_stream = tts.generate_audio(text)
    tts.play_audio_stream(audio_stream)

    text = "This is a\tmultiline\tstring 100"
    audio_stream = tts.generate_audio(text)
    tts.play_audio_stream(audio_stream)

    text = "This is a\tmultiline\tstring 2000"
    audio_stream = tts.generate_audio(text)
    tts.play_audio_stream(audio_stream)
"""
