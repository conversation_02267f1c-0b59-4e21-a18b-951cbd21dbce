import io
import threading
import time
from collections import deque
from typing import Optional, Callable
import asyncio
import json

from google.cloud import speech
import google.auth
from google.api_core import exceptions as google_exceptions

from tutor.executors.entry_dumper import EntryDumper
from tutor.executors.request_handler import Re<PERSON><PERSON><PERSON>ler


class GoogleCloudTranscriptResult:
    """Wrapper class to mimic AssemblyAI transcript structure"""
    def __init__(self, text: str, is_final: bool = False):
        self.text = text
        self.is_final = is_final


class GoogleCloudFinalTranscript(GoogleCloudTranscriptResult):
    """Mimic AssemblyAI RealtimeFinalTranscript"""
    def __init__(self, text: str):
        super().__init__(text, is_final=True)


class GoogleCloudPartialTranscript(GoogleCloudTranscriptResult):
    """Mimic AssemblyAI RealtimePartialTranscript"""
    def __init__(self, text: str):
        super().__init__(text, is_final=False)


class GoogleCloudSessionInfo:
    """Mimic AssemblyAI RealtimeSessionInformation"""
    def __init__(self, audio_duration_seconds: float = 0.0):
        self.audio_duration_seconds = audio_duration_seconds


class GoogleCloudError(Exception):
    """Mimic AssemblyAI RealtimeError"""
    def __init__(self, message: str):
        self.message = message
        super().__init__(message)


class RealtimeTranscription:
    """Google Cloud Speech realtime transcription implementation that mirrors AssemblyAI interface"""
    
    def __init__(self, project_id: str, credentials_path: str, user, logger, 
                 sample_rate: int = 16000, language_code: str = "en-US"):
        self.user = user
        self.logger = logger
        self.session = user.session
        self.file_name = "realtime_transcription.yaml"
        self.project_id = project_id
        self.credentials_path = credentials_path
        self.sample_rate = sample_rate
        self.language_code = language_code
        self.audio_queue = deque()
        
        # Threading and streaming control
        self.listen_task_threading = threading.Thread(target=self.stream_audio_data, daemon=True)
        self.is_stream_audio_data = True
        self.realtime_final_transcript = None
        self.last_transcript_time = None
        
        # Google Cloud Speech client and stream
        self.client = None
        self.stream_iterator = None
        self.streaming_config = None
        self.response_thread = None
        self.request_queue = deque()
        self.stream_lock = threading.Lock()
        
        # Callback handlers (to match AssemblyAI interface)
        self.on_data_callback = self.on_data_impl
        self.on_error_callback = self.on_error_impl
        self.on_open_callback = self.on_open_impl
        self.on_close_callback = self.on_close_impl
        self.on_extra_session_information_callback = lambda data: self.entry_dumper.start_dump_task(
            "audio_duration_seconds", f"{data.audio_duration_seconds} seconds"
        )
        
        # State tracking
        self.is_completed = True
        self.user_input = ""
        self.last_speak_time = time.time()
        self.last_llm_answer = None
        self.conversation_history = []
        
        # Integration components
        self.entry_dumper = EntryDumper(self.user.mobile, self.session, self.file_name)
        self.request_handler = RequestHandler(self.user, self.entry_dumper, logger)
        
        # Audio duration tracking
        self.audio_start_time = None
        self.total_audio_duration = 0.0
        
        # Initialize Google Cloud Speech client
        self._init_google_client()

    def _init_google_client(self):
        """Initialize Google Cloud Speech client with authentication"""
        try:
            if self.credentials_path:
                import os
                os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = self.credentials_path
                
            self.client = speech.SpeechClient()
            self.logger.info("Google Cloud Speech client initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Google Cloud Speech client: {e}")
            raise GoogleCloudError(f"Client initialization failed: {e}")

    def _create_streaming_config(self):
        """Create streaming recognition configuration"""
        self.recognition_config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
            sample_rate_hertz=self.sample_rate,
            language_code=self.language_code,
        )
        
        self.streaming_config = speech.StreamingRecognitionConfig(
            config=self.recognition_config,
            interim_results=True,
        )

    def _request_generator(self):
        """Generator that yields StreamingRecognizeRequest objects"""
        # Only yield audio data requests - config is passed separately
        while self.is_stream_audio_data:
            if self.request_queue:
                with self.stream_lock:
                    if self.request_queue:
                        request = self.request_queue.popleft()
                        yield request
            else:
                time.sleep(0.01)

    def _process_responses(self):
        """Process streaming recognition responses"""
        responses_processed = 0
        empty_responses = 0
        results_with_transcripts = 0
        
        try:
            if not self.stream_iterator:
                self.logger.error("No active stream iterator for processing responses")
                return
                
            self.logger.info("Google Cloud Speech response processing started - waiting for responses...")
            
            for response in self.stream_iterator:
                responses_processed += 1
                
                # Log first few responses for debugging
                if responses_processed <= 5:
                    self.logger.info(f"Google Cloud Speech: Received response #{responses_processed}")
                
                if not self.is_stream_audio_data:
                    self.logger.info(f"Google Cloud Speech response processing stopped by is_stream_audio_data flag after {responses_processed} responses")
                    break
                    
                if not response.results:
                    empty_responses += 1
                    # Log empty responses occasionally to track streaming activity
                    if responses_processed <= 10 or responses_processed % 50 == 0:
                        self.logger.info(f"Google Cloud Speech: Response #{responses_processed} was empty (total empty: {empty_responses})")
                    continue
                
                # We have results!
                self.logger.info(f"Google Cloud Speech: Response #{responses_processed} has {len(response.results)} results")
                    
                for result_idx, result in enumerate(response.results):
                    if not result.alternatives:
                        self.logger.info(f"Google Cloud Speech: Result {result_idx} has no alternatives")
                        continue
                    
                    self.logger.info(f"Google Cloud Speech: Result {result_idx} has {len(result.alternatives)} alternatives")
                        
                    transcript = result.alternatives[0].transcript
                    if not transcript.strip():
                        self.logger.info(f"Google Cloud Speech: Result {result_idx} has empty transcript")
                        continue
                    
                    results_with_transcripts += 1
                    
                    if result.is_final:
                        # Final transcript
                        final_transcript = GoogleCloudFinalTranscript(transcript)
                        self.logger.info(f"🎯 Google Cloud Final Transcript: '{transcript}'")
                        if self.on_data_callback:
                            self.logger.info("Calling on_data_callback for final transcript")
                            self.on_data_callback(final_transcript)
                        else:
                            self.logger.warning("No on_data_callback set for final transcript")
                    else:
                        # Interim/partial transcript
                        partial_transcript = GoogleCloudPartialTranscript(transcript)
                        self.logger.info(f"📝 Google Cloud Partial Transcript: '{transcript}'")
                        if self.on_data_callback:
                            self.logger.debug("Calling on_data_callback for partial transcript")
                            self.on_data_callback(partial_transcript)
                        else:
                            self.logger.debug("No on_data_callback set for partial transcript")
                            
        except google_exceptions.GoogleAPIError as e:
            self.logger.error(f"Google Cloud Speech API error after {responses_processed} responses ({results_with_transcripts} with transcripts): {e}")
            if self.on_error_callback:
                self.on_error_callback(GoogleCloudError(str(e)))
        except Exception as e:
            self.logger.error(f"Error processing Google Cloud Speech responses after {responses_processed} responses ({results_with_transcripts} with transcripts): {e}", exc_info=True)
            if self.on_error_callback:
                self.on_error_callback(GoogleCloudError(str(e)))
        
        self.logger.info(f"Google Cloud Speech response processing finished: {responses_processed} total responses, {empty_responses} empty, {results_with_transcripts} with transcripts")

    # Interface methods that mirror AssemblyAI RealtimeTranscriber
    def on_open(self, callback: Callable):
        """Set callback for session opened event"""
        self.on_open_callback = callback
        return self

    def on_data(self, callback: Callable):
        """Set callback for transcript data"""
        self.on_data_callback = callback
        return self

    def on_error(self, callback: Callable):
        """Set callback for errors"""
        self.on_error_callback = callback
        return self

    def on_close(self, callback: Callable):
        """Set callback for session close"""
        self.on_close_callback = callback
        return self

    def on_extra_session_information(self, callback: Callable):
        """Set callback for session information"""
        self.on_extra_session_information_callback = callback
        return self

    def connect(self):
        """Connect to Google Cloud Speech streaming service"""
        try:
            if not self.client:
                self.logger.error("Google Cloud Speech client not initialized")
                if self.on_error_callback:
                    self.on_error_callback(GoogleCloudError("Client not initialized"))
                return
                
            self._create_streaming_config()
            
            # Enhanced request generator with better real audio tracking
            def request_generator():
                requests_yielded = 0
                real_audio_requests = 0
                silent_requests = 0
                last_log_time = time.time()
                self.logger.info("Google Cloud Speech: Request generator started - yielding immediately")
                
                while self.is_stream_audio_data:
                    request = None
                    is_real_audio = False
                    
                    # Check if real audio is available
                    if self.request_queue:
                        with self.stream_lock:
                            if self.request_queue:
                                request = self.request_queue.popleft()
                                is_real_audio = True
                                real_audio_requests += 1
                    
                    # If no real audio, send silence to keep connection alive
                    if request is None:
                        request = speech.StreamingRecognizeRequest(audio_content=b'\x00' * 320)
                        silent_requests += 1
                    
                    yield request
                    requests_yielded += 1
                    
                    # Log periodically with audio type breakdown
                    current_time = time.time()
                    if requests_yielded % 50 == 0 or (current_time - last_log_time) >= 5:
                        queue_size = len(self.request_queue)
                        self.logger.info(f"Google Cloud Speech: yielded {requests_yielded} requests (real: {real_audio_requests}, silent: {silent_requests}, queue: {queue_size})")
                        last_log_time = current_time
                    
                    # Log first few real audio chunks for debugging
                    if is_real_audio and real_audio_requests <= 10:
                        self.logger.info(f"🎵 Sent real audio chunk #{real_audio_requests}")
                    
                    # Control request rate - ~100 requests per second (10ms audio chunks)
                    time.sleep(0.01)
            
            # Start streaming recognition - pass config and requests separately
            self.stream_iterator = self.client.streaming_recognize(
                config=self.streaming_config, 
                requests=request_generator()
            )
            
            # Start response processing thread
            self.response_thread = threading.Thread(target=self._process_responses, daemon=True)
            self.response_thread.start()
            self.logger.info("Google Cloud Speech: Response processing thread started")
            
            # Start audio streaming thread immediately (different from AssemblyAI)
            # Google Cloud Speech expects audio data to flow immediately after connection
            self.listen_task_threading.start()
            self.logger.info("Google Cloud Speech: Audio streaming thread started")
            
            # Track audio start time
            self.audio_start_time = time.time()
            
            # Call on_open callback
            if self.on_open_callback:
                session_info = {"session_id": f"gcp_{int(time.time())}"}
                self.on_open_callback(session_info)
                
            self.logger.info("Google Cloud Speech streaming connection established")
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Google Cloud Speech: {e}")
            # Ensure cleanup on connection failure
            self.stream_iterator = None
            self.response_thread = None
            if self.on_error_callback:
                self.on_error_callback(GoogleCloudError(str(e)))

    def stream(self, audio_chunk: bytes):
        """Add audio chunk to the stream"""
        if self.is_stream_audio_data:
            self.add_audio_chunk(audio_chunk)

    def close(self):
        """Close the streaming connection"""
        try:
            self.is_stream_audio_data = False
            
            # Calculate total audio duration
            if self.audio_start_time:
                self.total_audio_duration = time.time() - self.audio_start_time
                
            # Send session information callback
            if self.on_extra_session_information_callback:
                session_info = GoogleCloudSessionInfo(self.total_audio_duration)
                self.on_extra_session_information_callback(session_info)
            
            # Close stream iterator
            if self.stream_iterator:
                try:
                    # For Google Cloud Speech, the iterator doesn't have a cancel method
                    # We just stop feeding it requests by setting is_stream_audio_data = False
                    pass
                except:
                    pass
                self.stream_iterator = None
            
            # Call on_close callback
            if self.on_close_callback:
                self.on_close_callback()
                
            self.logger.info("Google Cloud Speech streaming connection closed")
            
        except Exception as e:
            self.logger.error(f"Error closing Google Cloud Speech connection: {e}")

    # Methods that match the existing AssemblyAI implementation interface
    def add_audio_chunk(self, audio_chunk):
        """Add audio chunk to processing queue"""
        self.audio_queue.append(audio_chunk)

    def stream_audio_data(self):
        """Main audio streaming loop - matches AssemblyAI implementation structure"""
        audio_buffer = io.BytesIO()
        start_time = time.time()
        max_chunk_size = 1024
        audio_chunks_processed = 0
        requests_sent = 0

        self.logger.info("Google Cloud Speech stream_audio_data started")
        audio_queue_checks = 0

        while self.is_stream_audio_data:
            # 30-minute timeout (1800 seconds)
            if time.time() - start_time >= 1800:
                self.logger.info("30-minute timeout reached. Sending transcripts and exiting.")
                
                llm_answer = "Please try again later. Thank you for using Ongo Service"
                # Send timeout message using protocol audio
                if hasattr(self.user, 'stream_id') and self.user.stream_id:
                    import asyncio
                    asyncio.run_coroutine_threadsafe(
                        self.request_handler.text_to_protocol_audio_send(llm_answer),
                        self.user.loop
                    ).result()
                self.user.sync_send(llm_answer)
                self.user.end_ai_call()
                break

            audio_queue_checks += 1
            
            if self.audio_queue:
                audio_chunk = self.audio_queue.popleft()
                audio_chunks_processed += 1
                audio_buffer.write(audio_chunk)
                
                # Debug log every 10 chunks
                if audio_chunks_processed % 10 == 0:
                    self.logger.info(f"Google Cloud Speech processed {audio_chunks_processed} audio chunks, buffer size: {audio_buffer.tell()}")
                
                # Log first few chunks for debugging
                if audio_chunks_processed <= 5:
                    self.logger.info(f"🎵 Processing audio chunk #{audio_chunks_processed}, size: {len(audio_chunk)} bytes")

                while audio_buffer.tell() >= max_chunk_size:
                    audio_buffer.seek(0)
                    audio_bytes = audio_buffer.read(max_chunk_size)
                    try:
                        # Send audio data to Google Cloud Speech via request queue
                        audio_request = speech.StreamingRecognizeRequest(audio_content=audio_bytes)
                        with self.stream_lock:
                            self.request_queue.append(audio_request)
                            requests_sent += 1
                            
                        # Debug log every request
                        if requests_sent % 5 == 0:
                            self.logger.info(f"Google Cloud Speech: {requests_sent} audio requests queued, queue size: {len(self.request_queue)}")
                            
                    except Exception as e:
                        self.logger.error("Error streaming audio: %s", e)
                        # Stop the stream and trigger cleanup
                        self.is_stream_audio_data = False
                        self.close()
                        # Trigger user cleanup to remove from active users
                        if hasattr(self.user, 'end_ai_call'):
                            self.user.end_ai_call()
                        return

                    remaining_data = audio_buffer.read()
                    audio_buffer = io.BytesIO()
                    audio_buffer.write(remaining_data)

                audio_buffer.seek(0, io.SEEK_END)
            else:
                time.sleep(0.01)

    def check_pause_and_handle_transcript(self):
        """Handle transcript processing and conversation flow - matches AssemblyAI implementation"""
        # greeting = self.user.vehicle_validator.assistant.start_new_call()
        greeting = self.user.bot_manager.start_new_call()
        self.last_llm_answer = greeting
        # Send greeting using protocol audio
        if hasattr(self.user, 'stream_id') and self.user.stream_id:
            import asyncio
            asyncio.run_coroutine_threadsafe(
                self.request_handler.text_to_protocol_audio_send(greeting),
                self.user.loop
            ).result()
        self.entry_dumper.start_dump_task("llm", greeting)
        
        while self.is_stream_audio_data:
            if self.last_transcript_time and (time.time() - self.last_transcript_time >= 2):
                user_response = self.user_input
                self.user_input = ""
                self.request_handler.handle_final_transcript(user_input=user_response)
                self.last_transcript_time = None
                self.last_speak_time = time.time()

            if self.is_completed and self.last_speak_time and (time.time() - self.last_speak_time >= 30):
                if self.last_llm_answer:
                    # Send repeat message using protocol audio
                    if hasattr(self.user, 'stream_id') and self.user.stream_id:
                        import asyncio
                        asyncio.run_coroutine_threadsafe(
                            self.request_handler.text_to_protocol_audio_send(self.last_llm_answer),
                            self.user.loop
                        ).result()

                self.last_speak_time = time.time()

            time.sleep(0.5)

    def start(self):
        """Start the transcription service - matches AssemblyAI implementation"""
        pause_thread = threading.Thread(target=self.check_pause_and_handle_transcript, daemon=True)
        pause_thread.start()
        # Note: Unlike AssemblyAI, we don't call stream_audio_data() here because
        # it's already started in connect() to meet Google Cloud Speech timing requirements

    # Callback implementations to match AssemblyAI interface
    def on_open_impl(self, session_opened):
        """Handle session opened - matches AssemblyAI implementation"""
        session_id = session_opened.get("session_id", "unknown")
        self.logger.info("Google Cloud Speech Session ID: %s", session_id)

    def on_data_impl(self, transcript):
        """Handle transcript data - matches AssemblyAI implementation"""
        if not transcript.text:
            return
            
        current_time = time.time()
        if isinstance(transcript, GoogleCloudFinalTranscript):
            self.logger.info("%s: %s", GoogleCloudFinalTranscript, transcript.text)
            self.user_input += transcript.text
        else:
            self.request_handler.handle_partial_transcript(user_input=transcript.text)

        self.last_transcript_time = current_time
        self.last_speak_time = time.time()

    def on_error_impl(self, error):
        """Handle errors - matches AssemblyAI implementation"""
        self.logger.error("An error occurred: %s", error)
        
        # Handle specific error types that require cleanup
        error_message = str(error).lower()
        
        # Critical errors that require immediate cleanup
        critical_errors = [
            "quota", "limit", "session", "authentication", "permission",
            "deadline", "unavailable", "resource_exhausted", "failed_precondition"
        ]
        
        is_critical_error = any(err in error_message for err in critical_errors)
        
        if is_critical_error:
            self.logger.warning(f"Critical error detected: {error_message}")
            try:
                # Stop the transcription stream
                self.is_stream_audio_data = False
                # Close the connection gracefully
                if self.stream_iterator:
                    try:
                        # Google Cloud Speech iterator doesn't have a cancel method
                        # We just stop feeding it requests
                        pass
                    except:
                        pass
                # End the user's AI call to trigger proper cleanup
                if hasattr(self.user, 'end_ai_call'):
                    self.user.end_ai_call()
            except Exception as cleanup_error:
                self.logger.error("Error during cleanup: %s", cleanup_error)
        else:
            # Non-critical errors - log but continue
            self.logger.info("Non-critical error, continuing operation")

    def on_close_impl(self):
        """Handle session close - matches AssemblyAI implementation"""
        self.logger.info("Closing Google Cloud Speech Session")