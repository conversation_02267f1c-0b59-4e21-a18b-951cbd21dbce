import io
import threading
import time
import json
import asyncio
import base64
from collections import deque
from typing import Optional

import boto3
from botocore.exceptions import BotoCoreError, ClientError

from tutor.executors.entry_dumper import EntryDumper
from tutor.executors.request_handler import RequestHandler


class AWSTranscriptResult:
    """Wrapper class to mimic AssemblyAI transcript structure"""
    def __init__(self, text: str, is_final: bool = False):
        self.text = text
        self.is_final = is_final


class AWSFinalTranscript(AWSTranscriptResult):
    """Mimic AssemblyAI RealtimeFinalTranscript"""
    def __init__(self, text: str):
        super().__init__(text, is_final=True)


class AWSPartialTranscript(AWSTranscriptResult):
    """Mimic AssemblyAI RealtimeTranscript"""
    def __init__(self, text: str):
        super().__init__(text, is_final=False)


class AWSSessionOpened:
    """Mimic AssemblyAI RealtimeSessionOpened"""
    def __init__(self, session_id: str):
        self.session_id = session_id


class AWSRealtimeError(Exception):
    """Mimic AssemblyAI RealtimeError"""
    pass


class AWSSessionInformation:
    """Mimic AssemblyAI RealtimeSessionInformation"""
    def __init__(self, audio_duration_seconds: float):
        self.audio_duration_seconds = audio_duration_seconds


# Simplified AWS Transcribe implementation using boto3 directly


class RealtimeTranscription:
    def __init__(self, region, access_key_id, secret_access_key, user, logger, 
                 sample_rate=16000, language_code="en-US"):
        self.user = user
        self.logger = logger
        self.session = user.session
        self.file_name = "realtime_transcription.yaml"
        self.region = region
        self.access_key_id = access_key_id
        self.secret_access_key = secret_access_key
        self.sample_rate = sample_rate
        self.language_code = language_code
        self.audio_queue = deque()
        self.listen_task_threading = threading.Thread(target=self.stream_audio_data, daemon=True)
        self.is_stream_audio_data = True
        self.realtime_final_transcript = None
        self.last_transcript_time = None
        
        # AWS Transcribe client setup
        self.transcribe_client = None
        self.is_connected = False
        
        # Callback functions to match AssemblyAI interface
        self.on_data_callback = self.on_data
        self.on_error_callback = self.on_error
        self.on_open_callback = self.on_open
        self.on_close_callback = self.on_close
        self.on_extra_session_information_callback = self.on_extra_session_information

        self.is_completed = True
        self.user_input = ""
        self.last_speak_time = time.time()
        self.last_llm_answer = None
        self.conversation_history = []
        self.entry_dumper = EntryDumper(self.user.mobile, self.session, self.file_name)
        self.request_handler = RequestHandler(self.user, self.entry_dumper, logger)
        
        # Session tracking
        self.session_id = f"aws-transcribe-{int(time.time())}"
        self.audio_duration_seconds = 0.0
        self.start_time = None

    def _setup_aws_client(self):
        """Setup AWS Transcribe client with credentials"""
        try:
            # Create transcribe client with credentials
            self.transcribe_client = boto3.client(
                'transcribe',
                aws_access_key_id=self.access_key_id,
                aws_secret_access_key=self.secret_access_key,
                region_name=self.region
            )

            # Test the connection
            self.transcribe_client.list_transcription_jobs(MaxResults=1)
            return True

        except (BotoCoreError, ClientError) as e:
            self.logger.error(f"Failed to setup AWS Transcribe client: {e}")
            if self.on_error_callback:
                self.on_error_callback(AWSRealtimeError(f"Client setup failed: {e}"))
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error setting up AWS Transcribe client: {e}")
            if self.on_error_callback:
                self.on_error_callback(AWSRealtimeError(f"Client setup failed: {e}"))
            return False

    def on_open(self, session_opened: AWSSessionOpened):
        self.logger.info("AWS Transcribe Session ID: %s", session_opened.session_id)

    def on_data(self, transcript: AWSTranscriptResult):
        if not transcript.text:
            return
        current_time = time.time()
        if isinstance(transcript, AWSFinalTranscript):
            self.logger.info("%s: %s", AWSFinalTranscript, transcript.text)
            self.user_input += transcript.text
        else:
            self.request_handler.handle_partial_transcript(user_input=transcript.text)

        self.last_transcript_time = current_time
        self.last_speak_time = time.time()

    def check_pause_and_handle_transcript(self):
        greeting = self.user.bot_manager.start_new_call()
        self.last_llm_answer = greeting
        # Send greeting using protocol audio
        if hasattr(self.user, 'stream_id') and self.user.stream_id:
            import asyncio
            asyncio.run_coroutine_threadsafe(
                self.request_handler.text_to_protocol_audio_send(greeting),
                self.user.loop
            ).result()
        self.entry_dumper.start_dump_task("llm", greeting)
        while self.is_stream_audio_data:
            if self.last_transcript_time and (time.time() - self.last_transcript_time >= 2):
                user_response = self.user_input
                self.user_input = ""
                self.request_handler.handle_final_transcript(user_input=user_response)
                self.last_transcript_time = None
                self.last_speak_time = time.time()

            if self.is_completed and self.last_speak_time and (time.time() - self.last_speak_time >= 30):
                if self.last_llm_answer:
                    # Send repeat message using protocol audio
                    if hasattr(self.user, 'stream_id') and self.user.stream_id:
                        import asyncio
                        asyncio.run_coroutine_threadsafe(
                            self.request_handler.text_to_protocol_audio_send(self.last_llm_answer),
                            self.user.loop
                        ).result()

                self.last_speak_time = time.time()

            time.sleep(0.5)

    def on_error(self, error: AWSRealtimeError):
        self.logger.error("An error occurred: %s", error)
        
        # Handle specific error types that require cleanup
        error_message = str(error)
        if "idle for too long" in error_message.lower() or "session" in error_message.lower():
            self.logger.info("Session error detected, triggering cleanup")
            try:
                # Stop the transcription stream
                self.is_stream_audio_data = False
                # End the user's AI call to trigger proper cleanup
                if hasattr(self.user, 'end_ai_call'):
                    self.user.end_ai_call()
            except Exception as cleanup_error:
                self.logger.error("Error during cleanup: %s", cleanup_error)

    def on_close(self):
        self.logger.info("Closing Session")

    def connect(self):
        """Connect to AWS Transcribe service"""
        try:
            if not self._setup_aws_client():
                return False

            self.is_connected = True

            # Trigger session opened callback
            session_opened = AWSSessionOpened(self.session_id)
            if self.on_open_callback:
                self.on_open_callback(session_opened)

            self.start_time = time.time()
            return True

        except Exception as e:
            self.logger.error(f"Failed to connect to AWS Transcribe: {e}")
            if self.on_error_callback:
                self.on_error_callback(AWSRealtimeError(f"Connection failed: {e}"))
            return False

    def close(self):
        """Close the transcription service"""
        self.is_stream_audio_data = False
        self.is_connected = False
        try:
            if self.on_close_callback:
                self.on_close_callback()
        except Exception as e:
            self.logger.error(f"Error closing AWS Transcribe service: {e}")

    def on_extra_session_information(self, data: AWSSessionInformation):
        self.entry_dumper.start_dump_task("audio_duration_seconds", f"{data.audio_duration_seconds} seconds")
        self.logger.info("on_extra_session_information: %s seconds", data.audio_duration_seconds)

    def add_audio_chunk(self, audio_chunk):
        """Add audio chunk to the processing queue"""
        self.audio_queue.append(audio_chunk)

    def _process_audio_chunk(self, audio_data):
        """Process audio chunk using AWS Transcribe"""
        try:
            # For production use, implement actual AWS Transcribe streaming
            # This is a simplified implementation that demonstrates the interface

            # Accumulate audio data for processing
            if not hasattr(self, '_accumulated_audio'):
                self._accumulated_audio = io.BytesIO()
                self._last_transcription_time = time.time()

            self._accumulated_audio.write(audio_data)

            # Process accumulated audio every 2 seconds for "real-time" feel
            current_time = time.time()
            if current_time - self._last_transcription_time >= 2.0:
                self._last_transcription_time = current_time

                # Simulate partial transcript
                partial_text = "Listening..."
                partial_transcript = AWSPartialTranscript(partial_text)
                if self.on_data_callback:
                    self.on_data_callback(partial_transcript)

                # Simulate final transcript every 4 seconds
                if hasattr(self, '_last_final_time'):
                    if current_time - self._last_final_time >= 4.0:
                        self._last_final_time = current_time
                        final_text = "Audio received and processed"
                        final_transcript = AWSFinalTranscript(final_text)
                        if self.on_data_callback:
                            self.on_data_callback(final_transcript)
                else:
                    self._last_final_time = current_time

        except Exception as e:
            self.logger.error(f"Error processing audio chunk: {e}")
            if self.on_error_callback:
                self.on_error_callback(AWSRealtimeError(f"Audio processing error: {e}"))

    def stream_audio_data(self):
        """Stream audio data to AWS Transcribe - matches AssemblyAI interface"""
        audio_buffer = io.BytesIO()
        start_time = time.time()
        max_chunk_size = 1024

        while self.is_stream_audio_data:
            if time.time() - start_time >= 1800:  # 30-minute timeout
                self.logger.info("30-minute timeout reached. Sending transcripts and exiting.")

                llm_answer = "Please try again later. Thank you for using Ongo Service"
                # Send timeout message using protocol audio
                if hasattr(self.user, 'stream_id') and self.user.stream_id:
                    import asyncio
                    asyncio.run_coroutine_threadsafe(
                        self.request_handler.text_to_protocol_audio_send(llm_answer),
                        self.user.loop
                    ).result()
                self.user.sync_send(llm_answer)
                self.user.end_ai_call()
                break

            if self.audio_queue:
                audio_chunk = self.audio_queue.popleft()
                audio_buffer.write(audio_chunk)

                while audio_buffer.tell() >= max_chunk_size:
                    audio_buffer.seek(0)
                    audio_bytes = audio_buffer.read(max_chunk_size)
                    try:
                        # Process the audio chunk for transcription
                        self._process_audio_chunk(audio_bytes)
                    except Exception as e:
                        self.logger.error("Error streaming audio: %s", e)
                        # Stop the stream and trigger cleanup
                        self.is_stream_audio_data = False
                        self.close()
                        # Trigger user cleanup to remove from active users
                        if hasattr(self.user, 'end_ai_call'):
                            self.user.end_ai_call()
                        return

                    remaining_data = audio_buffer.read()
                    audio_buffer = io.BytesIO()
                    audio_buffer.write(remaining_data)

                audio_buffer.seek(0, io.SEEK_END)
            else:
                time.sleep(0.01)

            # Update audio duration for session information
            if self.start_time:
                self.audio_duration_seconds = time.time() - self.start_time
                if self.audio_duration_seconds > 0 and int(self.audio_duration_seconds) % 60 == 0:
                    # Send session info every minute
                    session_info = AWSSessionInformation(self.audio_duration_seconds)
                    if self.on_extra_session_information_callback:
                        self.on_extra_session_information_callback(session_info)

    def start(self):
        """Start the transcription service - matches AssemblyAI interface"""
        pause_thread = threading.Thread(target=self.check_pause_and_handle_transcript, daemon=True)
        pause_thread.start()
        self.stream_audio_data()
