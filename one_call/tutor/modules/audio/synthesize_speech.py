import io
import os
import sys
from tempfile import gettempdir
import requests
from gtts import gTTS
from pydub import AudioSegment


async def text_to_audio(text):
    # Convert text to audio
    tts = gTTS(text=text, lang='en', slow=False)
    audio_stream = io.BytesIO()
    tts.write_to_fp(audio_stream)
    audio_stream.seek(0)
    # Convert MP3 to PCM (16-bit, mono, 44100Hz)
    audio_segment = AudioSegment.from_file(audio_stream, format="mp3")
    pcm_audio = audio_segment.set_frame_rate(44100).set_channels(1).set_sample_width(2)

    # Save PCM audio to a byte stream
    pcm_audio_stream = io.BytesIO()
    pcm_audio.export(pcm_audio_stream, format="raw")
    pcm_audio_stream.seek(0)
    return pcm_audio_stream


def send_text_to_audio_file_api(text):
    endpoint = "http://localhost:4483/get-text-audio-file"  # Update with your actual API endpoint
    data = {"message": text}
    try:
        response = requests.post(endpoint, json=data)
        response.raise_for_status()  # Raise exception for HTTP errors
        return response.content  # Return the audio content received from the API
    except requests.exceptions.RequestException as e:
        print("Error sending text to /get-text-audio-file API:", e)
        sys.exit(-1)


def synthesize_speech(text):
    # Send text to /get-text-audio-file API
    audio_content = send_text_to_audio_file_api(text)

    # Save the audio content to a file
    output = os.path.join(gettempdir(), "speech.mp3")
    with open(output, "wb") as file:
        file.write(audio_content)

    return output
