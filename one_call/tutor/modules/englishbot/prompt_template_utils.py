"""
This file implements prompt template for llama based models. 
Modify the prompt template based on the model you select. 
This seems to have significant impact on the output of the LLM.
"""

from langchain.prompts import PromptTemplate

# this is specific to Llama-2.
"""
PROMPT = PromptTemplate(
    input_variables=["history", "input"],
    template=You are <PERSON><PERSON>, an AI English teacher, here to help the student practice spoken English. 
    Correct their mistakes and provide explanations where necessary.
    Ensure your responses are friendly and encouraging. 
    Keep the conversation flowing naturally by asking relevant follow-up questions. 
    Only ask one question at a time.
    Do not write all the conversation at once.

    Current Conversation:
    {history}
    Student: {input}
    Teacher:
)
"""

"""
system_prompt = You are a helpful assistant, you will use the provided context to answer user questions.
Read the given context before answering questions and think step by step. If you can not answer a user question based on 
the provided context, inform the user. Do not use any other information for answering user. Provide a detailed answer to the question.
"""
system_prompt_old = """You are <PERSON>, an AI English teacher, here to help the student practice spoken English.                   
                   All your response will be audio fed,so keep your replies within 1 to 2 sentences without any parenthesis.
                   Keep the conversation flowing naturally by asking relevant follow-up questions. 
                   Only ask one question at a time.
                   Do not write all the conversation at once.
                """

system_prompt = """You are <PERSON>, an AI English teacher, Your answers will be spoken, so keep them short, like 1 or 
2 sentences. Ask relevant follow-up questions to keep the conversation flowing."""

def get_prompt_template(system_prompt=system_prompt, promptTemplate_type=None, history=False):
    if promptTemplate_type == "llama":
        B_INST, E_INST = "[INST]", "[/INST]"
        B_SYS, E_SYS = "<<SYS>>\n", "\n<</SYS>>\n\n"
        SYSTEM_PROMPT = B_SYS + system_prompt + E_SYS
        if history:
            instruction = """
            Context: {history}
            Student: {input}
            Teacher:"""

            prompt_template = B_INST + SYSTEM_PROMPT + instruction + E_INST
            prompt = PromptTemplate(input_variables=["history", "input"], template=prompt_template)
        else:
            instruction = """
            Context: {context}
            User: {question}"""

            prompt_template = B_INST + SYSTEM_PROMPT + instruction + E_INST
            prompt = PromptTemplate(input_variables=["context", "question"], template=prompt_template)
    elif promptTemplate_type == "mistral":
        B_INST, E_INST = "<s>[INST] ", " [/INST]"
        if history:
            prompt_template = (
                    B_INST
                    + system_prompt
                    + """
    
            Context: {history} \n {context}
            User: {question}"""
                    + E_INST
            )
            prompt = PromptTemplate(input_variables=["history", "context", "question"], template=prompt_template)
        else:
            prompt_template = (
                    B_INST
                    + system_prompt
                    + """
            
            Context: {context}
            User: {question}"""
                    + E_INST
            )
            prompt = PromptTemplate(input_variables=["context", "question"], template=prompt_template)
    else:
        # change this based on the model you have selected.
        if history:
            prompt_template = (
                    system_prompt
                    + """
    
            Context: {history} \n {context}
            User: {question}
            Answer:"""
            )
            prompt = PromptTemplate(input_variables=["history", "context", "question"], template=prompt_template)
        else:
            prompt_template = (
                    system_prompt
                    + """
            
            Context: {context}
            User: {question}
            Answer:"""
            )
            prompt = PromptTemplate(input_variables=["context", "question"], template=prompt_template)

    return prompt
