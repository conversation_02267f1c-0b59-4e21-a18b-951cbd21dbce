import random

start_call_messages = [
    "Hello! Welcome to your AI English classroom. Let's kick off by discussing your favorite English language learning method.",
    "Greetings! I'm your AI English tutor. Tell me about a memorable experience you've had while learning English.",
    "Hi there! Ready to improve your English skills? Start by sharing a challenging aspect of the language for you.",
    "Good day! I'm your virtual English coach. Share a language learning tip that has worked wonders for you.",
    "Hey! I'm your language mentor. Let's begin by discussing your goals and aspirations in mastering English.",
    "Salutations! I'm your AI language partner. Tell me about a time when you successfully used English in a real-life situation.",
    "Hello! I'm your online English instructor. Share a funny language learning anecdote that still makes you smile.",
    "Hey there! Ready to dive into the world of English? Start by telling me about your favorite English-speaking country and why.",
    "Greetings! I'm your digital English guide. Let's begin by discussing your favorite English words and phrases.",
    "Hi! I'm your AI language teacher. Share a language learning resource or app that you find particularly helpful."
]

def get_random_start_call_message():
    return random.choice(start_call_messages)

selected_message = get_random_start_call_message()
print(selected_message)
