import asyncio
import json
from concurrent.futures import Thread<PERSON>oolExecutor
from threading import <PERSON>
from typing import List

import websockets
from tutor.modules.audio.audio_processor import AudioProcessor  # Assuming you might use audio features for tutoring
from tutor.modules.logger import setup_logger
from tutor.modules.englishbot.bot_manager import Bo<PERSON><PERSON>anager  # This could handle tutor-bot interactions

DISCONNECT_DELAY = 600  # delay in seconds

class User:
    def __init__(self, session: str, name: str, websocket, data: dict, event_loop, active_users: List['User']):
        self.session = session
        self.name = name
        self.conn = websocket
        self.mobile = data.get('mobile')
        self.user_id = data.get('userId')
        self.sentences = data.get('sentences', [])  # Assuming users send sentences to be corrected
        self.loop = event_loop
        self.is_end_session = False
        self.ai_start_tutoring = False
        self.executor = ThreadPoolExecutor()
        self.active_users = active_users

        self.logger = setup_logger(session, self.mobile)
        self.realtime_feedback = None  # Placeholder for real-time feedback system
        self.bot_manager = BotManager(self, self.logger, event_loop)  # Manages AI tutor bot interactions
        self.audio_processor = AudioProcessor(self, self.logger)
        self._disconnect_task = None

    def __repr__(self):
        return (f"User(session={self.session}, name={self.name}, mobile={self.mobile}, "
                f"user_id={self.user_id}, sentences={self.sentences})")

    def to_dict(self):
        return {
            'session': self.session,
            'name': self.name,
            'mobile': self.mobile,
            'user_id': self.user_id,
            'sentences': self.sentences
        }
    
    async def send(self, message: str):
        """Send a message to the user."""
        try:
            if self.conn.open:
                await self.conn.send(message)
            else:
                await self.schedule_disconnect()
        except websockets.ConnectionClosed:
            self.logger.error("Failed to send message: Connection closed", exc_info=True)   
            await self.schedule_disconnect()        
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}", exc_info=True)
            await self.schedule_disconnect()

    def sync_send(self, message: str):
        """Send a message to the user synchronously."""
        asyncio.run_coroutine_threadsafe(self.send(message), self.loop)

    def start_ai_tutoring(self):
        """Start AI tutoring session for the user."""
        self.logger.info("Starting AI tutoring session")
        # Assuming sentence validation or interaction with bot for English corrections
        # self.sentence_validator.start_tutoring()

    def end_ai_tutoring(self):
        """End AI tutoring session for the user."""
        if self.is_end_session:
            return
        self.logger.info("Ending AI tutoring session")
        self.is_end_session = True
        self._run_async(self.close())
        # self.sentence_validator.save_session_data()  # Save the session data
        self._run_async(self.cleanup())

    def send_end_tutoring(self):
        """Notify the user that the AI tutoring session has ended and close the connection."""
        if self.is_end_session:
            return
        self.logger.info("Sending end tutoring notification to user")
        self.is_end_session = True
        self.sync_send(json.dumps({"type": "ai_end_tutoring", "data": "AI tutoring session completed"}))
        self._run_async(self.close())
        # self.sentence_validator.save_session_data()
        self._run_async(self.cleanup())


    async def process_text(self, input: str):
        # Get the AI response from the bot manager
        ai_response = await self.bot_manager.handle_user_input(input)
        # Properly format the JSON message with the actual AI response
        self.sync_send(json.dumps({"type": "ai_response", "data": f"{ai_response}"}))

    async def process_voice(self, input: str):
        input = self.audio_processor.get_speech_to_text()        
        # Properly format the JSON message with the actual AI response
        self.sync_send(json.dumps({"type": "speech_text", "data": f"{input}"}))

    async def close(self):
        try:
            await self.conn.close()
        except Exception as e:
            self.logger.error(f"Failed to close: {e}", exc_info=True)

    def _run_async(self, coroutine):
        """Run an asynchronous coroutine."""
        self.logger.info(f"Running asynchronous coroutine: {coroutine}")
        asyncio.run_coroutine_threadsafe(coroutine, self.loop)

    async def schedule_disconnect(self):
        """Schedule a task to disconnect the user after a delay."""
        if self._disconnect_task is None or self._disconnect_task.done():
            self.logger.info(f"Scheduling disconnect task for user {self.session}")
            self._disconnect_task = self.loop.create_task(self._disconnect_after_delay())

    async def _disconnect_after_delay(self):
        """Disconnect the user after a delay if the connection is still closed."""
        await asyncio.sleep(DISCONNECT_DELAY)
        if not self.conn.open:
            self.logger.info(f"Disconnecting user {self.session} after delay")
            # self.sentence_validator.save_session_data()
            await self.cleanup()

    async def cleanup(self):
        """Cleanup resources after the user session ends."""
        if self in self.active_users:
            self.logger.info("Cleaning up user resources")
            self.executor.shutdown(wait=False)
            handlers = self.logger.handlers[:]
            for handler in handlers:
                handler.close()
                self.logger.removeHandler(handler)
            try:
                self.active_users.remove(self)
                self.logger.info(f"User {self.session} removed from active users")
            except ValueError:
                self.logger.error(f"User {self.session} could not be removed from active users")

