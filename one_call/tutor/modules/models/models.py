import os

from tutor.modules.models.classes import (env, fileio, settings)


def _main_process_validations():
    # Create all necessary DB tables during startup
    if not os.path.isdir(fileio.root):
        os.mkdir(fileio.root)
    """
    db = database.Database(database=fileio.base_db)
    for table, column in TABLES.items():
        db.create_table(table_name=table, columns=column)
    # Create required file for alarms
    if not os.path.isfile(fileio.alarms):
        pathlib.Path(fileio.alarms).touch()
    # Create required file for reminders
    if not os.path.isfile(fileio.reminders):
        pathlib.Path(fileio.reminders).touch()
    """
    # Create required directory for uploads
    if not os.path.isdir(fileio.uploads):
        os.mkdir(fileio.uploads)
    # Create required directory for sessions
    if not os.path.isdir(fileio.sessions):
        os.mkdir(fileio.sessions)


_main_process_validations()
