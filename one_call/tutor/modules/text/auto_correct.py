import re
import difflib

from modules.text import word_to_number


def remove_extra_spaces(input_string):
    # Split the string into words and then join them with a single space
    cleaned_string = ' '.join(input_string.split())
    return cleaned_string


def remove_duplicate_words(sentence, correct_word):
    words = sentence.split()  # Split sentence into words
    unique_words = []  # List to store unique words
    seen_words = set()  # Set to track seen words

    for word in words:
        if word == correct_word:
            if word not in seen_words:
                unique_words.append(word)
                seen_words.add(word)
        elif word not in seen_words:
            unique_words.append(word)
            seen_words.add(word)

    return ' '.join(unique_words)  # Join words back into a sentence


def _clean_input(sentence):
    # Remove non-alphanumeric characters except for spaces to preserve word boundaries
    sentence = re.sub(r'[^A-Za-z0-9 ]+', '', sentence)
    return sentence.lower()


def replace_with_best_match(original_input, correct_word, match_threshold=0.4):
    correct_word_clean = correct_word.lower()
    user_input_clean = _clean_input(original_input)
    user_input_clean = word_to_number.words_to_digits(user_input_clean)
    # Variables to store the best match found
    best_match = None
    best_ratio = 0

    # Iterate through each word in the cleaned input to find the best matching segment
    words = user_input_clean.split()
    for word in words:
        # Consider possible combinations of words to find the best match
        for i in range(len(words)):
            for j in range(i, len(words)):
                segment = ' '.join(words[i:j + 1]).replace(' ', '')
                ratio = difflib.SequenceMatcher(None, segment, correct_word_clean).ratio()
                if ratio > best_ratio:
                    best_ratio = ratio
                    best_match = ' '.join(words[i:j + 1])

    # Replace the best match in the original input if the match ratio is high enough
    if best_match and best_ratio > match_threshold:
        # Find and replace the best match in the original input
        match_regex = re.compile(re.escape(best_match), re.IGNORECASE)
        original_input = match_regex.sub(correct_word, original_input, 1)  # Replace only the first occurrence

    return original_input


"""

# Example usage
correct_word = "mp041234th"

input_sentences = [
    "Can you proceed? Vehicle AB 12 with precise amount of 100 rupees.",
    "Can you proceed? 1234 with precise amount of 100 rupees.",
    "Can you Vehicle AB 34 with a precise of 100 rupees.",
    "Vehicle AB 12 with precise amount of 100.",
    "Vehicle A 12 with precise amount of 100.",
    "Car number AB1234 is parked outside.",
    "The registration is AB1234.",
    "See the plate: AB  1234.",
    "He mentioned A123.",
    "You noted 12 AB right?",
    "The number is 1A2B3.",
    "Can you proceed? Vehicle AB 12 with precise amount of 100 rupees.",
    "Can you proceed? 1234 with precise amount of 100 rupees.",
    "Can you Vehicle AB 34 with a precise of 100 rupees.",
    "Vehicle AB 12 with precise amount of 100.",
    "Vehicle A 12 with precise amount of 100.",
    "Some more examples MH 09 F 787878 and MP 04 1234 TH should be matched.",
    "Random inputs like KA 51 MC 4084 and VSGGDHDH 897 need to be replaced.",
    "Mixed patterns such as 23 BH 8890 A and AB 1234 are also included."
]

for input_sentence in input_sentences:
    corrected_sentence = replace_with_best_match(input_sentence, correct_word)
    print(corrected_sentence)
"""
