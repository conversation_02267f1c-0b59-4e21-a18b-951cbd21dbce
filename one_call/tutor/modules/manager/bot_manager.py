from typing import Tuple, Optional, Dict

from api.ongo.ongo_faqs_service import OngoFaqsService
from executors import static_responses
from modules.models import models
from modules.ongo.bot import OngoChatbot
from modules.preset_vehicle.vehicle_validator import VehicleValidator
from modules.text.vehicle_formatter import format_vehicles_in_sentence

def _validate_greeting(greeting: str) -> str:
    unwanted_phrases = ["output completed", "output: completed", "hello", "hi there"]
    for phrase in unwanted_phrases:
        greeting = greeting.replace(phrase, "")
    return greeting


def _extract_first_name(full_name: str) -> str:
    """Extracts the first name from the full name."""
    return full_name.split()[0] if full_name else "Customer"


def _answer_validate(answer) -> str:
    return answer


class BotManager:
    def __init__(self, user, logger):
        self.user = user
        self.logger = logger
        self.ongo_bot = OngoChatbot(mobile=self.user.mobile, session=self.user.session, logger=logger)
        self.vehicle_validator = VehicleValidator(user, logger)
        self.ongo_faqs_service = OngoFaqsService(logger)
        self.full_name = user.name
        self.first_name = _extract_first_name(self.full_name)
        self.last_bot_selected = None

    def start_new_call(self) -> str:
        return self._greeting_message()

    async def answer_call_back(self, human_input: str = None) -> str:
        """Handles user input and returns the assistant's response.

        Args:
            human_input: The input text from the user.

        Returns:
            The response text from the assistant.
        """
        if not human_input:
            return "Sorry, I didn't get that. Please try again."
        answer = await self._query(human_input.lower())
        answer = _answer_validate(answer)
        if not answer:
            answer = "Sorry, I didn't get that."
        return answer

    def _greeting_message(self) -> str:
        # greeting = (f"{static_responses.greeting()} {self.first_name}, {util.greeting_message()}!, Thank you for " f"calling ongo,")
        greeting = f"{static_responses.greeting()}, "
        greeting += static_responses.get_first_response()
        self.ongo_bot.start_new_call()
        self.vehicle_validator.start_new_call()
        return greeting

    async def _query(self, phrase: str) -> str:

        is_ongo = False
        is_faqs = False # New variable for FAQ handling
        context = ''

        # Check for ONGO FAQ first
        if models.settings.is_ongo_faq:
            is_ongo, context = await self._handle_ongo_query(phrase)

        ongo_faq_is_bypass = True
        if ongo_faq_is_bypass:
            if not is_ongo:
                context = '. '
            return self.vehicle_validator.answer_call_back(phrase, context if context is not None else "")
        else:
            if self.last_bot_selected == "vehicle_preset_amount":
                if phrase in ('yes', 'yeah', 'yep', 'yeh', 'indeed', 'ya', 'sure'):
                    is_ongo = False

            if is_ongo and self.last_bot_selected != "vehicle_preset_amount":
                self.last_bot_selected = "ongo_prepaid_card"
                return self.ongo_bot.answer_call_back(phrase, context if context is not None else "")
            else:
                self.last_bot_selected = "vehicle_preset_amount"
                if is_ongo:
                    context = None
                return self.vehicle_validator.answer_call_back(phrase, context if context is not None else "")

    async def _handle_ongo_query(self, phrase: str) -> Tuple[bool, str]:
        """
        Handles queries related to ONGO products.

        :param phrase: The query phrase related to ONGO products.
        :return: A tuple containing a boolean indicating if the source matches "ongo_faqs.csv"
                 and the answer from the ONGOData instance.
        """
        faq_response = await self.ongo_faqs_service.get_best_match(phrase)
        if faq_response:
            answer_parts = faq_response.answer.split("answer:")
            answer = answer_parts[-1].strip() if len(answer_parts) > 1 else faq_response.answer
            return faq_response.is_source_ongo_faqs("ongo_faqs.csv"), answer
        else:
            return False, "No answer available."



    def process_llm_answer(self, llm_answer: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        return self.vehicle_validator.process_llm_answer(llm_answer)

    def post_vehicle_number_and_preset_amount(self, ai_response: str, result: Optional[Dict]) -> str:
        return self.vehicle_validator.post_vehicle_number_and_preset_amount(ai_response, result)

    def handle_no_vehicle_case(self, llm_answer: str) -> Tuple[Optional[str], bool]:
        return self.vehicle_validator.handle_no_vehicle_case(llm_answer)

    def format_vehicle_number(self, sentence: str) -> str:
        return format_vehicles_in_sentence(sentence, self.vehicle_validator.vehicle_number_list)
