<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Online Users Monitoring</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css">
    <style>
        body, html {
            height: 100%;
            margin: 0;
            display: flex;
            flex-direction: column;
        }
        #usersTableContainer {
            flex: 1;
            overflow-y: auto;
            height: calc(100vh - 160px); /* Adjust according to the height of header and search bar */
        }
        .search-bar {
            margin: 10px 0;
        }
        .table-container {
            padding-bottom: 20px; /* Add padding to avoid cutting off content */
        }
    </style>
</head>
<body>
    <div class="container-fluid h-100 d-flex flex-column">
        <h1 class="text-center my-4">Online Users Monitoring</h1>
        <div class="search-bar text-center">
            <input type="text" id="searchInput" placeholder="Search by name or mobile number" class="form-control w-50 mx-auto">
        </div>
        <div id="usersTableContainer" class="table-responsive table-container">
            <table class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Mobile</th>
                        <th>User ID</th>
                        <th>Vehicles</th>
                    </tr>
                </thead>
                <tbody id="usersTableBody">
                    <!-- Users will be dynamically populated here -->
                </tbody>
            </table>
        </div>
        <div id="statusMessage" class="text-center my-2"></div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.6.0/socket.io.min.js"></script>
    <script>
        const socket = io();

        socket.on('update_users', function(users) {
            // console.log('Received update_users event:', users);  // Log the received data
            const usersTableBody = document.getElementById('usersTableBody');
            usersTableBody.innerHTML = '';

            users.forEach(user => {
                const vehicles = user.vehicles ? user.vehicles.map(vehicle => `${vehicle.make} (${vehicle.number})`).join(', ') : 'No vehicles';
                const userRow = document.createElement('tr');
                userRow.innerHTML = `
                    <td>${user.name}</td>
                    <td>${user.mobile}</td>
                    <td>${user.user_id}</td>
                    <td>${vehicles}</td>
                `;
                usersTableBody.appendChild(userRow);
            });
        });

        socket.on('server_status', function(status) {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.textContent = `Server Status: ${status.status}`;
        });

        document.getElementById('searchInput').addEventListener('input', function() {
            const filter = this.value.toLowerCase();
            const rows = document.querySelectorAll('#usersTableBody tr');

            rows.forEach(row => {
                const name = row.cells[0].textContent.toLowerCase();
                const mobile = row.cells[1].textContent.toLowerCase();
                if (name.includes(filter) || mobile.includes(filter)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
