# processor.py

import os
import shutil
import warnings
from datetime import datetime
from multiprocessing import Process
from threading import Thread
from typing import Dict, List, Union

import psutil

from tutor.api.server import tutor_api
from tutor.executors import offline, process_map, englishbot, transcription, qa_report
from tutor.modules.logger import logger
from tutor.modules.models import models
from tutor.modules.utils import util
from tutor.websocket_server import websocket_server
from tutor.executors import qa_report
from tutor.executors.englishbot import englishbot_api

# noinspection LongLine
def create_process_mapping(processes: Dict[str, Process], func_name: str = None) -> None:
    """Creates or updates the processes mapping file.

    Args:
        processes: Dictionary of process names and the process.
        func_name: Function name of each process.

    See Also:
        - This is a special function, that uses doc strings to create a python dict.

    Handles:
        - tutor_api: Fast api server 
        - websocket_server: WebSocket server for real-time communication
        - englishbot_api: English Bot
        - background_tasks: background tasks, sentiment anylize.
        - transcription_service: Audio transcription service for Hinglish call recordings
        - qa_report_service: QA report generation service for call transcripts
    """
    impact_lib = {}
    for doc in create_process_mapping.__doc__.split('Handles:')[1].splitlines():
        if doc.strip():
            element = doc.strip().split(':')
            func = element[0].lstrip('- ')
            desc = element[1].strip().split(', ')
            if processes.get(func):
                impact_lib[func] = desc
            else:
                logger.warning("'%s' not found in list of processes initiated", func)
    if not func_name and sorted(impact_lib.keys()) != sorted(processes.keys()):
        warnings.warn(message=f"{list(impact_lib.keys())} does not match {list(processes.keys())}")
    if func_name:  # Assumes a processes mapping file exists already, since flag passed during process specific restart
        dump = process_map.get()
        dump[func_name] = {processes[func_name].pid: impact_lib[func_name]}
    else:
        dump = {k: {v.pid: impact_lib[k]} for k, v in processes.items()}
        dump["aitutor"] = {models.settings.pid: ["Main Process"]}
    logger.debug("Processes data: %s", dump)
    process_map.add(dump)


def start_processes(func_name: str = None) -> Union[Process, Dict[str, Process]]:
    """Initiates multiple background processes to achieve parallelization.

    Args:
        func_name: Name of the function that has to be started.

    Returns:
        Union[Process, Dict[str, Process]]:
        Returns a process object if a function name is passed, otherwise a mapping of function name and process objects.

    See Also:
        - speech_synthesis_api: Initiates docker container for speech synthesis.
        - telegram_api: Initiates polling Telegram API to execute offline commands (if no webhook config is available)
        - jarvis_api: Initiates uvicorn server to process API requests, stock monitor and robinhood report generation.
        - background_tasks: Initiates internal background tasks, sentiment anylize.
        - plot_mic: Initiates plotting realtime microphone usage using matplotlib.
    """
    process_dict = {
        tutor_api.__name__: Process(target=tutor_api),  # no process map removal
        websocket_server.__name__: Process(target=websocket_server),  # no process map removal
        englishbot_api.__name__: Process(target=englishbot_api),
        offline.background_tasks.__name__: Process(target=offline.background_tasks),  # no process map removal
        transcription.transcription_service.__name__: Process(target=transcription.transcription_service),  # Audio transcription service
        qa_report.qa_report_service.__name__: Process(target=qa_report.qa_report_service),  # QA report generation from Transcripts
    }

    # Used when a single process is requested to be triggered
    processes: Dict[str, Process] = {func_name: process_dict[func_name]} if func_name else process_dict
    for func, process in processes.items():
        process.name = func
        process.start()
        logger.info("Started function: {func} with PID: {pid}".format(func=func, pid=process.pid))
    Thread(target=create_process_mapping, kwargs=dict(processes=processes, func_name=func_name)).start()
    return processes[func_name] if func_name else processes


