import threading
from typing import List, Dict

from tutor.executors import files


# Assuming these are defined somewhere else in your code
# from your_module import files

class EntryDumper:
    def __init__(self, mobile: str, session: str, file_name: str):
        self.session = session
        self.mobile = mobile
        self.file_name = file_name

    def start_dump_task(self, entry_type: str, response: str) -> None:
        """
        Start a new thread to dump the entry.

        Args:
            entry_type (str): Type of the entry (e.g., user, gpt, function).
            response (str): Response to be recorded.
        """
        threading.Thread(target=self.dump_entry, args=(entry_type, response)).start()

    def dump_entry(self, entry_type: str, response: str) -> None:
        """
        Dump responses to a YAML file for future reference.

        Args:
            entry_type (str): Type of the entry (e.g., user, gpt, function).
            response (str): Response to be recorded.
        """
        data = self.load_entries()
        data.append({'type': entry_type, 'response': response})
        self.save_entries(data)

    def load_entries(self) -> List[Dict[str, str]]:
        """
        Load the entries from a YAML file.

        Returns:
            List[Dict[str, str]]: The loaded entries.
        """
        return files.load_yaml_from_session(self.mobile, self.session, self.file_name) or []

    def save_entries(self, data: List[Dict[str, str]]) -> None:
        """
        Save the entries to a YAML file.

        Args:
            data (List[Dict[str, str]]): The entries to save.
        """
        files.save_yaml_to_session(self.mobile, self.session, self.file_name, data)


"""
# Example usage of the EntryDumper class
if __name__ == "__main__":
    dumper = EntryDumper("example_session", "example_file.yaml")
    dumper.start_dump_task("user", "This is a test response.")
"""
