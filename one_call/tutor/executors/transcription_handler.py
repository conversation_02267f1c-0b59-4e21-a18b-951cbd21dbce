import asyncio
import os
from typing import Dict

from tutor.modules.transcription.hinglish_transcriber import HinglishTranscriber
from tutor.modules.database import database
from tutor.modules.models import models

class TranscriptionHandler:
    """Handler for processing audio files and managing transcription workflow."""
    
    def __init__(self, logger):
        self.logger = logger
        self.transcriber = HinglishTranscriber(logger)
        self.db = database.Database(database=models.fileio.base_db)
        
    async def process_audio_file(self, file_path: str, call_metadata: Dict) -> Dict:
        """
        Process an audio file for transcription and storage.
        
        Args:
            file_path: Path to the audio file
            call_metadata: Metadata about the call (call_id, date, participants, etc.)
            
        Returns:
            Dict with processing results
        """
        try:
            # Validate file exists
            if not os.path.exists(file_path):
                self.logger.error(f"Audio file not found: {file_path}")
                return {"success": False, "error": "File not found"}
                
            # Transcribe audio
            transcript_data = await self.transcriber.transcribe_audio(file_path)
            
            if "error" in transcript_data:
                return {"success": False, "error": transcript_data["error"]}
                
            # Save to database
            call_id = call_metadata.get("call_id", str(int(time.time())))
            save_result = await self.transcriber.save_to_database(
                transcript_data, call_id, self.db
            )
            
            if save_result:
                return {
                    "success": True, 
                    "call_id": call_id,
                    "transcript": transcript_data
                }
            else:
                return {"success": False, "error": "Failed to save to database"}
                
        except Exception as e:
            self.logger.error(f"Error processing audio file: {str(e)}")
            return {"success": False, "error": str(e)}