from typing import Tuple, Optional, Dict, Union
import inspect

import requests
from tutor.executors import static_responses
from tutor.modules.assistants.bank_assistant import BankAssistant
from tutor.modules.assistants.faqs_assistant import FaqsAssistant
from tutor.modules.assistants.fastag_assistant import FastagAssistant
from tutor.modules.assistants.hospital_appointment_assistant import HospitalAppointmentAssistant
from tutor.modules.assistants.restaurant_order_assistant import RestaurantOrderAssistant
from tutor.modules.assistants.wealth_assistant import WealthAssistant
from tutor.modules.models import models
from tutor.modules import logger
from tutor.modules.firebase import chatting


def _validate_greeting(greeting: str) -> str:
    unwanted_phrases = ["output completed", "output: completed", "hello", "hi there"]
    for phrase in unwanted_phrases:
        greeting = greeting.replace(phrase, "")
    return greeting


def _extract_first_name(full_name: str) -> str:
    """Extracts the first name from the full name."""
    return full_name.split()[0] if full_name else "Student"


def _answer_validate(answer) -> str:
    return answer

def send_message(chat_id: int, response: str, parse_mode: Union[str, None] = 'markdown',
                 retry: bool = False) -> requests.Response:
    """Generates a payload to reply to a message received.

    Args:
        chat_id: Chat ID.
        response: Message to be sent to the user.
        parse_mode: Parse mode. Defaults to ``markdown``
        retry: Retry reply in case reply failed because of parsing.

    Returns:
        Response:
        Response class.
    """
    result = chatting.send_message_to_user(response, chat_id)
    if result.status_code == 400 and parse_mode and not retry:  # Retry with response as plain text
        logger.warning("Retrying response as plain text with no parsing")
        send_message(chat_id=chat_id, response=response, parse_mode=None, retry=True)
    return result

class BotManager:
    def __init__(self, user, logger, event_loop, target: str):
        self.user = user
        self.logger = logger
        self.event_loop = event_loop
        self.full_name = user.name
        self.first_name = _extract_first_name(self.full_name)        
        self.last_bot_selected = None
        # self.tutor_bot = RestaurantOrderAssistant(mobile=self.user.mobile, session=self.user.session, logger=logger,full_name=user.name,loop=event_loop,restaurant_id=target)
        self.bot = self._select_bot(target)
        # Detect if bot.query method is async or sync
        self.is_async_bot = inspect.iscoroutinefunction(self.bot.query)


    def _select_bot(self, target: str):
        """
        Selects the appropriate tutor bot (assistant) based on the target.
        The target could be a restaurant ID, hospital ID, or other service identifier.
        """
        # Mapping target prefixes or exact values to assistant classes
        target_to_bot_map = {
            "assistant": BankAssistant,
            "+************": RestaurantOrderAssistant,
            "+************": RestaurantOrderAssistant,
            "+************": RestaurantOrderAssistant,
            "+************": HospitalAppointmentAssistant,
            "+************": HospitalAppointmentAssistant,
            "+************": HospitalAppointmentAssistant,
            "+************": BankAssistant,
            "+************": WealthAssistant,  
            "+************": FastagAssistant,
            "+************": FaqsAssistant,           
        }

        # Default to None or raise an error if the target is not recognized
        bot_class = target_to_bot_map.get(target)

        if not bot_class:
            self.logger.error(f"Invalid target: {target}")
            raise ValueError(f"No bot available for target: {target}")

        # Initialize the selected bot with user data
        return bot_class(
            mobile=self.user.mobile,
            session=self.user.session,
            logger=self.logger,
            full_name=self.user.name,
            loop=self.event_loop,
            target=target
        )

    def start_new_call(self) -> str:
        return self._greeting_message()
    
    def start_new_session(self) -> str:
        return self._greeting_message()

    async def handle_user_input(self, user_input: str = None) -> str:
        """Handles user input and returns the assistant's response.

        Args:
            user_input: The input text from the user.

        Returns:
            The response text from the assistant.
        """
        if not user_input:
            return "Sorry, I didn't get that. Please try again."
        answer = await self._query(user_input.lower())
        answer = _answer_validate(answer)
        if not answer:
            answer = "Sorry, I didn't get that."

        # send_message(self.user.mobile, answer, None)
        return answer

    def _greeting_message(self) -> str:
        # greeting = f"{static_responses.greeting()}, Welcome to your English tutor session!"
        # greeting += static_responses.get_first_response()
        greeting = self.bot.start_new_session()
        # self.sentence_validator.start_new_session()
        return greeting

    async def _query(self, phrase: str) -> str:
        if self.is_async_bot:
            return await self.bot.query(phrase, "")
        else:
            return self.bot.query(phrase, "")
        
    def process_llm_answer(self, llm_answer: str) -> Tuple[bool, Optional[Dict], Optional[str]]:
        #return self.sentence_validator.process_llm_answer(llm_answer)
        return False, None, llm_answer

    def post_sentence_correction(self, ai_response: str, result: Optional[Dict]) -> str:
        # return self.sentence_validator.post_sentence_correction(ai_response, result)
        return ai_response

    def handle_no_sentence_case(self, llm_answer: str) -> Tuple[Optional[str], bool]:
        # return self.sentence_validator.handle_no_sentence_case(llm_answer)
        return llm_answer

    def format_sentence(self, sentence: str) -> str:
        # return self.sentence_validator.format_sentence(sentence)
        return sentence
