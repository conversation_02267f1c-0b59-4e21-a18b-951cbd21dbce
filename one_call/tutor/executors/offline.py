import os
import time
import traceback
from datetime import datetime
from multiprocessing import Process, Queue
from threading import Thread, Timer
from typing import AnyStr, List, Union

import requests
from pydantic import HttpUrl

from tutor.executors import (communicator, files, sentiment_anylize,
                              word_match)
from tutor.modules.conditions import keywords
from tutor.modules.database import database
from tutor.modules.exceptions import EgressErrors
from tutor.modules.logger import logger, multiprocessing_logger
from tutor.modules.models import classes, models
from tutor.modules.utils import shared, support, util

db = database.Database(database=models.fileio.base_db)


def background_tasks() -> None:
    """Initiate the runner function for background tasks."""
    try:
        background_task_runner()
    except Exception as error:
        logger.critical("ATTENTION: %s", error.__str__())
        # controls.restart_control(quiet=True)


def background_task_runner() -> None:
    """Trigger for background tasks, cron jobs, automation, alarms, reminders, events and meetings sync."""
    multiprocessing_logger(filename=os.path.join('logs', 'background_tasks_%d-%m-%Y.log'))

    start_cron = time.time()
    sleep_time = 5
    logger.info(f"Sleeping for {sleep_time} seconds before starting background tasks.")
    dry_run = True
    while True:
        now = datetime.now()
        # Trigger background tasks

        # Trigger periodic background tasks
        logger.debug("Running scheduled sentiment analysis.")

        try:
            # Trigger cron jobs once during start up (regardless of schedule) and follow schedule after that
            if start_cron + 10 <= time.time():  # Condition passes every minute
                start_cron = time.time()
                Process(target=communicator.read_gmail, daemon=True).start()

        except Exception as e:
            logger.error(f"Error scommunicator read_gmail: {e}")

        try:
            Process(target=sentiment_anylize.anylize, daemon=True).start()
        except Exception as e:
            logger.error(f"Error starting sentiment analysis: {e}")

        try:
            Process(target=sentiment_anylize.anylize_post, daemon=True).start()
        except Exception as e:
            logger.error(f"Error starting sentiment analysis post: {e}")    

        dry_run = False
        time.sleep(sleep_time)  # Reduces CPU utilization as constant fileIO operations spike CPU % on producation change to 5

