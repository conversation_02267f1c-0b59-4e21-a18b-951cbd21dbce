import json
import requests
from tutor.modules.logger import logger
from tutor.modules.models import models
from tutor.modules.utils import shared
from tutor.modules.database import database

class SentimentDataPoster:
    def __init__(self):
        # Initialize the database connection
        self.db = database.Database(database=models.fileio.base_db)

        # Authentication and Sentiment Analysis URLs
        self.auth_url = "https://192.168.1.5:5012/api/auth/login"
        self.create_url = "https://192.168.1.5:5012/api/SentimentAnalysisResult/create"

        # Production URLs (commented for easy switching)
        self.auth_url = "http://13.202.85.17:44312/api/auth/login"
        self.create_url = "http://13.202.85.17:44312/api/SentimentAnalysisResult/create"

        # Authentication payload and headers
        self.auth_payload = {
            "email": "<EMAIL>",
            "Password": "Admin@123"
        }
        self.token = shared.token
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.token}' if self.token else '',
        }

    def authenticate(self) -> bool:
        """Fetches a new authentication token and updates headers."""
        try:
            response = requests.post(
                self.auth_url,
                headers={'Content-Type': 'application/json'},
                data=json.dumps(self.auth_payload),
                verify=False
            )
            if response.status_code == 200:
                self.token = response.json().get("Token")
                if self.token:
                    self.headers['Authorization'] = f"Bearer {self.token}"
                    shared.token = self.token
                    logger.info("Successfully authenticated and retrieved token.")
                    return True
                else:
                    logger.error("Failed to retrieve access token.")
                    return False
            else:
                logger.error(f"Authentication failed with status code {response.status_code}: {response.text}")
                return False
        except Exception as e:
            logger.error(f"Error during authentication: {e}")
            return False

    def post_sentiment_data(self, record: dict) -> bool:
        """Posts sentiment data to the create API and handles token expiration."""
        payload = {
            "ComplaintId": record["case_id"],
            "AccountId": record["account_id"],
            "CustomerName": record["customer_name"],
            "Subject": record["subject"],
            "Description": record["description"],
            "Status": record["status"],
            "CreatedOnUtc": record["date"],
            "IsSentimentAnalyzed": record["is_sentiment_analyzed"],
            "SentimentCategory": record.get("sentiment_category", "Neutral"),
            "SentimentSubcategory": record.get("sentiment_subcategory", "Neutral"),
            "SentimentScore": record.get("sentiment_score", 0),
            "KeyEmotions": record.get("key_emotions", "Neutral"),
            "TextSentiment": record.get("text_sentiment", "")
        }

        try:
            response = requests.post(
                self.create_url,
                headers=self.headers,
                data=json.dumps(payload),
                verify=False
            )
            if response.status_code == 200:
                self.db.update_record_with_is_posted(record["case_id"])
                logger.info(f"Successfully posted record {record['case_id']} with sentiment data.")
                print(f"Posted record {record['case_id']} with sentiment data.")
                return True
            elif response.status_code == 401:
                logger.warning("Token expired. Re-authenticating...")
                if self.authenticate():  # Retry authentication
                    # Retry posting with new token
                    response = requests.post(
                        self.create_url,
                        headers=self.headers,
                        data=json.dumps(payload),
                        verify=False
                    )
                    if response.status_code == 200:
                        logger.info(f"Successfully posted record {record['case_id']} with sentiment data after re-authentication.")
                        self.db.update_record_with_is_posted(record["case_id"])
                        print(f"Posted record {record['case_id']} with sentiment data after re-authentication.")
                        return True
                    else:
                        logger.error(f"Failed to post record {record['case_id']} after re-authentication: {response.text}")
                else:
                    logger.error("Re-authentication failed. Cannot post sentiment data.")
            else:
                logger.error(f"Failed to post record {record['case_id']}: {response.text}")
        except Exception as e:
            logger.error(f"Error posting sentiment data for record {record['case_id']}: {e}")
        return False

    def anylize_post(self) -> None:
        """Processes unanalyzed records and posts them to the Sentiment Analysis Result API."""
        try:
            unanalyzed_records = self.db.get_unposted_records()

            for record in unanalyzed_records:
                self.post_sentiment_data(record)
        except Exception as e:
            logger.error(f"Error in anylize_post: {e}")
            print(f"Error in anylize_post: {e}")    
