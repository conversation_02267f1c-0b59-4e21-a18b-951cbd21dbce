import json
import os
import string
from datetime import datetime

import requests

from tutor.executors.sentiment_data_poster import Sentiment<PERSON><PERSON>Poster
from tutor.modules.assistants.sentiment_analyze_assistant import SentimentAnalyzeAssistant
from tutor.modules.database import database
from tutor.modules.database.msql_database import Database
from tutor.modules.logger import logger, multiprocessing_logger
from tutor.modules.models import models
from tutor.modules.utils import shared


def anylize() -> None:
    multiprocessing_logger(filename=os.path.join('logs', 'background_tasks_%d-%m-%Y.log'))
    try:
        # db = Database(database="sampledb", server="***********,1433", user="mayur", password="123456")
        db = database.Database(database=models.fileio.base_db)
        unanalyzed_records = db.get_unanalyzed_records()

        for record in unanalyzed_records:
            sentiment_assistant = SentimentAnalyzeAssistant(logger=logger, record=record, db=db)
            text_sentiment, sentiment_category, sentiment_subcategory, score, emotions = sentiment_assistant.analyze_sentiment()
            
            # Update record with full sentiment details
            db.update_record_with_sentiment(
                case_id=record["case_id"],
                sentiment_category=sentiment_category,
                sentiment_subcategory=sentiment_subcategory,
                score=score,
                emotions=emotions,
                text_sentiment=text_sentiment
            )
            
            print(f"Processed record {record['case_id']} with sentiment: {sentiment_category}, subcategory: {sentiment_subcategory}, score: {score}, emotions: {emotions}")

    except TypeError as e:
        logger.error(f"Failed anylize due to TypeError: {e}")
    except Exception as e:
        logger.error(f"An error occurred during anylize: {e}")

def anylize_post() -> None:
    multiprocessing_logger(filename=os.path.join('logs', 'background_tasks_%d-%m-%Y.log'))
    try:
        # Initialize and run
        sentiment_poster = SentimentDataPoster()
        sentiment_poster.anylize_post()
        
    except TypeError as e:
        logger.error(f"Failed anylize_post due to TypeError: {e}")
    except Exception as e:
        logger.error(f"An error occurred in anylize_post: {e}")
    