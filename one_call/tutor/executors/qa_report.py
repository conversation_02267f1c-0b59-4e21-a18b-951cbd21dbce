import os
import time
import j<PERSON>
from typing import Dict, List

from tutor.modules.assistants.qa_assistant import QAAssistant
from tutor.modules.database import database
from tutor.modules.logger import logger, multiprocessing_logger
from tutor.modules.models import models

def qa_report_service() -> None:
    """Background service for generating QA reports from transcripts."""
    multiprocessing_logger(filename=os.path.join('logs', 'qa_report_%d-%m-%Y.log'))
    logger.info("Starting QA report service")
    
    try:
        # Initialize database connection
        db = database.Database(database=models.fileio.base_db)
        
        # Ensure QA tables exist
        db.create_qa_tables()
        
        # Ensure is_qa_processed column exists in Transcripts table
        db.add_column("Transcripts", "is_qa_processed", "INTEGER", default_value=0)
        db.add_column("Transcripts", "qa_report_id", "TEXT", default_value="")
        
        while True:
            # Get unprocessed transcripts
            unprocessed_transcripts = get_unprocessed_transcripts(db)
            
            if unprocessed_transcripts:
                logger.info(f"Found {len(unprocessed_transcripts)} unprocessed transcripts")
                
                for transcript_record in unprocessed_transcripts:
                    try:
                        call_id = transcript_record.get("call_id", "unknown")
                        logger.info(f"Processing QA for transcript with call_id: {call_id}")
                        
                        # Parse transcript data if it's a string
                        transcript = prepare_transcript_for_qa(transcript_record)
                        
                        # Process transcript with QA assistant
                        qa_assistant = QAAssistant(logger=logger, transcript=transcript, db=db)
                        qa_result = qa_assistant.analyze_call()
                        
                        # Get the report ID from the result
                        report_id = qa_result.get("report_id", "")
                        
                        # If report_id is missing, generate one
                        if not report_id:
                            from tutor.modules.utils import shared
                            shared.seq_number += 1
                            timestamp = time.strftime("%d%H%M%S")
                            report_id = f"QA{timestamp}{shared.seq_number:03}"
                            qa_result["report_id"] = report_id
                        
                        # Mark transcript as processed with the report ID
                        mark_transcript_processed(db, call_id, report_id)
                        
                        logger.info(f"Generated QA report for transcript {call_id} with ID: {report_id}, score: {qa_result['total_score']}")
                    except Exception as e:
                        logger.error(f"Error processing transcript {transcript_record.get('call_id', 'unknown')}: {str(e)}")
            
            # Sleep before next check
            time.sleep(10)  # Check every minute
            
    except Exception as e:
        logger.error(f"QA report service error: {str(e)}")
        return

def get_unprocessed_transcripts(db) -> List[Dict]:
    """Get transcripts that haven't been processed for QA yet."""
    query = "SELECT * FROM Transcripts WHERE is_qa_processed = 0 OR is_qa_processed IS NULL"
    
    with db.connection:
        cursor = db.connection.cursor()
        cursor.execute(query)
        columns = [column[0] for column in cursor.description]
        results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        return results

def mark_transcript_processed(db, call_id: str, qa_report_id: str) -> None:
    """Mark a transcript as processed for QA and store the report ID."""
    query = "UPDATE Transcripts SET is_qa_processed = 1, qa_report_id = ? WHERE call_id = ?"
    
    with db.connection:
        cursor = db.connection.cursor()
        cursor.execute(query, (qa_report_id, call_id))
        db.connection.commit()

def prepare_transcript_for_qa(transcript_record: Dict) -> Dict:
    """Prepare transcript data for QA processing."""
    transcript = {}
    
    # Copy call_id for reference
    transcript["call_id"] = transcript_record.get("call_id", "unknown")
    
    # Handle utterances - parse from JSON string if needed
    if "utterances" in transcript_record:
        if isinstance(transcript_record["utterances"], str):
            try:
                transcript["utterances"] = json.loads(transcript_record["utterances"])
            except json.JSONDecodeError:
                # If JSON parsing fails, create a single utterance with the full text
                transcript["utterances"] = [{
                    "speaker": "Unknown",
                    "text": transcript_record["utterances"]
                }]
        else:
            transcript["utterances"] = transcript_record["utterances"]
    elif "transcript_text" in transcript_record:
        # If no utterances but transcript_text exists, create a single utterance
        transcript["transcript_text"] = transcript_record["transcript_text"]
    else:
        # Create an empty transcript structure
        transcript["utterances"] = []
        transcript["transcript_text"] = ""
    
    return transcript
