import asyncio
import json
import time
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor
from threading import Lock
from typing import List, Optional, Dict, Any

import websockets
from tutor.executors.bot_manager import BotManager
from tutor.modules.audio.audio_processor import AudioProcessor  # Assuming you might use audio features for tutoring
from tutor.modules.logger import setup_logger
from tutor.modules.websocket import MediaEvent, MarkEvent, AudioCodec
# from tutor.modules.englishbot.bot_manager import BotManager  # This could handle tutor-bot interactions

class User:
    DISCONNECT_DELAY_CHAT = 600
    DISCONNECT_DELAY_CALL = 5

    def __init__(self, session: str, name: str, websocket, data: dict, event_loop, active_users: List['User']):
        self.session = session
        self.name = name
        self.conn = websocket
        self.mobile = data.get('mobile')
        self.user_id = data.get('userId')
        self.sentences = data.get('sentences', [])  # Assuming users send sentences to be corrected
        self.session_type = data.get('sessionType', 'chat')
        self.disconnect_delay = self._get_disconnect_delay()
        self.loop = event_loop
        self.is_end_session = False
        self.ai_start_tutoring = False
        self.ai_start_listening = False  # For protocol compatibility
        self.executor = ThreadPoolExecutor()
        self.active_users = active_users

        self.logger = setup_logger(session, self.mobile)
        self.realtime_transcription = None
        self.bot_manager = BotManager(self, self.logger, event_loop, data.get('target'))  # Manages AI tutor bot interactions
        self.audio_processor = AudioProcessor(self, self.logger)
        self._disconnect_task = None
        
        # Protocol-specific attributes
        self.stream_id: Optional[str] = None
        self.sequence_counter = 0
        self.chunk_counter = 0
        self.pending_marks: Dict[str, float] = {}  # mark_name -> timestamp
        self.media_stats = {'chunks_received': 0, 'last_timestamp': 0}
        self.audio_codec = AudioCodec()

    def __repr__(self):
        return (f"User(session={self.session}, name={self.name}, mobile={self.mobile}, "
                f"user_id={self.user_id}, sentences={self.sentences})")

    def to_dict(self):
        return {
            'session': self.session,
            'name': self.name,
            'mobile': self.mobile,
            'user_id': self.user_id,
            'sentences': self.sentences,
            'stream_id': self.stream_id,
            'status': 'active' if not self.is_end_session else 'ended'
        }
    def _get_disconnect_delay(self) -> int:
        """Determine disconnect delay based on session type."""
        return self.DISCONNECT_DELAY_CHAT if self.session_type == 'chat' else self.DISCONNECT_DELAY_CALL
    
    async def process_text(self, input: str):
        if input == "@@Hello":
            ai_response = self.bot_manager.start_new_session()
        else:
            # Get the AI response from the bot manager
            ai_response = await self.bot_manager.handle_user_input(input)
        # Properly format the JSON message with the actual AI response
        self.sync_send(json.dumps({"type": "ai_response", "data": f"{ai_response}"}))

    async def process_voice(self, input: str):
        input = self.audio_processor.get_speech_to_text()        
        # Properly format the JSON message with the actual AI response
        self.sync_send(json.dumps({"type": "speech_text", "data": f"{input}"}))

    async def send(self, message: str):
        """Send a message to the user."""
        try:
            if self.conn.open:
                await self.conn.send(message)
            else:
                await self.schedule_disconnect()
        except websockets.ConnectionClosed:
            self.logger.error("Failed to send message: Connection closed", exc_info=True)   
            await self.schedule_disconnect()        
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}", exc_info=True)
            await self.schedule_disconnect()

    def sync_send(self, message: str):
        """Send a message to the user synchronously."""
        asyncio.run_coroutine_threadsafe(self.send(message), self.loop)

    def start_ai_call(self):
        """Start AI call for the user."""
        self.logger.info("Starting AI call")
        self.audio_processor.start_ai_call()
    
    def start_ai_call_with_protocol(self):
        """Start AI call with protocol support"""
        self.logger.info("Starting AI call with protocol support")
        self.ai_start_listening = True
        self.audio_processor.start_ai_call()
        
        # Enable protocol mode in request handler when realtime transcription starts
        def enable_request_handler_protocol():
            if (hasattr(self, 'realtime_transcription') and 
                self.realtime_transcription and 
                hasattr(self.realtime_transcription, 'request_handler')):
                self.realtime_transcription.request_handler.enable_protocol_mode()
                self.logger.info("Protocol mode enabled for request handler")
        
        # Schedule enabling protocol mode after transcription is initialized
        self._run_async(asyncio.sleep(0.5))  # Small delay to ensure transcription is ready
        self.executor.submit(enable_request_handler_protocol)

    def end_ai_call(self):
        """End AI call for the user and save audio file."""
       
        if hasattr(self, 'is_end_call') and self.is_end_call:
            return
        self.logger.info("Ending AI call")
        self.is_end_call = True
        self._run_async(self.close())          
        self.audio_processor.save_audio_file()
        self._run_async(self.cleanup())

    def send_end_call(self):
        """Notify the user that the AI call has ended and close the connection."""
        if self.is_end_call:
            return
        self.logger.info("Sending end call notification to user")
        self.is_end_call = True
        self.sync_send(json.dumps({"type": "ai_end_call", "data": "ai call completed"}))
        self._run_async(self.close())
        self.audio_processor.save_audio_file()
        self._run_async(self.cleanup())

    async def close(self):
        try:
            await self.conn.close()
        except Exception as e:
            self.logger.error(f"Failed to close: {e}", exc_info=True)

    def _run_async(self, coroutine):
        """Run an asynchronous coroutine."""
        self.logger.info(f"Running asynchronous coroutine: {coroutine}")
        asyncio.run_coroutine_threadsafe(coroutine, self.loop)

    async def schedule_disconnect(self):
        """Schedule a task to disconnect the user after a delay."""
        if self._disconnect_task is None or self._disconnect_task.done():
            self.logger.info(f"Scheduling disconnect task for user {self.session}")
            self._disconnect_task = self.loop.create_task(self._disconnect_after_delay())

    async def _disconnect_after_delay(self):
        """Disconnect the user after a delay if the connection is still closed."""
        await asyncio.sleep(self.disconnect_delay)
        if not self.conn.open:
            self.logger.info(f"Disconnecting user {self.session} after delay")
            self.audio_processor.save_audio_file()
            await self.cleanup()

    async def cleanup(self):
        """Cleanup resources after the user session ends."""
        if self in self.active_users:
            self.logger.info("Cleaning up user resources")
            self.executor.shutdown(wait=False)
            handlers = self.logger.handlers[:]
            for handler in handlers:
                handler.close()
                self.logger.removeHandler(handler)
            try:
                self.active_users.remove(self)
                self.logger.info(f"User {self.session} removed from active users")
            except ValueError:
                self.logger.error(f"User {self.session} could not be removed from active users")
    
    # Protocol-specific methods
    def set_stream_id(self, stream_id: str) -> None:
        """Set the stream ID for protocol communication"""
        self.stream_id = stream_id
        self.logger.info(f"Set stream ID: {stream_id}")
    
    def update_media_stats(self, chunk: int, timestamp: int) -> None:
        """Update media statistics from received media events"""
        self.media_stats['chunks_received'] += 1
        self.media_stats['last_timestamp'] = timestamp
        self.chunk_counter = max(self.chunk_counter, chunk)
    
    def handle_mark_completion(self, mark_name: str) -> None:
        """Handle completion of a mark event (audio playback finished)"""
        if mark_name in self.pending_marks:
            duration = time.time() - self.pending_marks[mark_name]
            self.logger.info(f"Mark '{mark_name}' completed after {duration:.2f}s")
            del self.pending_marks[mark_name]
        else:
            self.logger.info(f"Mark '{mark_name}' completed (not tracked)")
    
    def clear_audio_buffer(self) -> None:
        """Clear audio buffer and send pending marks"""
        self.logger.info("Clearing audio buffer")
        
        # Clear the audio processor buffer if it has one
        if hasattr(self.audio_processor, 'clear_buffer'):
            self.audio_processor.clear_buffer()
        
        # Send all pending marks as completed
        for mark_name in list(self.pending_marks.keys()):
            self.handle_mark_completion(mark_name)
    
    def handle_dtmf_input(self, digit: str) -> None:
        """Handle DTMF (touch-tone) input"""
        self.logger.info(f"Received DTMF input: {digit}")
        
        # Process DTMF input through bot manager or audio processor
        if hasattr(self.bot_manager, 'handle_dtmf'):
            self.bot_manager.handle_dtmf(digit)
        else:
            # Default DTMF handling
            self.logger.info(f"DTMF digit {digit} received but no handler available")
    
    async def send_media_event(self, audio_data: bytes, mark_name: Optional[str] = None, is_protocol_format: bool = False) -> None:
        """Send media event with audio data to client
        
        Args:
            audio_data: Audio data to send
            mark_name: Optional mark name for synchronization
            is_protocol_format: True if audio_data is already in µ-law protocol format
        """
        if not self.stream_id:
            self.logger.warning("Cannot send media event: no stream ID")
            return
        
        try:
            # Convert audio to protocol format only if not already converted
            if is_protocol_format:
                protocol_audio = audio_data
                self.logger.debug("Using pre-converted protocol audio")
            else:
                protocol_audio = self.audio_codec.convert_audio_to_protocol_format(audio_data)
                self.logger.debug("Converting audio to protocol format")
            
            # Create chunks and send them
            chunks = self.audio_codec.create_chunks(protocol_audio, chunk_duration_ms=20)
            
            for i, chunk in enumerate(chunks):
                self.chunk_counter += 1
                timestamp = int(time.time() * 1000)  # milliseconds
                
                media_event = MediaEvent(
                    sequence_number=self.sequence_counter + 1,
                    stream_sid=self.stream_id,
                    chunk=self.chunk_counter,
                    timestamp=timestamp,
                    payload=chunk
                )
                
                await self.send(media_event.to_json())
                self.sequence_counter += 1
            
            # Send mark event if provided
            if mark_name:
                await self.send_mark_event(mark_name)
                
        except Exception as e:
            self.logger.error(f"Error sending media event: {e}")
    
    async def send_mark_event(self, mark_name: str) -> None:
        """Send mark event for audio synchronization"""
        if not self.stream_id:
            self.logger.warning("Cannot send mark event: no stream ID")
            return
        
        try:
            self.sequence_counter += 1
            mark_event = MarkEvent(self.sequence_counter, self.stream_id, mark_name)
            
            # Track the mark for completion handling
            self.pending_marks[mark_name] = time.time()
            
            await self.send(mark_event.to_json())
            self.logger.info(f"Sent mark event: {mark_name}")
            
        except Exception as e:
            self.logger.error(f"Error sending mark event: {e}")

