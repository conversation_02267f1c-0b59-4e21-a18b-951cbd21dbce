import os
from typing import List, Dict

import yaml
from pydantic import DirectoryPath
from pydub import AudioSegment

from tutor.modules.logger import logger
from tutor.modules.models import models


def get_gpt_data() -> List[Dict[str, str]]:
    """Get history from Jarvis -> ChatGPT conversation.

    Returns:
        List[Dict[str, str]]:
        A list of dictionaries with request and response key-value pairs.
    """
    try:
        with open(models.fileio.gpt_data) as file:
            return yaml.load(stream=file, Loader=yaml.FullLoader)
    except (yaml.YAMLError, FileNotFoundError) as error:
        logger.error(error)


def put_gpt_data(data: List[Dict[str, str]]) -> None:
    """Stores Jarvis -> ChatGPT conversations in a history file.

    Args:
        data: List of dictionaries that have to be saved for future reference.
    """
    with open(models.fileio.gpt_data, 'w') as file:
        yaml.dump(data=data, stream=file, indent=4, Dumper=yaml.Dumper)
        file.flush()  # Write buffer to file immediately


def save_yaml_to_session(mobile: str, session: str, filename: str, data: List[Dict[str, str]]) -> None:
    """
    Save data to a YAML file in the specified session directory.

    Args:
        session (str): The session ID.
        filename (str): Name of the file to save.
        data (List[Dict[str, str]]): Data to save in the file.
    """
    mobile_path: DirectoryPath = os.path.join(models.fileio.sessions, mobile)

    session_path: DirectoryPath = os.path.join(mobile_path, session)

    # Create the session directory if it does not exist
    if not os.path.isdir(session_path):
        os.makedirs(session_path, exist_ok=True)

    file_path = os.path.join(session_path, filename)
    with open(file_path, 'w') as file:
        yaml.dump(data, file, indent=4, Dumper=yaml.Dumper)
        file.flush()  # Ensure data is written to the file immediately


def load_yaml_from_session(mobile: str, session: str, filename: str) -> List[Dict[str, str]]:
    """
    Load data from a YAML file in the specified session directory.

    Args:
        mobile (str): The mobile .
        filename (str): Name of the file to load.

    Returns:
        List[Dict[str, str]]: Data loaded from the file.
    """
    try:
        mobile_path: DirectoryPath = os.path.join(models.fileio.sessions, mobile)

        session_path: DirectoryPath = os.path.join(mobile_path, session)

        # Create the session directory if it does not exist
        if not os.path.isdir(session_path):
            os.makedirs(session_path, exist_ok=True)

        file_path = os.path.join(session_path, filename)
        with open(file_path) as file:
            return yaml.load(file, Loader=yaml.FullLoader)
    except (yaml.YAMLError, FileNotFoundError) as error:
        # logger.error("Error loading YAML file: %s", error)
        return []


def save_audio_segment(data: bytes, mobile: str, session: str) -> None:
    try:
        mobile_path: DirectoryPath = os.path.join(models.fileio.sessions, mobile)
        session_path: DirectoryPath = os.path.join(mobile_path, session)

        audio_segment = AudioSegment(
            data=data,
            sample_width=2,  # 16-bit PCM
            frame_rate=16000,  # Sample rate
            channels=1  # Mono
        )

        audio_file_path = os.path.join(session_path, f"call_recording.wav")

        audio_segment.export(audio_file_path, format="wav")
        logger.info(f"Audio file saved: {audio_file_path}")
    except FileNotFoundError as error:
        logger.error("Error saving audio file: %s", error)
