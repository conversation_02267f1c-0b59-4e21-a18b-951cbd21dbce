import email
from typing import Union

import gmailconnector
import html2text
import jinja2
from pydantic import EmailStr

from tutor.executors import word_match
from tutor.modules.audio import listener
from tutor.modules.conditions import keywords
from tutor.modules.database import database
from tutor.modules.logger import logger
from tutor.modules.models import models
from tutor.modules.templates import templates
from tutor.modules.utils import shared, support


def read_gmail(*args) -> None:
    """Reads unread emails from the gmail account for which the credentials are stored in env variables."""
    if not all([models.env.gmail_user, models.env.gmail_pass]):
        logger.warning("Gmail username and password not found.")
        # support.no_env_vars()
        return

    # support.write_screen(text="Fetching unread emails..")
    reader = gmailconnector.ReadEmail(gmail_user=models.env.gmail_user, gmail_pass=models.env.gmail_pass)
    response = reader.instantiate()
    if response.ok:
        logger.info(f'You have {response.count} unread emails {models.env.title}.')
        db = database.Database(database=models.fileio.base_db)
        for mail in reader.read_mail(messages=response.body, humanize_datetime=True):
            body = extract_body_text(mail.body)
            # Fix: Add case_type parameter (using mail.subject as case_type and body as description)
            case_id = db.insert_support_ticket(mail.sender_email, mail.sender, "Email", mail.subject, body)
            logger.info(f"New email from {mail.sender}: Subject - '{mail.subject}' received on {mail.date_time}")
            
    elif response.status == 204:
        logger.info(f"No new emails to catch up on, {models.env.title}!")
    else:
        logger.error(f"Unable to read emails for {models.env.title}. Status code: {response.status}")


def send_sms(user: str, password: str, number: Union[str, int], body: str, subject: str = None) -> Union[bool, str]:
    """Send text message through SMS gateway of destination number.

    References:
        Uses `gmail-connector <https://pypi.org/project/gmail-connector/>`__ to send the SMS.

    Args:
        user: Gmail username to authenticate SMTP lib.
        password: Gmail password to authenticate SMTP lib.
        number: Phone number stored as env var.
        body: Content of the message.
        subject: Takes subject as an optional argument.

    Returns:
        Union[bool, str]:
        - Boolean flag to indicate the SMS was sent successfully.
        - Error response from gmail-connector.
    """
    if not any([models.env.phone_number, number]):
        logger.error('No phone number was stored in env vars to trigger a notification.')
        return False
    if not subject:
        subject = "Message from Jarvis" if number == models.env.phone_number else f"Message from {models.env.name}"
    sms_object = gmailconnector.SendSMS(gmail_user=user, gmail_pass=password)
    response = sms_object.send_sms(phone=number or models.env.phone_number,
                                   subject=subject, message=body, delete_sent=True)
    if response.ok:
        logger.info('SMS notification has been sent.')
        return True
    else:
        logger.error('Unable to send SMS notification.')
        return response.body


def send_email(body: str, recipient: Union[EmailStr, str], subject: str = None, sender: str = None,
               gmail_user: Union[EmailStr, str] = None, gmail_pass: str = None, title: str = None,
               attachment: str = None) -> Union[bool, str]:
    """Sends an email using an email template formatted as html.

    Args:
        body: Message to be inserted as html body in the email.
        sender: Sender name of the email.
        subject: Subject of the email.
        recipient: Email address to which the mail has to be sent.
        gmail_user: Username for email.
        gmail_pass: Password for email.
        title: Sender name on template.
        attachment: Attachment to include in notification.

    References:
        Uses `gmail-connector <https://pypi.org/project/gmail-connector/>`__ to send the Email.

    Returns:
        Union[bool, str]:
        - Boolean flag to indicate the email was sent successfully.
        - Error response from gmail-connector.
    """
    if not subject:
        subject = "Message from Customer Support" if recipient == models.env.recipient else f"Message from {models.env.name}"
    rendered = jinja2.Template(source=templates.email.notification).render(SENDER=title or models.env.name,
                                                                           MESSAGE=body)
    email_object = gmailconnector.SendEmail(gmail_user=gmail_user or models.env.gmail_user,
                                            gmail_pass=gmail_pass or models.env.gmail_pass)
    mail_stat = email_object.send_email(recipient=recipient, sender=sender or 'Customer Support Communicator',
                                        subject=subject, html_body=rendered, attachment=attachment)
    if mail_stat.ok:
        logger.info('Email notification has been sent')
        return True
    else:
        logger.error('Unable to send email notification.')
        logger.error(mail_stat.json())
        return mail_stat.body


def extract_body_text(raw_email_content):
    # Parse the email content
    msg = email.message_from_string(raw_email_content)
    body_text = ""

    # Iterate over each part to find the plain text or HTML part
    if msg.is_multipart():
        for part in msg.walk():
            content_type = part.get_content_type()
            content_disposition = str(part.get("Content-Disposition"))

            # Check for plain text part
            if content_type == "text/plain" and "attachment" not in content_disposition:
                body_text = part.get_payload(decode=True).decode(part.get_content_charset() or "utf-8")
                break

            # Check for HTML part if no plain text part is found
            elif content_type == "text/html" and "attachment" not in content_disposition and not body_text:
                html_content = part.get_payload(decode=True).decode(part.get_content_charset() or "utf-8")
                body_text = html2text.html2text(html_content)  # Convert HTML to plain text
    else:
        # If the email is not multipart, handle it as a single text or HTML part
        content_type = msg.get_content_type()
        if content_type == "text/plain":
            body_text = msg.get_payload(decode=True).decode(msg.get_content_charset() or "utf-8")
            body_text = html2text.html2text(body_text)
        elif content_type == "text/html":
            html_content = msg.get_payload(decode=True).decode(msg.get_content_charset() or "utf-8")
            body_text = html2text.html2text(html_content)

    # Clean up the text to remove excessive whitespace
    body_text = body_text.strip()
    return body_text
