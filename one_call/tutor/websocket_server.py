# websocket_server.py - Refactored for bi-directional audio streaming protocol

import asyncio
import json
import time
import uuid
from flask import Flask, jsonify
from flask_socketio import So<PERSON><PERSON>
from typing import Optional, Type, Dict, Any

import websockets
from websockets.exceptions import ConnectionClosedError
from tutor.executors.user import User
from tutor.modules.logger import custom_handler, logger
from tutor.modules.models import models
from tutor.modules.websocket import (
    EventType, ConnectedEvent, StartEvent, MediaEvent, StopEvent,
    DTMFEvent, MarkEvent, ClearEvent, EventFactory, StreamManager,
    AudioCodec
)


# Initialize logger
handler = custom_handler()
logger.info("Switching to %s", handler.baseFilename)
logger.addHandler(hdlr=handler)


# List to store active users and stream manager
active_users = []
stream_manager = StreamManager()
audio_codec = AudioCodec()

def find_user_by_session(session: str) -> Optional[User]:
    """Find a user by their session ID."""
    for user in active_users:
        if user.session == session:
            return user
    return None


def find_user_by_stream_id(stream_sid: str) -> Optional[User]:
    """Find a user by their stream ID."""
    for user in active_users:
        if hasattr(user, 'stream_id') and user.stream_id == stream_sid:
            return user
    return None


async def handle_protocol_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle incoming WebSocket events based on bi-directional streaming protocol"""
    try:
        event_type = event_data.get("event")
        
        if event_type == EventType.CONNECTED.value:
            await handle_connected_event(websocket, event_data)
        
        elif event_type == EventType.START.value:
            await handle_start_event(websocket, event_data)
        
        elif event_type == EventType.MEDIA.value:
            await handle_media_event(websocket, event_data)
        
        elif event_type == EventType.STOP.value:
            await handle_stop_event(websocket, event_data)
        
        elif event_type == EventType.MARK.value:
            await handle_mark_event(websocket, event_data)
        
        elif event_type == EventType.CLEAR.value:
            await handle_clear_event(websocket, event_data)
        
        elif event_type == EventType.DTMF.value:
            await handle_dtmf_event(websocket, event_data)
            
        else:
            logger.warning(f"Unknown protocol event type: {event_type}")
            
    except Exception as e:
        logger.error(f"Error handling protocol event: {e}")





async def handle_connected_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle connected event - respond with connected acknowledgment"""
    try:
        # Send connected response
        connected_event = ConnectedEvent()
        await websocket.send(connected_event.to_json())
        logger.info("Sent connected event response")
    except Exception as e:
        logger.error(f"Error handling connected event: {e}")


async def handle_start_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle start event from external streaming service"""
    try:
        stream_sid = event_data.get("streamSid")
        start_data = event_data.get("start", {})
        
        if not stream_sid:
            logger.warning("Start event missing streamSid")
            return
            
        # Extract call information
        call_sid = start_data.get("callSid", "")
        from_number = start_data.get("from", "")
        to_number = start_data.get("to", "")
        account_sid = start_data.get("accountSid", "")
        
        logger.info(f"Processing start event for stream {stream_sid}, call {call_sid}, from {from_number} to {to_number}")
        
        # Check if user already exists for this stream
        existing_user = find_user_by_stream_id(stream_sid)
        if existing_user:
            logger.info(f"User already exists for stream {stream_sid}")
            return
            
        # Create new user for external call
        user_id = f"external_{call_sid}"
        session_id = f"session_{stream_sid}"
        
        user = User(
            session=session_id,
            name=f"sir",
            websocket=websocket,
            data={
                "mobile": from_number,
                "userId": user_id,
                "sessionType": "external_call",
                "target": "assistant",
                "callSid": call_sid,
                "accountSid": account_sid,
                "to_number": to_number
            },
            event_loop=asyncio.get_event_loop(),
            active_users=active_users
        )
        
        # Set stream ID for the user
        user.set_stream_id(stream_sid)
        
        # Add to active users
        active_users.append(user)
        
        # Start AI call with protocol support
        user.start_ai_call_with_protocol()
        
        logger.info(f"Created new user for external call: {user.name} with stream {stream_sid}")
        
    except Exception as e:
        logger.error(f"Error handling start event: {e}")


async def handle_stop_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle stop event from external streaming service"""
    try:
        stream_sid = event_data.get("streamSid")
        stop_data = event_data.get("stop", {})
        
        if not stream_sid:
            logger.warning("Stop event missing streamSid")
            return
            
        # Extract stop information
        call_sid = stop_data.get("callSid", "")
        reason = stop_data.get("reason", "unknown")
        
        logger.info(f"Processing stop event for stream {stream_sid}, call {call_sid}, reason: {reason}")
        
        # Find user by stream ID
        user = find_user_by_stream_id(stream_sid)
        if not user:
            logger.warning(f"No user found for stop event with streamSid: {stream_sid}")
            return
            
        # End AI call
        user.end_ai_call()
        logger.info(f"Ended AI call for user {user.name}")
        
        # Remove user from active users
        try:
            active_users.remove(user)
            logger.info(f"Removed user {user.name} from active users")
        except ValueError as e:
            logger.error(f"Error removing user from active users: {e}")
            
        # Clean up user's stream ID
        if hasattr(user, 'stream_id'):
            delattr(user, 'stream_id')
            
        logger.info(f"Processed stop event for user {user.name}, call ended due to: {reason}")
        
    except Exception as e:
        logger.error(f"Error handling stop event: {e}")


async def handle_media_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle incoming media event with audio data"""
    try:
        stream_sid = event_data.get("streamSid")
        media = event_data.get("media", {})
        
        # Validate stream_sid
        if not stream_sid:
            logger.warning("Media event missing streamSid")
            return
            
        # Find user by stream ID
        user = find_user_by_stream_id(stream_sid)
        if not user:
            logger.warning(f"No user found for streamSid: {stream_sid}")
            return
            
        # Decode base64 audio payload
        payload_b64 = media.get("payload", "")
        if payload_b64:
            try:
                audio_data = audio_codec.decode_audio_from_base64(payload_b64)
                
                # Validate audio data
                if not audio_data:
                    logger.warning("Empty audio data received")
                    return
                    
                user.audio_processor.add_protocol_audio(audio_data)
                
                # Update chunk and timestamp tracking
                try:
                    chunk = int(media.get("chunk", 0))
                    timestamp = int(media.get("timestamp", 0))
                    user.update_media_stats(chunk, timestamp)
                except (ValueError, TypeError) as e:
                    logger.warning(f"Invalid chunk/timestamp format: {e}")
                
            except Exception as e:
                logger.error(f"Error processing media payload: {e}")
        else:
            logger.warning("Media event received without payload")
                
    except Exception as e:
        logger.error(f"Error handling media event: {e}")


async def handle_mark_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle mark event for audio synchronization"""
    try:
        stream_sid = event_data.get("streamSid")
        mark = event_data.get("mark", {})
        mark_name = mark.get("name", "")
        
        if not stream_sid:
            logger.warning("Mark event missing streamSid")
            return
            
        user = find_user_by_stream_id(stream_sid)
        if user:
            # Handle mark completion (audio playback finished)
            user.handle_mark_completion(mark_name)
            logger.info(f"Processed mark event: {mark_name} for stream {stream_sid}")
        else:
            logger.warning(f"No user found for mark event with streamSid: {stream_sid}")
            
    except Exception as e:
        logger.error(f"Error handling mark event: {e}")


async def handle_clear_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle clear event to interrupt audio buffer"""
    try:
        stream_sid = event_data.get("streamSid")
        
        if not stream_sid:
            logger.warning("Clear event missing streamSid")
            return
            
        user = find_user_by_stream_id(stream_sid)
        if user:
            # Clear audio buffer and send pending marks
            user.clear_audio_buffer()
            logger.info(f"Processed clear event for stream {stream_sid}")
        else:
            logger.warning(f"No user found for clear event with streamSid: {stream_sid}")
            
    except Exception as e:
        logger.error(f"Error handling clear event: {e}")


async def handle_dtmf_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle DTMF (touch-tone) event"""
    try:
        stream_sid = event_data.get("streamSid")
        dtmf = event_data.get("dtmf", {})
        digit = dtmf.get("digit", "")
        
        if not stream_sid:
            logger.warning("DTMF event missing streamSid")
            return
            
        user = find_user_by_stream_id(stream_sid)
        if user:
            # Process DTMF input
            user.handle_dtmf_input(digit)
            logger.info(f"Processed DTMF event: {digit} for stream {stream_sid}")
        else:
            logger.warning(f"No user found for DTMF event with streamSid: {stream_sid}")
            
    except Exception as e:
        logger.error(f"Error handling DTMF event: {e}")





async def start_protocol_stream(websocket, user) -> None:
    """Start a new protocol-compliant stream"""
    try:
        # Create new stream
        stream_sid = stream_manager.create_stream(
            from_number=user.mobile,
            to_number="SYSTEM",
            custom_parameters={
                "name": user.name,
                "session": user.session,
                "userId": user.user_id
            }
        )
        
        # Associate stream with user
        user.set_stream_id(stream_sid)
        
        # Get stream metadata and send start event
        metadata = stream_manager.get_stream_metadata(stream_sid)
        if metadata:
            sequence_num = stream_manager.get_next_sequence_number(stream_sid)
            start_event = StartEvent(sequence_num, metadata)
            await websocket.send(start_event.to_json())
            
            # Start AI call with protocol support
            user.start_ai_call_with_protocol()
            
            logger.info(f"Started protocol stream {stream_sid} for user {user.name}")
        else:
            logger.error(f"Failed to get metadata for stream {stream_sid}")
            
    except Exception as e:
        logger.error(f"Error starting protocol stream: {e}")


async def end_protocol_stream(websocket, user, reason: str) -> None:
    """End a protocol-compliant stream"""
    try:
        stream_sid = getattr(user, 'stream_id', None)
        if not stream_sid:
            logger.warning(f"No stream ID found for user {user.name}")
            return
            
        # Get stream metadata for stop event
        metadata = stream_manager.get_stream_metadata(stream_sid)
        if metadata:
            sequence_num = stream_manager.get_next_sequence_number(stream_sid)
            stop_event = StopEvent(
                sequence_num, stream_sid, 
                metadata.accountSid, metadata.callSid, reason
            )
            await websocket.send(stop_event.to_json())
            
        # End stream in manager
        stream_manager.end_stream(stream_sid, reason)
        
        # End AI call
        user.end_ai_call()
        logger.info(f"user name: {user.name} WebSocket connection closed")
        
        # Remove user from active users
        try:
            active_users.remove(user)
        except ValueError as e:
            logger.error(f"Error removing user: {e}")
            
        # Cleanup stream
        stream_manager.cleanup_stream(stream_sid)
        
    except Exception as e:
        logger.error(f"Error ending protocol stream: {e}")


async def handle_user_management_event(websocket, event_data: Dict[str, Any]) -> None:
    """Handle user management events (registration, call start/end)"""
    try:
        event_type = event_data.get("type")

        if event_type == "user_register":
            await handle_user_registration(websocket, event_data)

        elif event_type == "call_start":
            await handle_call_start(websocket, event_data)

        elif event_type == "call_end":
            await handle_call_end(websocket, event_data)

        else:
            logger.warning(f"Unknown user management event type: {event_type}")

    except Exception as e:
        logger.error(f"Error handling user management event: {e}")




async def handle_user_registration(websocket, event_data: Dict[str, Any]) -> None:
    """Handle user registration"""
    try:
        session_id = event_data.get("sessionId")
        if not session_id:
            raise ValueError("Missing sessionId")

        user_data = event_data.get("userData", {})

        # Create new user
        user = User(
            session=session_id,
            name=user_data.get("name", "Unknown"),
            websocket=websocket,
            data={
                "mobile": user_data.get("mobile"),
                "userId": user_data.get("userId"),
                "sessionType": user_data.get("sessionType", "call"),
                "target": user_data.get("target", "assistant")  # Default target for web users
            },
            event_loop=asyncio.get_event_loop(),
            active_users=active_users
        )

        # Add to active users
        active_users.append(user)

        logger.info(f"User registered: {user.name} ({session_id})")

        # Send registration confirmation
        await websocket.send(json.dumps({
            "type": "user_registered",
            "sessionId": session_id,
            "status": "success"
        }))

        logger.info(f"User {user.name} registered successfully with session {session_id}")

    except Exception as e:
        logger.error(f"Error handling user registration: {e}")
        await websocket.send(json.dumps({
            "type": "error",
            "message": "User registration failed"
        }))


async def handle_call_start(websocket, event_data: Dict[str, Any]) -> None:
    """Handle call start request"""
    try:
        session_id = event_data.get("sessionId")
        if not session_id:
            logger.warning("Missing sessionId in call start request")
            await websocket.send(json.dumps({
                "type": "error",
                "message": "Missing sessionId"
            }))
            return

        # Find user by session
        user = find_user_by_session(session_id)
        if not user:
            logger.warning(f"No user found for session: {session_id}")
            await websocket.send(json.dumps({
                "type": "error",
                "message": "User not found"
            }))
            return

        # Start protocol stream
        await start_protocol_stream(websocket, user)

    except Exception as e:
        logger.error(f"Error handling call start: {e}")


async def handle_call_end(websocket, event_data: Dict[str, Any]) -> None:
    """Handle call end request"""
    try:
        session_id = event_data.get("sessionId")
        if not session_id:
            logger.warning("Missing sessionId in call end request")
            return

        reason = event_data.get("reason", "user_ended")

        # Find user by session
        user = find_user_by_session(session_id)
        if not user:
            logger.warning(f"No user found for session: {session_id}")
            return

        # End protocol stream
        await end_protocol_stream(websocket, user, reason)

    except Exception as e:
        logger.error(f"Error handling call end: {e}")


async def on_request(websocket, path):
    """Handle incoming WebSocket requests with protocol support."""
    try:
        async for message in websocket:
            try:
                data = json.loads(message)

                # Handle protocol-compliant events
                if "event" in data:
                    if data.get("event") not in ["media"]:
                        logger.debug(f"Protocol event: {data}")
                    await handle_protocol_event(websocket, data)

                # Handle user management events
                elif "type" in data:
                    logger.debug(f"User management event: {data.get('type')}")
                    await handle_user_management_event(websocket, data)

                else:
                    # Unknown message format
                    logger.warning(f"Unknown message format received: {data}")
                    await websocket.send(json.dumps({"type": "error", "message": "Unknown message format"}))

            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error: {e}")
                await websocket.send(json.dumps({"type": "error", "message": "Invalid JSON format"}))
            except KeyError as e:
                logger.error(f"Missing key in data: {e}")
                await websocket.send(json.dumps({"type": "error", "message": "Missing key in data"}))
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                await websocket.send(json.dumps({"type": "error", "message": "Unexpected error occurred"}))
    except ConnectionClosedError as e:
        logger.warning(f"Connection closed: {e}")
    except Exception as e:
        logger.error(f"Error in WebSocket connection: {e}")


def websocket_server() -> None:
    # Start WebSocket server
    start_server = websockets.serve(on_request, "0.0.0.0", models.env.websocket_port)

    # Run the server
    print(f"Starting WebSocket server on port {models.env.websocket_port} with asyncio event loop")
    loop = asyncio.get_event_loop()
    loop.run_until_complete(start_server)
    loop.run_forever()


if __name__ == "__main__":
    websocket_server()