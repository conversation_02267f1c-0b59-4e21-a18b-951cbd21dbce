import re
import difflib


def replace_with_correct_word(user_input, correct_word, match_threshold=0.4):
    user_input = user_input.lower()  # Convert input to lowercase for case-insensitive matching
    correct_word = correct_word.lower()  # Convert correct word to lowercase

    # Refine the pattern to better match vehicle number formats
    pattern = r'\b(?:[a-z]{1,2}\s*\d{1,4}[a-z]{0,2}|\d{4,8}[a-z]{0,2}|\d{2,4}[a-z]{1,2}\d{1,4})\b'

    # Function to determine if a match should be replaced
    def should_replace(match):
        match_ratio = difflib.SequenceMatcher(None, match.lower(), correct_word).ratio()
        return match_ratio >= match_threshold

    # Replace matches in the user input
    def replace(match):
        return correct_word if should_replace(match) else match

    # Perform the replacement using regex and the replace function
    replaced_input = re.sub(pattern, lambda m: replace(m.group()), user_input)

    return replaced_input


def remove_duplicate_words(sentence, correct_word):
    words = sentence.split()  # Split sentence into words
    unique_words = []  # List to store unique words
    seen_words = set()  # Set to track seen words

    for word in words:
        if word == correct_word:
            if word not in seen_words:
                unique_words.append(word)
                seen_words.add(word)
        elif word not in seen_words:
            unique_words.append(word)
            seen_words.add(word)

    return ' '.join(unique_words)  # Join words back into a sentence


# Example usage
correct_word = "mp041234th"

input_sentences = [
    "Can you proceed? Vehicle AB 12 with precise amount of 100 rupees.",
    "Can you proceed? 1234 with precise amount of 100 rupees.",
    "Can you Vehicle AB 34 with a precise of 100 rupees.",
    "Vehicle AB 12 with precise amount of 100.",
    "Vehicle A 12 with precise amount of 100.",
    "Car number AB1234 is parked outside.",
    "The registration is AB1234.",
    "See the plate: AB  1234.",
    "He mentioned A123.",
    "You noted 12 AB right?",
    "The number is 1A2B3.",
    "Can you proceed? Vehicle AB 12 with precise amount of 100 rupees.",
    "Can you proceed? 1234 with precise amount of 100 rupees.",
    "Can you Vehicle AB 34 with a precise of 100 rupees.",
    "Vehicle AB 12 with precise amount of 100.",
    "Vehicle A 12 with precise amount of 100.",
    "Some more examples MH 09 F 787878 and MP 04 1234 TH should be matched.",
    "Random inputs like KA 51 MC 4084 and VSGGDHDH 897 need to be replaced.",
    "Mixed patterns such as 23 BH 8890 A and AB 1234 are also included."
]

for input_sentence in input_sentences:
    corrected_sentence = replace_with_correct_word(input_sentence, correct_word)
    corrected_sentence = remove_duplicate_words(corrected_sentence, correct_word)
    print(corrected_sentence)
