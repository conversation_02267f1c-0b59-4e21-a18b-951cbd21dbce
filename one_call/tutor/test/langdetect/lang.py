from langdetect import detect, DetectorFactory
from langdetect.lang_detect_exception import LangDetectException

# Setting seed for reproducibility
DetectorFactory.seed = 0


def detect_language_langdetect(text):
    try:
        return detect(text)
    except LangDetectException:
        return "unknown"


text = "The Mary vehicle Igdotine preset Karnahe."
detected_language = detect_language_langdetect(text)
print(f"text :{text}")
if detected_language != 'en':
    print("The sentence is not in English.")
else:
    print("The sentence is in English.")
