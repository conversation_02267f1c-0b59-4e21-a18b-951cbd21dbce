import re
import numpy as np

test_sentences = [
    "rs 99",
    "amount 99",
    "preset 66",
    "Idliketopresetmyvehicleabc123with5000 amount is 5000",
    "Pleasepresetvehiclexyz789with3000 payment",
    "Vehicle abc123 should be updated with 4500 amount",
    "Presetmyvehicleab1234with5600 as new value",
    "Update the vehicle abc1234 amount to 1234",
    "Vehicle number xyz789 should be charged 7890 dollars",
    "The new amount for vehicle xyz789 should be 9876",
    "Please update the amount for vehicle xyz789 to 5000",
    "Charge vehicle abc123 with 3210 as the new amount",
    "Idliketopresetmyvehicleab123with2000 dollars",
    "Vehicle xyz789 has a new amount of 789 dollars",
    "Update vehicle abc123 amount to 6000 immediately",
    "Please set the vehicle number abc123 to 1000",
    "The charge for vehicle abc123 should be 3333",
    "Preset the vehicle xyz789 with the amount 4444",
    "Idliketopresetmyvehicleabc123with1234 immediately",
    "The new balance for vehicle abc123 is 2000 dollars",
    "Update my vehicle xyz789 to have 5555 as the amount",
    "The amount for vehicle abc2 should now be 6666",
    "Idliketopresetmyvehiclexyz789with4321 amount is 4321"
]

vehicle_number_list = ["abc123", "xyz789"]

def levenshtein_distance(a, b):
    if len(a) < len(b):
        return levenshtein_distance(b, a)

    if len(b) == 0:
        return len(a)

    previous_row = np.arange(len(b) + 1)
    for i, c1 in enumerate(a):
        current_row = previous_row + 1
        current_row[1:] = np.minimum(
            current_row[1:], np.add(previous_row[:-1], [c2 != c1 for c2 in b])
        )
        current_row[1:] = np.minimum(
            current_row[1:], current_row[:-1] + 1
        )
        previous_row = current_row

    return previous_row[-1]


def similarity_ratio(a, b):
    distance = levenshtein_distance(a, b)
    return 1 - (distance / max(len(a), len(b)))


def get_amount_from_string(input_string, vehicle_number_list, threshold=0.6, amount_check_threshold=0.4):
    # Check for the best matching vehicle number first
    for vehicle_number in vehicle_number_list:
        max_ratio = 0
        best_match_start = -1
        for i in range(len(input_string) - len(vehicle_number) + 1):
            substring = input_string[i:i + len(vehicle_number)]
            ratio = similarity_ratio(substring, vehicle_number)
            if ratio > max_ratio:
                max_ratio = ratio
                best_match_start = i

        if max_ratio >= threshold:
            matched_vehicle_number = input_string[best_match_start:best_match_start + len(vehicle_number)]
            remaining_string = input_string.replace(matched_vehicle_number, "", 1)

            # Find all potential amounts in the remaining string
            amount_matches = re.findall(r'\d+', remaining_string)
            valid_amounts = []

            # Validate each amount
            for amount in amount_matches:
                is_valid_amount = True
                for vn in vehicle_number_list:
                    if similarity_ratio(amount, vn) >= amount_check_threshold:
                        is_valid_amount = False
                        break
                if is_valid_amount:
                    valid_amounts.append(int(amount))

            if valid_amounts:
                return valid_amounts[0]  # Return the first valid amount found

    return None


# Testing the sentences
results = []
for sentence in test_sentences:
    result = get_amount_from_string(sentence, vehicle_number_list)
    results.append(result)

print(results)
