# Importing the required libraries
from gtts import gTTS
from playsound import playsound
import os


# Function to convert text to speech
def text_to_speech(text, language='en', slow=False):
    # Create a gTTS object
    tts = gTTS(text=text, lang=language, slow=slow, tld='com.in')  # 'co.in' for Indian accent
    # Save the converted audio to a file
    audio_file = 'output.mp3'
    tts.save(audio_file)
    # Play the converted file
    playsound(audio_file)
    # Remove the audio file after playing
    os.remove(audio_file)


# Main function
if __name__ == "__main__":
    # Text to be converted to speech
    text = "Hello, this is a sample text to speech conversion with an Indian female voice."
    # Convert and play the text
    text_to_speech(text)
