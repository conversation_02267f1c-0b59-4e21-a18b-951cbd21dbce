import re
from difflib import <PERSON><PERSON><PERSON><PERSON><PERSON>


def is_similar(a, b, threshold=0.6):
    return SequenceMatcher(None, a, b).ratio() > threshold


def get_amount_from_string_1(input_string, vehicle_number_list):
    # Iterate through the vehicle number list to find a match with at least 60% similarity
    for vehicle_number in vehicle_number_list:
        if is_similar(vehicle_number, input_string) or True:
            # Remove the vehicle number from the string
            remaining_string = re.sub(vehicle_number, '', input_string)
            # Find the amount in the remaining string using regex
            amount_match = re.search(r'\d+', remaining_string)
            if amount_match:
                # Convert the matched amount to an integer
                amount = int(amount_match.group())
                return amount
            else:
                return None
    # If no vehicle number matches, return None
    return None


def similar(a, b):
    return SequenceMatcher(None, a, b).ratio()


def get_amount_from_string_2(input_string, vehicle_number_list):
    # Iterate through the vehicle number list to find a match with at least 60% similarity
    for vehicle_number in vehicle_number_list:
        match = re.search(vehicle_number, input_string)
        if match and similar(vehicle_number, match.group()) >= 0.6:
            # Remove the vehicle number from the string
            remaining_string = input_string.replace(vehicle_number, "")
            # Find the amount in the remaining string using regex
            amount_match = re.search(r'\d+', remaining_string)
            if amount_match:
                # Convert the matched amount to an integer
                amount = int(amount_match.group())
                return amount
            else:
                return None
    # If no vehicle number matches, return None
    return None


def similarity_ratio_1(a, b):
    return SequenceMatcher(None, a, b).ratio()


def get_amount_from_string_3(input_string, vehicle_number_list, threshold=0.6):
    # Iterate through the vehicle number list to find a match with at least 60% similarity
    for vehicle_number in vehicle_number_list:
        ratio = similarity_ratio(input_string, vehicle_number)
        if ratio >= threshold:
            # Find the best match substring in the input string
            match = SequenceMatcher(None, input_string, vehicle_number).find_longest_match(0, len(input_string), 0,
                                                                                           len(vehicle_number))
            if match.size > 0:
                matched_vehicle_number = input_string[match.a: match.a + match.size]
                # Remove the matched vehicle number from the string
                remaining_string = input_string.replace(matched_vehicle_number, "")
                # Find the amount in the remaining string using regex
                amount_match = re.search(r'\d+', remaining_string)
                if amount_match:
                    # Convert the matched amount to an integer
                    amount = int(amount_match.group())
                    return amount
                else:
                    return None
    # If no vehicle number matches sufficiently, return None
    return None


import numpy as np


def levenshtein_distance(a, b):
    if len(a) < len(b):
        return levenshtein_distance(b, a)

    if len(b) == 0:
        return len(a)

    previous_row = np.arange(len(b) + 1)
    for i, c1 in enumerate(a):
        current_row = previous_row + 1
        current_row[1:] = np.minimum(
            current_row[1:], np.add(previous_row[:-1], [c2 != c1 for c2 in b])
        )
        current_row[1:] = np.minimum(
            current_row[1:], current_row[:-1] + 1
        )
        previous_row = current_row

    return previous_row[-1]


def similarity_ratio(a, b):
    distance = levenshtein_distance(a, b)
    return 1 - (distance / max(len(a), len(b)))


def get_amount_from_string(input_string, vehicle_number_list, threshold=0.6, amount_check_threshold=0.4):
    for vehicle_number in vehicle_number_list:
        max_ratio = 0
        best_match_start = -1
        for i in range(len(input_string) - len(vehicle_number) + 1):
            substring = input_string[i:i + len(vehicle_number)]
            ratio = similarity_ratio(substring, vehicle_number)
            if ratio > max_ratio:
                max_ratio = ratio
                best_match_start = i

        if max_ratio >= threshold:
            matched_vehicle_number = input_string[best_match_start:best_match_start + len(vehicle_number)]
            remaining_string = input_string.replace(matched_vehicle_number, "", 1)
            amount_match = re.search(r'\d+', remaining_string)
            if amount_match:
                amount = amount_match.group()
                # Check if the amount is less than 40% similar to any vehicle number
                is_valid_amount = True
                for vn in vehicle_number_list:
                    if similarity_ratio(amount, vn) >= amount_check_threshold:
                        is_valid_amount = False
                        break
                if is_valid_amount:
                    return int(amount)
                else:
                    return None
            else:
                return None

    return None


# Example usage
vehicle_number_list = ["abc123", "xyz789"]

test_string1 = "Idliketopresetmyvehicleab123with5000 amount is 5000"
test_string2 = "Idliketopresetmyvehicle123with5123 amount is None"

print(get_amount_from_string(test_string1, vehicle_number_list))  # Output: 5000
print(get_amount_from_string(test_string2, vehicle_number_list))  # Output: 5123

test_string1 = "Idliketopresetmyvehicle ab c1 with 5000 amount is 5000"
test_string2 = "Idliketopresetmyvehiclebc123with5123 amount is None"
test_string3 = "Idliketopresetmyvehicle ab 13 with 5000 amount is 5000"  # Slightly different vehicle number

print(get_amount_from_string(test_string1, vehicle_number_list))  # Output: 5000
print(get_amount_from_string(test_string2, vehicle_number_list))  # Output: 5123
print(get_amount_from_string(test_string3, vehicle_number_list))  # Output: 5000 (similar enough to abc123)
