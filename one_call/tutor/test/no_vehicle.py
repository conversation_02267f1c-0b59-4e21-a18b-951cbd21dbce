import random
import re

from executors import static_responses


class YourClass:
    def __init__(self):
        self.vehicle_number_list = []

    def get_no_vehicle_message(self):
        messages = [
            "Your vehicle isn't tagged. Please visit the nearest petrol pump to get one. Thank you.",
            "Vehicle not tagged. Please go to the nearest gas station for a tag. Thank you.",
            "Please tag your vehicle at the nearest petrol station and call us again. Thank you.",
            "No tag detected. Visit the closest fuel station for a tag. Thank you.",
            "Tag required. Please get one at the nearest petrol pump. Thank you.",
            "Vehicle tag missing. Head to the nearest petrol station to obtain one. Thank you.",
            "Please visit the nearest gas station to tag your vehicle. Thank you.",
            "Your vehicle needs a tag. Visit the closest petrol pump. Thank you.",
            "Vehicle not tagged. Please get a tag at the nearest petrol station. Thank you.",
            "Please tag your vehicle at the nearest petrol station. Thank you."
        ]
        return random.choice(messages)

    def process_llm_answer(self, llm_answer):
        phrase = "output: completed"
        is_phrase = phrase in llm_answer
        new_llm_answer = None
        is_call_for_no_vehicle = False

        if is_phrase:
            try:
                if not self.vehicle_number_list:
                    try:
                        new_llm_answer = llm_answer.replace(phrase, "").replace("  ", " ").strip()
                        cleaned_llm_answer = re.sub(r'[^a-zA-Z0-9\s]', '', new_llm_answer)
                        if not cleaned_llm_answer:
                            is_call_for_no_vehicle = True
                            new_llm_answer = self.get_no_vehicle_message()
                    except ValueError:
                        is_call_for_no_vehicle = True
                        new_llm_answer = self.get_no_vehicle_message()
                    return False, None, is_phrase, None, is_call_for_no_vehicle, new_llm_answer
            except Exception:
                # Handle unexpected exceptions (optional)
                new_llm_answer = self.get_no_vehicle_message()  # Assuming this method exists

        return False, None, is_phrase, None, False, new_llm_answer


# Example usage:
"""
your_instance = YourClass()
result = your_instance.process_llm_answer("output: completed and some other text")
print(result)

result = your_instance.process_llm_answer("output: completed")
print(result)

result = your_instance.process_llm_answer("'output: completed'")
print(result)
"""

