import re
import numpy as np
from fuzzywuzzy import fuzz

test_sentences = [
    "rs 99",
    "amount 99",
    "preset 66",
    "23",
    "Idliketopresetmyvehicleabc123with5000 amount is 5000",
    "Pleasepresetvehiclexyz789with3000",
    "Vehicle abc123 should be updated with 4500 amount",
    "Presetmyvehicleab1234with5600 as new value",
    "Update the vehicle abc1234 amount to 1234",
    "Vehicle number xyz789 should be charged 7890 dollars",
    "The new amount for vehicle xyz789 should be 9876",
    "Please update the amount for vehicle xyz789 to 5000",
    "Charge vehicle abc123 with 3210 as the new amount",
    "Idliketopresetmyvehicleab123with2000 dollars",
    "Vehicle xyz789 has a new amount of 789 dollars",
    "Update vehicle abc123 amount to 6000 immediately",
    "Please set the vehicle number abc123 to 1000",
    "The charge for vehicle abc123 should be 3333",
    "Preset the vehicle xyz789 with the amount 4444",
    "Idliketopresetmyvehicleabc123with1234 immediately",
    "The new balance for vehicle abc123 is 2000 dollars",
    "Update my vehicle xyz789 to have 5555 as the amount",
    "The amount for vehicle abc2 should now be 6666",
    "Idliketopresetmyvehiclexyz789with4321 amount is 421"
]

vehicle_number_list = ["abc123", "12xyz789"]


def extract_amounts(string):
    # Find all sequences of digits in the string
    amounts = re.findall(r'\d+', string)
    # Convert the sequences from strings to integers
    amounts = [int(amount) for amount in amounts]
    return amounts


def partial_match_vehicle_number(transcript, vehicle_number_list):
    # Find vehicle numbers that contain the partial transcript
    matches = [vn for vn in vehicle_number_list if transcript in vn]
    return matches


def extract_preset_amount(transcript):
    # Example regex pattern for preset amount
    match = re.search(r'\b(\d+)\b', transcript)
    return int(match.group(0)) if match else None


def is_amount_in_vehicle_list(amount, vehicle_number_list, threshold=80):
    amount_str = str(amount)
    for vn in vehicle_number_list:
        if fuzz.partial_ratio(amount_str, vn) >= threshold:
            return True
    return False


def filter_amounts_not_in_vehicle_list(amounts, vehicle_number_list, threshold=80):
    unmatched_amounts = [amount for amount in amounts if
                         not is_amount_in_vehicle_list(amount, vehicle_number_list, threshold)]
    return unmatched_amounts


def process_sentence(sentence: str, vehicle_number_list):
    normalized_text_remove_space = normalize_vehicle_number(sentence)
    amounts = extract_amounts(normalized_text_remove_space)
    unmatched_amounts = filter_amounts_not_in_vehicle_list(amounts, vehicle_number_list)
    return select_correct_amount(sentence, unmatched_amounts)


def select_correct_amount(sentence, unmatched_amounts):
    if not unmatched_amounts:
        return None

    keywords = [
        "amount", "dollars", "rs", "rupees", "rupees", "preset", "charge", "balance", "set", "reset",
        "value", "cost", "price", "sum", "total", "fee", "expense", "payment",
        "deposit", "fund", "currency", "cash", "capital", "worth", "credit",
        "rate", "tariff", "figure", "quota", "valuation", "assessment",
        "estimation", "count", "number", "quantity", "figure", "update",
        "adjust", "revise", "amend", "modify", "alter", "change"
    ]

    keyword_positions = {keyword: sentence.lower().find(keyword) for keyword in keywords}

    # Sort unmatched amounts by relevance
    def relevance(amount):
        amount_str = str(amount)
        position_score = -sentence.lower().rfind(amount_str)  # prioritize later positions
        keyword_scores = [(sentence.lower().find(amount_str) - keyword_positions[keyword]) ** 2 for keyword in keywords
                          if keyword_positions[keyword] != -1]
        keyword_score = min(keyword_scores) if keyword_scores else float('inf')
        return position_score, keyword_score

    unmatched_amounts.sort(key=relevance)

    # Return the most relevant amount
    return unmatched_amounts[0]


def normalize_vehicle_number(normalized_text: str) -> str:
    return normalized_text.replace(" ", "").lower()


def levenshtein_distance(a, b):
    if len(a) < len(b):
        return levenshtein_distance(b, a)

    if len(b) == 0:
        return len(a)

    previous_row = np.arange(len(b) + 1)
    for i, c1 in enumerate(a):
        current_row = previous_row + 1
        current_row[1:] = np.minimum(
            current_row[1:], np.add(previous_row[:-1], [c2 != c1 for c2 in b])
        )
        current_row[1:] = np.minimum(
            current_row[1:], current_row[:-1] + 1
        )
        previous_row = current_row

    return previous_row[-1]


def similarity_ratio(a, b):
    distance = levenshtein_distance(a, b)
    return 1 - (distance / max(len(a), len(b)))


def get_amount_from_string(input_string, vehicle_number_list, threshold=0.6, amount_check_threshold=0.4):
    # Check for the best matching vehicle number first
    matched_vehicle_number = None
    for vehicle_number in vehicle_number_list:
        max_ratio = 0
        best_match_start = -1
        for i in range(len(input_string) - len(vehicle_number) + 1):
            substring = input_string[i:i + len(vehicle_number)]
            ratio = similarity_ratio(substring, vehicle_number)
            if ratio > max_ratio:
                max_ratio = ratio
                best_match_start = i

        if max_ratio >= threshold:
            matched_vehicle_number = input_string[best_match_start:best_match_start + len(vehicle_number)]
            break

    if matched_vehicle_number:
        # Remove matched vehicle number from input string
        remaining_string = input_string.replace(matched_vehicle_number, "", 1)
    else:
        remaining_string = input_string

    # Use regex to find potential amounts
    amount_matches = re.findall(r'\b\d+\b', remaining_string)
    valid_amounts = []

    # Validate each amount
    for amount in amount_matches:
        is_valid_amount = True
        for vn in vehicle_number_list:
            if similarity_ratio(amount, vn) >= amount_check_threshold:
                is_valid_amount = False
                break
        if is_valid_amount:
            valid_amounts.append(int(amount))

    # Filter out amounts that are in unwanted contexts
    filtered_amounts = []
    for amount in valid_amounts:
        # Convert amount to string to check its context in the original string
        amount_str = str(amount)
        if re.search(rf'\b{amount_str}\b', remaining_string):
            filtered_amounts.append(amount)

    if filtered_amounts:
        return filtered_amounts[0]  # Return the first valid amount found

    return None


# Testing the sentences
results = []
for sentence in test_sentences:
    result = get_amount_from_string(sentence, vehicle_number_list)
    results.append(result)

print(results)

results = []
for sentence in test_sentences:
    result = partial_match_vehicle_number(sentence, vehicle_number_list)
    if len(result) == 1:
        results.append(result[0])
    else:
        results.append("")

print(results)

results = []
for sentence in test_sentences:
    amounts = process_sentence(sentence, vehicle_number_list)
    results.append(amounts)

print(results)
