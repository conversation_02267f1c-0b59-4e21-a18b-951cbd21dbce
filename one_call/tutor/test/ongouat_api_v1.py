import requests
import json

url = "https://ongouat.agsindia.com:8443/fservices/api/reqas/postdata"

payload = json.dumps({
  "data": "{\r\n  \"MOBILENO\": \"9595036832\",\r\n  \"VEHICLENO\": \"\",\r\n  \"PRESETAMOUNT\": \"240\",\r\n  \"PRESETQTY\": \"-1\",\r\n  \"FIXEDPRESET\": \"\",\r\n  \"SwitchBypass\": true,\r\n  \"userid\": \"160171\",\r\n  \"RequestTime\": \"\",\r\n  \"lat\": \"\",\r\n  \"lng\": \"\",\r\n  \"COUNTRYID\": 10003,\r\n  \"foruserid\": \"160171\",\r\n  \"Org_Id\": 8\r\n  \r\n}",
  "zone": "Vehicle"
})
headers = {
  'APIKEY': 'VEHICLE100000000001',
  'ACTION': 'Vehicle-Preset',
  'uniqueid': 'VEHICLE00001',
  'utype': 'S',
  'TOKEN': '160171',
  'Content-Type': 'application/json'
}

response = requests.request("POST", url, headers=headers, data=payload)

print(response.text)
