import re
from difflib import Sequence<PERSON>atcher

# Predefined list of vehicle numbers
vehicle_number_list = ["AB1234", "XYZ789"]


def clean_sentence(sentence):
    # Remove unnecessary symbols
    cleaned_sentence = re.sub(r'[^\w\s]', '', sentence)
    return cleaned_sentence


def find_potential_vehicle_numbers(sentence):
    # Regular expression pattern for vehicle numbers (simple example, can be modified)
    pattern = r'\b[A-Z]{1,2}\d{1,4}\b'
    return re.findall(pattern, sentence)


def get_closest_vehicle_number(potential_number, vehicle_list):
    # Find the closest match based on character similarity
    highest_ratio = 0
    best_match = potential_number
    for vehicle in vehicle_list:
        ratio = SequenceMatcher(None, potential_number, vehicle).ratio()
        if ratio > highest_ratio:
            highest_ratio = ratio
            best_match = vehicle
    return best_match


def correct_sentence(sentence, vehicle_list):
    cleaned_sentence = clean_sentence(sentence)
    potential_numbers = find_potential_vehicle_numbers(cleaned_sentence)

    for potential_number in potential_numbers:
        closest_match = get_closest_vehicle_number(potential_number, vehicle_list)
        sentence = re.sub(r'\b{}\b'.format(re.escape(potential_number)), closest_match, sentence)

    return sentence


# Example usage
input_sentences = [
    "Can you proceed? Vehicle 1234 with precise amount of RS100 rupees.",
    "Can you proceed? 1234 with precise amount of 100 rupees.",
    "Can you Vehicle AB. 34 with a precise of 100 rupees.",
    "Vehicle AB 12 with precise amount of 100 ."
]

for sentence in input_sentences:
    corrected_sentence = correct_sentence(sentence, vehicle_number_list)
    print(corrected_sentence)
