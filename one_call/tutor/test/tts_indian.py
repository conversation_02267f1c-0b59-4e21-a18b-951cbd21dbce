from gtts import gTTS
import os

# Text to be converted to speech
text = "Hello! This is an example of text-to-speech conversion with an Indian accent."

# Language in which you want to convert
language = 'en-IN'

# Passing the text and language to the engine
speech = gTTS(text=text, lang=language, slow=False)

# Saving the converted audio in a mp3 file named
speech.save("tts_example.mp3")

# Playing the converted file
os.system("mpg321 tts_example.mp3")
