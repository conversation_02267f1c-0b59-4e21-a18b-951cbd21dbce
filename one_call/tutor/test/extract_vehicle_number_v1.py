import re
from fuzzywuzzy import process

from modules.text.word_to_number import words_to_digits


def normalize_vehicle_number(vehicle_number: str) -> str:
    return vehicle_number.replace(" ", "").lower()


def remove_symbols(text):
    # Use regular expression to remove all non-alphanumeric characters
    cleaned_text = re.sub(r'[^a-zA-Z0-9]', '', text)
    return cleaned_text


def extract_numbers(text):
    # Use regular expression to find all sequences of digits in the text
    numbers = re.findall(r'\d+', text)
    return numbers


def extract_vehicle_number(text: str, vehicle_number_list) -> str:
    normalized_text = remove_symbols(text.lower())
    normalized_text_remove_space = normalize_vehicle_number(normalized_text)
    numbers = extract_numbers(normalized_text_remove_space)
    vehicles = []

    for number in numbers:
        for vehicle in vehicle_number_list:
            if number in vehicle:
                vehicles.append(vehicle)

    vehicle_number = None

    if len(vehicles) == 1:
        vehicle_number = vehicles[0]

    if not vehicle_number:
        best_match, ratio = process.extractOne(normalized_text_remove_space, vehicle_number_list)

        if ratio >= 40:
            vehicle_number = best_match

    return vehicle_number


test_cases = [
    "okay",
    "Okay DS T 897 And Preset amount is 100",
    "Can you press it? Vehicle 1234.With preset amount of hundred rupees or chakra.",
    "preset amount of hundred rupees",
    "My vehicle number is 56 dhd two thousand three hundred forty five.",
    "I have one hundred and twenty five apples.",
    "She earned five thousand six hundred seventy eight dollars last month.",
    "The distance is three hundred miles.",
    "There are forty two students in the class.",
    "dh dh one two three four",
    "My vehicle number is fifty six dhd two thousand three hundred forty five.",
    "I have one hundred and twenty-five apples.",
    "She earned five thousand six hundred seventy eight dollars last month.",
    "The distance is three hundred miles.",
    "There are forty two students in the class.",
    "dh dh ten twenty one",
    "eleven",
    "twenty"
]

vehicle_number_list = ['ka5656', 'ab1234', 'bc4576', 'dhdh897']
for sentence in test_cases:
    phrase = sentence.lower()
    phrase = words_to_digits(phrase)
    print(phrase)
    vehicle_number = extract_vehicle_number(phrase, vehicle_number_list)
    print(vehicle_number)
