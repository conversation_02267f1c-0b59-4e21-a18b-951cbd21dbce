import json
import threading
import time
import aiohttp
import asyncio

import httpx


class ChatAPI:
    def __init__(self, api_key, model, conversation_history):
        self.api_key = api_key
        self.model = model
        self.conversation_history = conversation_history
        self.base_url = "https://api.openai.com/v1/chat/completions"
        self.client = httpx.AsyncClient()

    async def get_chat_response(self):
        """Gets the chat response from the OpenAI API and prints the response time asynchronously."""
        start_time = time.time()

        url = "https://api.openai.com/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "messages": self.conversation_history,
            "model": self.model,
            "temperature": 0.3,
            "max_tokens": 50,
            "top_p": 1,
            "frequency_penalty": 0,
            "presence_penalty": 0
        }

        print(payload)
        try:
            start_time = time.time()
            response = await self.client.post(url, headers=headers, json=payload)
            response.raise_for_status()  # Raise an error for bad status codes
            end_time = time.time()

            response_time = end_time - start_time
            print(f"Response time for get_chat_response: {response_time:.2f} seconds")

            response_data = response.json()
            print(f"response_data {response_data}")
            return response_data
        except (httpx.RequestError, json.JSONDecodeError, KeyError) as e:
            print(f"Error during request: {e}")
            return None


# Example usage:
def start_event_loop(loop):
    """Start an event loop in a new thread."""
    asyncio.set_event_loop(loop)
    loop.run_forever()


# Set up the event loop in a separate thread
new_loop = asyncio.new_event_loop()
thread = threading.Thread(target=start_event_loop, args=(new_loop,), daemon=True)
thread.start()

# Example usage:
api_key = "********************************************************"
model = "gpt-3.5-turbo"
conversation_history = [{"role": "system", "content": "You are a helpful assistant."},
                        {"role": "user", "content": "Tell me a joke."}]

chat_api = ChatAPI(api_key, model, conversation_history)

# Use run_coroutine_threadsafe to run the coroutine in the background event loop
future = asyncio.run_coroutine_threadsafe(chat_api.get_chat_response(), new_loop)

# Get the result from the future
response_data = future.result()
print(response_data)
