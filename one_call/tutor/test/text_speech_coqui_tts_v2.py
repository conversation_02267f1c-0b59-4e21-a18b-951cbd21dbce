import io
import os
from pathlib import Path

import pyaudio
import requests
from pydub import AudioSegment


def text_to_audio(text):
    audio_dir = Path(__file__).parent / "audio"
    audio_dir.mkdir(exist_ok=True)

    speech_file_path = audio_dir / f"{hash(text)}.wav"

    url = "http://localhost:5002//api/tts?text=These cards can be ordered online thru Ongo app, https://ongo.co.in/ and Ongo card can be bought at Ongo Authorized Agent network outlets.&speaker_id=p273&style_wav=&language_id=en"

    payload = {}
    headers = {
        'Cookie': '.Ks.Culture=c%3Den-US%7Cuic%3Den-US; .Ks.Customer=f938cc93-b16e-4aa9-9d28-e0be97f14354'
    }

    response = requests.request("GET", url, headers=headers, data=payload)

    # Save the generated speech to an MP3 file
    with open(speech_file_path, "wb") as f:
        f.write(response.content)

    # Load the newly saved audio file
    audio_segment = AudioSegment.from_file(speech_file_path, format="wav")

    # Convert the audio file to PCM format
    pcm_audio = audio_segment.set_frame_rate(44100).set_channels(1).set_sample_width(2)

    # Save PCM audio to a byte stream
    pcm_audio_stream = io.BytesIO()
    pcm_audio.export(pcm_audio_stream, format="raw")
    pcm_audio_stream.seek(0)

    return pcm_audio_stream


def play_audio_stream(pcm_audio_stream):
    # Initialize PyAudio
    p = pyaudio.PyAudio()

    # Open a stream
    stream = p.open(format=p.get_format_from_width(2),  # 16-bit audio
                    channels=1,  # Mono
                    rate=44100,  # 44.1 kHz
                    output=True)

    # Read and play audio stream in chunks
    chunk_size = 1024
    pcm_audio_stream.seek(0)
    data = pcm_audio_stream.read(chunk_size)
    while data:
        stream.write(data)
        data = pcm_audio_stream.read(chunk_size)

    # Stop and close the stream
    stream.stop_stream()
    stream.close()
    p.terminate()


# Example usage
pcm_audio_stream = text_to_audio("Today is a wonderful day to build something people love!")
play_audio_stream(pcm_audio_stream)
