import re


# Function to normalize numbers in words to digits
def text2int(textnum, numwords={}):
    if not numwords:
        units = [
            "zero", "one", "two", "three", "four", "five", "six", "seven",
            "eight", "nine", "ten", "eleven", "twelve", "thirteen", "fourteen",
            "fifteen", "sixteen", "seventeen", "eighteen", "nineteen",
        ]
        tens = ["", "", "twenty", "thirty", "forty", "fifty", "sixty", "seventy", "eighty", "ninety"]
        scales = ["hundred", "thousand", "million", "billion", "trillion"]

        numwords["and"] = (1, 0)
        for idx, word in enumerate(units): numwords[word] = (1, idx)
        for idx, word in enumerate(tens): numwords[word] = (1, idx * 10)
        for idx, word in enumerate(scales): numwords[word] = (10 ** (idx * 3 or 2), 0)

    textnum = textnum.replace('-', ' ')
    current = result = 0
    curstring = ""
    onnumber = False

    for word in textnum.split():
        if word not in numwords:
            if onnumber:
                curstring += repr(result + current) + " "
            curstring += word + " "
            result = current = 0
            onnumber = False
        else:
            scale, increment = numwords[word]
            current = current * scale + increment
            if scale > 100:
                result += current
                current = 0
            onnumber = True

    if onnumber:
        curstring += repr(result + current)

    return curstring


# Function to clean and correct sentence
def correct_sentence(input_text):
    # Remove non-alphanumeric characters except spaces
    clean_text = re.sub(r'[^A-Za-z0-9\s]', '', input_text)

    # Normalize numbers in words to digits
    clean_text = text2int(clean_text.lower())

    # Capitalize the first letter of the sentence
    corrected_sentence = clean_text.capitalize()

    return corrected_sentence


# Sample input
input_text = "100 rupees for vehicle.  vsggdhdh897, my present amount is 100..  seven."
corrected_output = correct_sentence(input_text)
print(corrected_output)
