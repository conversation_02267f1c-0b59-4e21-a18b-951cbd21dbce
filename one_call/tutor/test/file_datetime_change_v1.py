import os
import time

# Path to the file
file_path = 'web.config'

# Desired modified time in Unix timestamp format
# For example, let's set it to 1st January 2022, 12:00:00
new_time = time.mktime((2024, 1, 11, 12, 7, 58, 8, 0, 0))

# Get the current access time
current_access_time = os.path.getatime(file_path)

# Set the new access and modified times
os.utime(file_path, (current_access_time, new_time))

print(f"Modified time of '{file_path}' has been updated.")
