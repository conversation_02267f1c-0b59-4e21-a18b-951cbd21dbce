import re
from difflib import Se<PERSON><PERSON>atcher

from modules.preset_vehicle import vehicle_validator


def clean_sentence(sentence):
    """Remove unnecessary symbols from the sentence."""
    return re.sub(r'[^a-zA-Z0-9\s]', '', sentence)


def find_potential_vehicle_numbers(sentence, vehicle_number_list):
    """Find all potential vehicle number candidates in the sentence and match with vehicle number list."""
    # Refine regex to match typical vehicle number formats better
    potential_numbers = re.findall(r'\b[A-Za-z]{1,2}\s?\d{1,4}\b', sentence)

    def best_match(candidate, vehicle_number_list):
        """Find the best matching vehicle number from the list based on similarity."""
        candidate_cleaned = candidate.replace(" ", "")
        best_match = None
        highest_ratio = 0
        for vehicle_number in vehicle_number_list:
            ratio = SequenceMatcher(None, candidate_cleaned, vehicle_number).ratio()
            if ratio > highest_ratio:
                highest_ratio = ratio
                best_match = vehicle_number
        return best_match if highest_ratio > 0.6 else candidate_cleaned  # Threshold to ensure reasonable match

    matched_numbers = [best_match(num, vehicle_number_list) for num in potential_numbers]
    return matched_numbers


def best_match_vehicle_number(candidate, vehicle_number_list):
    """Find the best matching vehicle number from the list."""
    print("candidate:", candidate)
    candidate_cleaned = candidate.replace(" ", "")
    best_match = vehicle_validator.extract_vehicle_number(candidate_cleaned, vehicle_number_list)
    print("best_match:", best_match)
    if best_match:  # Assuming extract_vehicle_number returns None or empty string if no match found
        return best_match
    return candidate_cleaned  # Return the original candidate if no match is found


def correct_sentence(sentence, vehicle_number_list):
    """Correct the sentence based on the vehicle number list."""
    cleaned_sentence = clean_sentence(sentence)
    potential_numbers = find_potential_vehicle_numbers(cleaned_sentence, vehicle_number_list)

    for number in potential_numbers:
        if not re.match(r'^[A-Za-z]{1,2}\d{1,4}$', number.replace(" ", "")):
            continue  # Skip candidates that do not match the vehicle number format
        best_match = best_match_vehicle_number(number, vehicle_number_list)
        # Use a more comprehensive pattern to replace all variations
        sentence = re.sub(re.escape(number).replace("\\ ", "\s*"), best_match, sentence, flags=re.IGNORECASE)

    return sentence


# Example usage
vehicle_number_list = ["AB1234", "XYZ789"]

input_sentences = [
    "Can you proceed? Vehicle AB 12 with precise amount of 100 rupees.",
    "Can you proceed? 1234 with precise amount of 100 rupees.",
    "Can you Vehicle AB. 34 with a precise of 100 rupees.",
    "Vehicle AB 12 with precise amount of 100.",
]

for input_sentence in input_sentences:
    corrected_sentence = correct_sentence(input_sentence, vehicle_number_list)
    print(corrected_sentence)
