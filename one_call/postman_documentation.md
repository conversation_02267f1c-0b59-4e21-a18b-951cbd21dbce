# QA Results API Documentation

## GET /api/qa-results

Retrieves QA results from the database with optional filtering, pagination, parameter details, transcript data, and audio file URLs.

### Request

**Method:** GET

**URL:** `{{base_url}}/api/qa-results`

**Query Parameters:**

| Parameter               | Type    | Required | Description                                       |
|-------------------------|---------|----------|---------------------------------------------------|
| call_id                 | string  | No       | Filter results by specific call ID                |
| report_id               | string  | No       | Filter results by specific QA report ID           |
| limit                   | integer | No       | Maximum number of results (default: 10)           |
| offset                  | integer | No       | Number of results to skip (default: 0)            |
| include_parameter_details | boolean | No     | Include full parameter definitions (default: false)|
| include_transcript      | boolean | No       | Include full transcript text and utterances (default: false)|
| include_audio           | boolean | No       | Include audio file URLs for playback (default: false)|

### Examples

#### Get all QA results (paginated)
```
GET {{base_url}}/api/qa-results
```

#### Get QA results with audio URLs
```
GET {{base_url}}/api/qa-results?include_audio=true
```

#### Get complete QA results with all details
```
GET {{base_url}}/api/qa-results?include_parameter_details=true&include_transcript=true&include_audio=true
```

### Response

**Status Code:** 200 OK

**Content Type:** application/json

**Body (with audio data):**
```json
{
  "status": "success",
  "data": [
    {
      "report_id": "***************",
      "call_summary": "Customer called regarding billing issue with their account.",
      "total_score": 85,
      "call_category": "Good",
      "timestamp": **********,
      "agent_strengths": [
        "Clear communication",
        "Empathy",
        "Problem resolution"
      ],
      "agent_areas_for_improvement": [
        "Call opening",
        "Documentation"
      ],
      "call_id": "12345",
      "audio": {
        "url": "/api/audio/12345/call_recording.wav",
        "file_path": "/path/to/audio_storage/api_upload/12345/call_recording.wav",
        "duration": null
      },
      "parameter_scores": [
        {
          "parameter_id": 101,
          "score": 1.5,
          "comments": "Agent greeted customer professionally but didn't introduce themselves."
        }
      ]
    }
  ],
  "count": 1
}
```

## GET /api/audio/{call_id}/{filename}

Serves audio files for playback in the browser or media player.

### Request

**Method:** GET

**URL:** `{{base_url}}/api/audio/{call_id}/{filename}`

**URL Parameters:**

| Parameter | Type   | Required | Description                        |
|-----------|--------|----------|------------------------------------|
| call_id   | string | Yes      | The call ID of the recording       |
| filename  | string | Yes      | The filename of the audio recording|

### Example

```
GET {{base_url}}/api/audio/12345/call_recording.wav
```

### Response

**Status Code:** 200 OK

**Content Type:** audio/wav (or appropriate audio MIME type)

**Body:** Binary audio data

### Error Responses

**Status Code:** 404 Not Found

**Content Type:** application/json

**Body:**
```json
{
  "status": "error",
  "message": "Audio file not found"
}
```

**Status Code:** 500 Internal Server Error

**Content Type:** application/json

**Body:**
```json
{
  "status": "error",
  "message": "Failed to serve audio file: [error details]"
}
```
