#!/bin/bash

# One call - Start Script
# Launches both the Python backend (main.py) and React frontend application

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_SCRIPT="$SCRIPT_DIR/main.py"
FRONTEND_DIR="$SCRIPT_DIR/frontend"
PID_DIR="$SCRIPT_DIR/.pids"
BACKEND_PID_FILE="$PID_DIR/backend.pid"
FRONTEND_PID_FILE="$PID_DIR/frontend.pid"
LOG_DIR="$SCRIPT_DIR/logs"
BACKEND_LOG="$LOG_DIR/backend.log"
FRONTEND_LOG="$LOG_DIR/frontend.log"
PORTS_CONFIG="$SCRIPT_DIR/ports.conf"

# Default port values (can be overridden by configuration)
API_PORT=1801
WEBSOCKET_PORT=1802
FRONTEND_PORT=1800
BACKEND_PROXY_PORT=1803

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[One call]${NC} $1"
}

# Function to load port configuration
load_port_config() {
    if [[ -f "$PORTS_CONFIG" ]]; then
        print_status "Loading port configuration from $PORTS_CONFIG"
        # Source the configuration file to load port values
        while IFS='=' read -r key value || [[ -n "$key" ]]; do
            # Skip comments and empty lines
            [[ $key =~ ^[[:space:]]*# ]] && continue
            [[ -z "$key" ]] && continue
            
            # Remove any whitespace and export the variable
            key=$(echo "$key" | xargs)
            value=$(echo "$value" | xargs)
            
            if [[ -n "$key" && -n "$value" ]]; then
                case "$key" in
                    "API_PORT") API_PORT="$value" ;;
                    "WEBSOCKET_PORT") WEBSOCKET_PORT="$value" ;;
                    "FRONTEND_PORT") FRONTEND_PORT="$value" ;;
                    "BACKEND_PROXY_PORT") BACKEND_PROXY_PORT="$value" ;;
                esac
            fi
        done < "$PORTS_CONFIG"
        
        print_status "Port configuration loaded: API=$API_PORT, WS=$WEBSOCKET_PORT, Frontend=$FRONTEND_PORT"
    else
        print_warning "Port configuration file not found, using default values"
    fi
    
    # Also check environment variables (they take precedence)
    if [[ -n "$API_PORT_ENV" ]]; then API_PORT="$API_PORT_ENV"; fi
    if [[ -n "$WEBSOCKET_PORT_ENV" ]]; then WEBSOCKET_PORT="$WEBSOCKET_PORT_ENV"; fi
    if [[ -n "$FRONTEND_PORT_ENV" ]]; then FRONTEND_PORT="$FRONTEND_PORT_ENV"; fi
    if [[ -n "$BACKEND_PROXY_PORT_ENV" ]]; then BACKEND_PROXY_PORT="$BACKEND_PROXY_PORT_ENV"; fi
}

# Function to validate port configuration
validate_port_config() {
    local errors=0
    
    print_status "Validating port configuration..."
    
    # Check if all ports are different
    local ports=($API_PORT $WEBSOCKET_PORT $FRONTEND_PORT $BACKEND_PROXY_PORT)
    local unique_ports=($(printf "%s\n" "${ports[@]}" | sort -u))
    
    if [[ ${#ports[@]} -ne ${#unique_ports[@]} ]]; then
        print_error "Duplicate ports detected! Each service must use a different port."
        print_error "Current configuration: API=$API_PORT, WS=$WEBSOCKET_PORT, Frontend=$FRONTEND_PORT, Proxy=$BACKEND_PROXY_PORT"
        ((errors++))
    fi
    
    # Check if ports are in valid range (1024-65535 for non-root users)
    for port_name in "API" "WEBSOCKET" "FRONTEND" "BACKEND_PROXY"; do
        case $port_name in
            "API") port_value=$API_PORT ;;
            "WEBSOCKET") port_value=$WEBSOCKET_PORT ;;
            "FRONTEND") port_value=$FRONTEND_PORT ;;
            "BACKEND_PROXY") port_value=$BACKEND_PROXY_PORT ;;
        esac
        
        if [[ $port_value -lt 1024 || $port_value -gt 65535 ]]; then
            print_warning "$port_name port $port_value may require root privileges or is invalid"
        fi
        
        if [[ $port_value -lt 1 || $port_value -gt 65535 ]]; then
            print_error "$port_name port $port_value is not a valid port number (1-65535)"
            ((errors++))
        fi
    done
    
    # Check for common conflicting services
    local common_ports=(22 25 53 80 110 443 993 995 3306 5432 6379 11211 27017)
    for port in "${ports[@]}"; do
        for common_port in "${common_ports[@]}"; do
            if [[ $port -eq $common_port ]]; then
                print_warning "Port $port is commonly used by system services and may conflict"
            fi
        done
    done
    
    if [[ $errors -gt 0 ]]; then
        print_error "Port configuration validation failed with $errors error(s)"
        print_status "Please check your port configuration in $PORTS_CONFIG"
        exit 1
    else
        print_status "Port configuration validation passed"
    fi
}

# Function to check if a process is running
is_process_running() {
    local pid_file="$1"
    if [[ -f "$pid_file" ]]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            return 0
        else
            # Remove stale PID file
            rm -f "$pid_file"
            return 1
        fi
    fi
    return 1
}

# Function to create necessary directories
setup_directories() {
    print_status "Setting up directories..."
    mkdir -p "$PID_DIR" "$LOG_DIR"
}

# Function to check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 is not installed or not in PATH"
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed or not in PATH"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed or not in PATH"
        exit 1
    fi
    
    # Check if main.py exists
    if [[ ! -f "$BACKEND_SCRIPT" ]]; then
        print_error "Backend script not found: $BACKEND_SCRIPT"
        exit 1
    fi
    
    # Check if frontend directory exists
    if [[ ! -d "$FRONTEND_DIR" ]]; then
        print_error "Frontend directory not found: $FRONTEND_DIR"
        exit 1
    fi
    
    # Check if requirements.txt exists
    if [[ ! -f "$SCRIPT_DIR/requirements.txt" ]]; then
        print_error "Requirements file not found: $SCRIPT_DIR/requirements.txt"
        exit 1
    fi
    
    print_status "All dependencies found"
}

setup_python_venv() {
    print_status "Checking Python virtual environment..."
    
    cd "$SCRIPT_DIR"
    
    # Check if virtual environment exists
    if [[ ! -d ".venv" ]]; then
        print_warning "Virtual environment not found. Creating one..."
        if python3 -m venv .venv; then
            print_status "Virtual environment created successfully"
        else
            print_error "Failed to create virtual environment"
            exit 1
        fi
    fi
    
    # Check if requirements need to be installed/updated
    local requirements_file="requirements.txt"
    local venv_timestamp=".venv/.requirements-timestamp"
    
    if [[ ! -f "$venv_timestamp" ]] || [[ "$requirements_file" -nt "$venv_timestamp" ]]; then
        print_status "Installing/updating Python requirements..."
        if source .venv/bin/activate && pip install -r requirements.txt > /dev/null 2>&1; then
            touch "$venv_timestamp"
            print_status "Python requirements installed successfully"
            
            # Check if torch installation needs special handling
            if ! python -c "import torch" &> /dev/null; then
                print_status "Installing PyTorch with specific version..."
                if pip install torch~=2.3.1 --no-cache-dir > /dev/null 2>&1; then
                    print_status "PyTorch installed successfully"
                else
                    print_warning "PyTorch installation failed, continuing with existing setup"
                fi
            fi
        else
            print_error "Failed to install Python requirements"
            exit 1
        fi
    else
        print_status "Python requirements are up to date"
    fi
    
    # Verify virtual environment is working
    if source .venv/bin/activate && python -c "import sys; print('Python virtual environment ready')" > /dev/null 2>&1; then
        print_status "Virtual environment verified and ready"
    else
        print_error "Virtual environment verification failed"
        exit 1
    fi
}

# Function to install frontend dependencies if needed
install_frontend_deps() {
    print_status "Checking frontend dependencies..."
    
    cd "$FRONTEND_DIR"
    
    if [[ ! -d "node_modules" ]] || [[ ! -f "node_modules/.install-timestamp" ]] || [[ "package.json" -nt "node_modules/.install-timestamp" ]]; then
        print_status "Installing frontend dependencies..."
        if npm install; then
            touch "node_modules/.install-timestamp"
            print_status "Frontend dependencies installed successfully"
        else
            print_error "Failed to install frontend dependencies"
            exit 1
        fi
    else
        print_status "Frontend dependencies are up to date"
    fi
    
    cd "$SCRIPT_DIR"
}

# Function to check if services are already running
check_running_services() {
    local backend_running=false
    local frontend_running=false
    local port_conflicts=false
    
    # Check PID files first
    if is_process_running "$BACKEND_PID_FILE"; then
        backend_running=true
        print_warning "Backend is already running (PID: $(cat "$BACKEND_PID_FILE"))"
    fi
    
    if is_process_running "$FRONTEND_PID_FILE"; then
        frontend_running=true
        print_warning "Frontend is already running (PID: $(cat "$FRONTEND_PID_FILE"))"
    fi
    
    # Check for port conflicts even if no PID files exist
    if command -v lsof &> /dev/null; then
        # Check backend ports
        local backend_port_api=$(lsof -t -i:$API_PORT 2>/dev/null || true)
        local backend_port_ws=$(lsof -t -i:$WEBSOCKET_PORT 2>/dev/null || true)
        local frontend_port=$(lsof -t -i:$FRONTEND_PORT 2>/dev/null || true)
        
        if [[ -n "$backend_port_api" ]] || [[ -n "$backend_port_ws" ]]; then
            print_warning "Backend ports ($API_PORT, $WEBSOCKET_PORT) are already in use by processes: $backend_port_api $backend_port_ws"
            backend_running=true
            port_conflicts=true
        fi
        
        if [[ -n "$frontend_port" ]]; then
            print_warning "Frontend port ($FRONTEND_PORT) is already in use by process: $frontend_port"
            frontend_running=true
            port_conflicts=true
        fi
    fi
    
    if [[ "$backend_running" == true ]] && [[ "$frontend_running" == true ]]; then
        if [[ "$port_conflicts" == true ]]; then
            print_error "Services are already running on required ports. Use './stop.sh --force' to stop all related processes."
        else
            print_warning "Both services are already running. Use './stop.sh' to stop them first."
        fi
        show_running_status
        exit 0
    elif [[ "$backend_running" == true ]]; then
        print_warning "Backend is already running. Starting frontend only..."
    elif [[ "$frontend_running" == true ]]; then
        print_warning "Frontend is already running. Starting backend only..."
    fi
}

# Function to show status of running services
show_running_status() {
    echo ""
    print_header "Current Service Status:"
    
    # Check by PID files
    if is_process_running "$BACKEND_PID_FILE"; then
        print_status "✓ Backend running (PID: $(cat "$BACKEND_PID_FILE"))"
    fi
    
    if is_process_running "$FRONTEND_PID_FILE"; then
        print_status "✓ Frontend running (PID: $(cat "$FRONTEND_PID_FILE"))"
    fi
    
    # Check by ports
    if command -v lsof &> /dev/null; then
        local backend_port_api=$(lsof -t -i:$API_PORT 2>/dev/null || true)
        local backend_port_ws=$(lsof -t -i:$WEBSOCKET_PORT 2>/dev/null || true)
        local frontend_port=$(lsof -t -i:$FRONTEND_PORT 2>/dev/null || true)
        
        if [[ -n "$backend_port_api" ]]; then
            print_status "✓ Backend API server running on port $API_PORT (PID: $backend_port_api)"
        fi
        
        if [[ -n "$backend_port_ws" ]]; then
            print_status "✓ Backend WebSocket server running on port $WEBSOCKET_PORT (PID: $backend_port_ws)"
        fi
        
        if [[ -n "$frontend_port" ]]; then
            print_status "✓ Frontend running on port $FRONTEND_PORT (PID: $frontend_port)"
        fi
    fi
    
    echo ""
    print_status "To stop services: ./stop.sh"
    print_status "To force stop: ./stop.sh --force"
    echo ""
}

# Function to start the backend
start_backend() {
    # Check if backend is already running by port or PID
    if is_process_running "$BACKEND_PID_FILE"; then
        print_warning "Backend is already running (PID file exists)"
        return 0
    fi
    
    # Check for port conflicts
    if command -v lsof &> /dev/null; then
        local port_api=$(lsof -t -i:$API_PORT 2>/dev/null || true)
        local port_ws=$(lsof -t -i:$WEBSOCKET_PORT 2>/dev/null || true)
        
        if [[ -n "$port_api" ]] || [[ -n "$port_ws" ]]; then
            print_warning "Backend ports ($API_PORT, $WEBSOCKET_PORT) are already in use. Skipping backend start."
            return 0
        fi
    fi
    
    print_status "Starting Python backend..."
    
    # Change to script directory to ensure relative paths work
    cd "$SCRIPT_DIR"
    
    # Check if virtual environment exists
    if [[ -f ".venv/bin/activate" ]]; then
        print_status "Activating virtual environment..."
        # Start backend with virtual environment activated
        nohup bash -c "source .venv/bin/activate && python3 '$BACKEND_SCRIPT'" > "$BACKEND_LOG" 2>&1 &
    else
        print_warning "Virtual environment not found, using system Python..."
        # Start backend in background and capture PID
        nohup python3 "$BACKEND_SCRIPT" > "$BACKEND_LOG" 2>&1 &
    fi
    local backend_pid=$!
    
    # Save PID to file
    echo "$backend_pid" > "$BACKEND_PID_FILE"
    
    # Wait a moment and check if process is still running
    sleep 2
    if kill -0 "$backend_pid" 2>/dev/null; then
        print_status "Backend started successfully (PID: $backend_pid)"
        print_status "Backend logs: $BACKEND_LOG"
    else
        print_error "Backend failed to start. Check logs: $BACKEND_LOG"
        rm -f "$BACKEND_PID_FILE"
        exit 1
    fi
}

# Function to start the frontend
start_frontend() {
    # Check if frontend is already running by port or PID
    if is_process_running "$FRONTEND_PID_FILE"; then
        print_warning "Frontend is already running (PID file exists)"
        return 0
    fi
    
    # Check for port conflicts
    if command -v lsof &> /dev/null; then
        local port_frontend=$(lsof -t -i:$FRONTEND_PORT 2>/dev/null || true)
        
        if [[ -n "$port_frontend" ]]; then
            print_warning "Frontend port $FRONTEND_PORT is already in use. Skipping frontend start."
            return 0
        fi
    fi
    
    print_status "Starting React frontend..."
    
    cd "$FRONTEND_DIR"
    
    # Check if start script exists in package.json
    if npm run 2>/dev/null | grep -q "start"; then
        # Start frontend in background and capture PID
        nohup npm start > "$FRONTEND_LOG" 2>&1 &
        local frontend_pid=$!
        
        # Save PID to file
        echo "$frontend_pid" > "$FRONTEND_PID_FILE"
        
        # Wait a moment and check if process is still running
        sleep 3
        if kill -0 "$frontend_pid" 2>/dev/null; then
            print_status "Frontend started successfully (PID: $frontend_pid)"
            print_status "Frontend logs: $FRONTEND_LOG"
        else
            print_error "Frontend failed to start. Check logs: $FRONTEND_LOG"
            rm -f "$FRONTEND_PID_FILE"
            cd "$SCRIPT_DIR"
            exit 1
        fi
    else
        print_error "Frontend start script not found in package.json"
        cd "$SCRIPT_DIR"
        exit 1
    fi
    
    cd "$SCRIPT_DIR"
}

# Function to wait for services to be ready
wait_for_services() {
    print_status "Waiting for services to be ready..."
    
    local max_attempts=20
    local attempt=0
    
    while [[ $attempt -lt $max_attempts ]]; do
        local backend_ready=false
        local frontend_ready=false
        
        # Check if backend ports are active
        if command -v lsof &> /dev/null; then
            if lsof -i:$API_PORT &> /dev/null && lsof -i:$WEBSOCKET_PORT &> /dev/null; then
                backend_ready=true
            fi
        elif is_process_running "$BACKEND_PID_FILE"; then
            backend_ready=true
        fi
        
        # Check if frontend port is active
        if command -v lsof &> /dev/null; then
            if lsof -i:$FRONTEND_PORT &> /dev/null; then
                frontend_ready=true
            fi
        elif is_process_running "$FRONTEND_PID_FILE"; then
            frontend_ready=true
        fi
        
        if [[ "$backend_ready" == true ]] && [[ "$frontend_ready" == true ]]; then
            print_status "All services are ready!"
            return 0
        fi
        
        ((attempt++))
        echo -n "."
        sleep 2
    done
    
    echo ""
    print_warning "Services may still be starting up. Check logs if needed."
}

# Function to display service status
show_status() {
    echo ""
    print_header "Service Status:"
    
    if is_process_running "$BACKEND_PID_FILE"; then
        print_status "✓ Backend running (PID: $(cat "$BACKEND_PID_FILE"))"
        print_status "  Backend API URL: http://localhost:$API_PORT"
        print_status "  Backend WebSocket URL: ws://localhost:$WEBSOCKET_PORT"
        print_status "  Backend logs: $BACKEND_LOG"
    else
        print_error "✗ Backend not running"
    fi
    
    if is_process_running "$FRONTEND_PID_FILE"; then
        print_status "✓ Frontend running (PID: $(cat "$FRONTEND_PID_FILE"))"
        print_status "  Frontend URL: http://localhost:$FRONTEND_PORT"
        print_status "  Frontend logs: $FRONTEND_LOG"
    else
        print_error "✗ Frontend not running"
    fi
    
    echo ""
    print_header "Commands:"
    print_status "Stop services: ./stop.sh"
    print_status "View backend logs: tail -f $BACKEND_LOG"
    print_status "View frontend logs: tail -f $FRONTEND_LOG"
    echo ""
}

# Main execution
main() {
    print_header "Starting One call Application"
    
    # Load port configuration
    load_port_config
    
    # Validate port configuration
    validate_port_config
    
    # Setup and checks
    setup_directories
    check_dependencies
    setup_python_venv
    check_running_services
    install_frontend_deps
    
    # Start services
    start_backend
    start_frontend
    
    # Wait for services to be ready
    wait_for_services
    
    # Show final status
    show_status
    
    print_header "One call started successfully!"
    print_status "Access the application at: http://localhost:$FRONTEND_PORT"
}

# Trap to handle script interruption
trap 'print_error "Script interrupted. You may need to run stop.sh to clean up."; exit 1' INT TERM

# Run main function
main "$@"